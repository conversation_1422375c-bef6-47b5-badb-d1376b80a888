<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - Entity Relationship Diagram</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .legend {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .legend h3 {
            margin-top: 0;
            color: #374151;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
        }
        .print-note {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Entity Relationship Diagram</h1>
            <p>TryMeOut Vulnerability Platform - Database Schema</p>
        </div>
        
        <div class="content">
            <div class="print-note">
                <strong>📋 Report Usage:</strong> This ERD shows the complete database structure of the TryMeOut platform, including all entities, attributes, and relationships. Perfect for technical documentation and system analysis.
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
erDiagram
    %% Core User Management
    USERS {
        int id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        enum role
        varchar profile_picture
        timestamp last_login
        boolean is_verified
        timestamp created_at
        timestamp updated_at
    }
    
    %% Authentication & Security
    OTP_VERIFICATIONS {
        int id PK
        int user_id FK
        varchar otp_code
        varchar email
        varchar type
        datetime expires_at
        timestamp created_at
    }
    
    ACCOUNT_LOCKOUTS {
        int id PK
        varchar email
        timestamp locked_at
        timestamp unlock_at
        int attempt_count
        varchar ip_address
    }
    
    BRUTE_FORCE_ATTEMPTS {
        int id PK
        varchar email
        varchar ip_address
        timestamp attempt_time
        text user_agent
    }
    
    %% Challenge System
    CATEGORIES {
        int id PK
        varchar name UK
        text description
        varchar icon
        varchar color
        int sort_order
        timestamp created_at
    }
    
    SUBCATEGORIES {
        int id PK
        int category_id FK
        varchar name
        text description
        int sort_order
        timestamp created_at
    }
    
    CHALLENGES {
        int id PK
        int level
        varchar title
        text description
        text hint
        varchar solution
        varchar difficulty
        varchar category
        int category_id FK
        int subcategory_id FK
    }
    
    USER_PROGRESS {
        int id PK
        int user_id FK
        int challenge_id FK
        enum status
        timestamp completed_at
        timestamp created_at
    }
    
    %% Gamification System
    BADGES {
        int id PK
        varchar name
        text description
        varchar icon
        varchar color
        int category_id FK
        varchar requirement_type
        int requirement_value
        timestamp created_at
    }
    
    USER_BADGES {
        int id PK
        int user_id FK
        int badge_id FK
        timestamp earned_at
    }
    
    USER_CERTIFICATES {
        int id PK
        int user_id FK
        int category_id FK
        varchar certificate_code UK
        timestamp issued_at
    }
    
    %% Logging & Monitoring
    SECURITY_LOGS {
        int id PK
        enum event_type
        int user_id FK
        varchar username
        varchar ip_address
        text user_agent
        json details
        enum risk_level
        varchar country
        varchar city
        varchar session_id
        timestamp created_at
    }
    
    AUDIT_LOGS {
        int id PK
        varchar action_type
        varchar table_name
        int record_id
        varchar username
        json old_values
        json new_values
        varchar ip_address
        text user_agent
        timestamp created_at
    }
    
    ACCESS_LOGS {
        int id PK
        int user_id FK
        varchar username
        varchar ip_address
        varchar country
        varchar city
        text user_agent
        varchar request_method
        text request_uri
        text file_path
        text directory_path
        enum access_type
        int response_code
        int response_size
        text referer
        decimal execution_time
        boolean blocked
        varchar block_reason
        enum risk_level
        json details
        timestamp created_at
    }
    
    SYSTEM_LOGS {
        int id PK
        enum log_level
        varchar category
        text message
        json details
        varchar ip_address
        timestamp created_at
    }
    
    ANOMALY_LOGS {
        int id PK
        varchar anomaly_type
        int user_id FK
        varchar ip_address
        enum severity
        text description
        json data
        boolean resolved
        timestamp created_at
    }
    
    LOGIN_ATTEMPTS {
        int id PK
        varchar email
        int user_id FK
        varchar ip_address
        varchar country
        varchar city
        text user_agent
        boolean success
        varchar failure_reason
        timestamp created_at
    }
    
    %% Relationships
    USERS ||--o{ OTP_VERIFICATIONS : "has"
    USERS ||--o{ USER_PROGRESS : "tracks"
    USERS ||--o{ USER_BADGES : "earns"
    USERS ||--o{ USER_CERTIFICATES : "receives"
    USERS ||--o{ SECURITY_LOGS : "generates"
    USERS ||--o{ ACCESS_LOGS : "creates"
    USERS ||--o{ ANOMALY_LOGS : "triggers"
    USERS ||--o{ LOGIN_ATTEMPTS : "attempts"
    
    CATEGORIES ||--o{ SUBCATEGORIES : "contains"
    CATEGORIES ||--o{ CHALLENGES : "groups"
    CATEGORIES ||--o{ BADGES : "awards"
    CATEGORIES ||--o{ USER_CERTIFICATES : "certifies"
    
    SUBCATEGORIES ||--o{ CHALLENGES : "organizes"
    
    CHALLENGES ||--o{ USER_PROGRESS : "tracked_in"
    
    BADGES ||--o{ USER_BADGES : "awarded_as"
                </div>
            </div>
            
            <div class="legend">
                <h3>📊 Database Schema Legend</h3>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3b82f6;"></div>
                    <span><strong>Primary Key (PK):</strong> Unique identifier for each record</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #10b981;"></div>
                    <span><strong>Foreign Key (FK):</strong> References another table's primary key</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f59e0b;"></div>
                    <span><strong>Unique Key (UK):</strong> Ensures unique values across the table</span>
                </div>
                
                <h4>🏗️ Table Categories:</h4>
                <ul>
                    <li><strong>Core User Management:</strong> users, otp_verifications, account_lockouts</li>
                    <li><strong>Challenge System:</strong> categories, subcategories, challenges, user_progress</li>
                    <li><strong>Gamification:</strong> badges, user_badges, user_certificates</li>
                    <li><strong>Security & Logging:</strong> security_logs, audit_logs, access_logs, system_logs</li>
                    <li><strong>Authentication:</strong> brute_force_attempts, login_attempts</li>
                </ul>
                
                <h4>🔗 Key Relationships:</h4>
                <ul>
                    <li><strong>One-to-Many:</strong> Users can have multiple progress records, badges, certificates</li>
                    <li><strong>Many-to-Many:</strong> Users and Challenges (through user_progress)</li>
                    <li><strong>Hierarchical:</strong> Categories → Subcategories → Challenges</li>
                    <li><strong>Audit Trail:</strong> All user actions logged across multiple log tables</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            },
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#374151',
                fill: '#f9fafb',
                fontSize: 12
            }
        });
    </script>
</body>
</html>
