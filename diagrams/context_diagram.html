<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - Context Diagram</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .description {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .description h3 {
            margin-top: 0;
            color: #374151;
        }
        .external-entities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .entity-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .entity-card h4 {
            margin-top: 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .entity-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .print-note {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Context Diagram</h1>
            <p>TryMeOut Vulnerability Platform - System Boundary & External Entities</p>
        </div>
        
        <div class="content">
            <div class="print-note">
                <strong>📋 Report Usage:</strong> This context diagram shows the TryMeOut platform as a single system and its interactions with external entities. It provides a high-level view of system boundaries and data flows.
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
graph TB
    %% External Entities
    Students[👨‍🎓 Students<br/>Security Learners]
    Instructors[👨‍🏫 Instructors<br/>Educators]
    Admins[👨‍💼 System Administrators<br/>Platform Managers]
    EmailService[📧 Email Service<br/>SMTP Gmail]
    Database[(🗄️ MySQL Database<br/>vuln_platform_beta)]
    WebServer[🌐 Web Server<br/>Apache/Nginx]
    Browser[🌍 Web Browser<br/>Chrome/Firefox/Safari]
    FileSystem[📁 File System<br/>Local Storage]
    LogSystem[📊 Logging System<br/>Security Monitoring]
    
    %% Central System
    TryMeOut{{"🎯 TryMeOut<br/>Vulnerability Platform<br/><br/>• User Management<br/>• Challenge System<br/>• Progress Tracking<br/>• Certificate Generation<br/>• Security Monitoring"}}
    
    %% User Interactions
    Students -.->|Registration Requests<br/>Login Credentials<br/>Challenge Submissions| TryMeOut
    TryMeOut -.->|Challenge Content<br/>Progress Reports<br/>Certificates<br/>Badges| Students
    
    Instructors -.->|Course Management<br/>Student Monitoring<br/>Progress Reviews| TryMeOut
    TryMeOut -.->|Student Analytics<br/>Performance Reports<br/>Completion Status| Instructors
    
    Admins -.->|System Configuration<br/>User Management<br/>Security Policies| TryMeOut
    TryMeOut -.->|System Logs<br/>Security Reports<br/>Platform Analytics| Admins
    
    %% System Dependencies
    TryMeOut <-->|User Data<br/>Challenge Data<br/>Progress Records<br/>Security Logs| Database
    TryMeOut <-->|OTP Codes<br/>Notifications<br/>Certificates| EmailService
    TryMeOut <-->|HTTP Requests<br/>Static Content<br/>API Responses| WebServer
    TryMeOut <-->|Profile Pictures<br/>Certificates<br/>System Files| FileSystem
    TryMeOut -->|Security Events<br/>Audit Trails<br/>System Metrics| LogSystem
    
    %% Browser Interaction
    Browser <-->|HTTP/HTTPS<br/>User Interface<br/>AJAX Requests| WebServer
    
    %% Styling
    classDef userEntity fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef systemEntity fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#4a148c
    classDef serviceEntity fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    classDef centralSystem fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#e65100
    
    class Students,Instructors,Admins userEntity
    class Database,WebServer,Browser,FileSystem systemEntity
    class EmailService,LogSystem serviceEntity
    class TryMeOut centralSystem
                </div>
            </div>
            
            <div class="description">
                <h3>🎯 System Overview</h3>
                <p>The TryMeOut Vulnerability Platform serves as a centralized educational system for cybersecurity training. It provides hands-on vulnerability challenges in a controlled environment, allowing students to learn ethical hacking and security concepts safely.</p>
            </div>
            
            <div class="external-entities">
                <div class="entity-card">
                    <h4>👨‍🎓 Students (Primary Users)</h4>
                    <ul>
                        <li>Register and authenticate accounts</li>
                        <li>Access vulnerability challenges</li>
                        <li>Submit solutions and track progress</li>
                        <li>Earn badges and certificates</li>
                        <li>View learning analytics</li>
                    </ul>
                </div>
                
                <div class="entity-card">
                    <h4>👨‍🏫 Instructors (Secondary Users)</h4>
                    <ul>
                        <li>Monitor student progress</li>
                        <li>Access performance analytics</li>
                        <li>Generate progress reports</li>
                        <li>Manage course assignments</li>
                        <li>Review completion certificates</li>
                    </ul>
                </div>
                
                <div class="entity-card">
                    <h4>👨‍💼 System Administrators</h4>
                    <ul>
                        <li>Manage user accounts and roles</li>
                        <li>Configure system settings</li>
                        <li>Monitor security events</li>
                        <li>Generate system reports</li>
                        <li>Maintain platform integrity</li>
                    </ul>
                </div>
                
                <div class="entity-card">
                    <h4>📧 Email Service (SMTP)</h4>
                    <ul>
                        <li>Delivers OTP verification codes</li>
                        <li>Sends password reset emails</li>
                        <li>Provides account notifications</li>
                        <li>Delivers certificate notifications</li>
                    </ul>
                </div>
                
                <div class="entity-card">
                    <h4>🗄️ MySQL Database</h4>
                    <ul>
                        <li>Stores user accounts and profiles</li>
                        <li>Maintains challenge content</li>
                        <li>Tracks user progress and achievements</li>
                        <li>Logs security and audit events</li>
                    </ul>
                </div>
                
                <div class="entity-card">
                    <h4>🌐 Web Server Infrastructure</h4>
                    <ul>
                        <li>Hosts the web application</li>
                        <li>Serves static content and assets</li>
                        <li>Handles HTTP/HTTPS requests</li>
                        <li>Manages session state</li>
                    </ul>
                </div>
            </div>
            
            <div class="description">
                <h3>🔄 Key Data Flows</h3>
                <ul>
                    <li><strong>Authentication Flow:</strong> Users → Platform → Email Service → Database</li>
                    <li><strong>Challenge Flow:</strong> Students → Platform → Database → Progress Tracking</li>
                    <li><strong>Monitoring Flow:</strong> Platform → Logging System → Security Reports</li>
                    <li><strong>Certificate Flow:</strong> Achievement → Platform → Email Service → Users</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            },
            flowchart: {
                nodeSpacing: 50,
                rankSpacing: 80,
                curve: 'basis',
                padding: 20
            }
        });
    </script>
</body>
</html>
