<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - System Diagrams Collection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .container {
            max-width: 1200px;
        }
        .header-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
        }
        .header-card h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 15px;
        }
        .header-card p {
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 0;
        }
        .diagram-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            height: 100%;
        }
        .diagram-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        .diagram-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            font-size: 3rem;
        }
        .diagram-content {
            padding: 25px;
        }
        .diagram-title {
            color: #2d3748;
            font-weight: 600;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }
        .diagram-description {
            color: #718096;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .diagram-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        .diagram-features li {
            color: #4a5568;
            font-size: 0.9rem;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .diagram-features li:before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .btn-view {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }
        .btn-view:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        .report-note {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            color: #234e52;
        }
        .report-note h4 {
            color: #234e52;
            margin-bottom: 15px;
        }
        .usage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .usage-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .usage-item strong {
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-card">
            <h1><i class="fas fa-project-diagram me-3"></i>TryMeOut Platform System Diagrams</h1>
            <p>Comprehensive collection of system analysis and design diagrams for the vulnerability learning platform</p>
        </div>
        
        <div class="report-note">
            <h4><i class="fas fa-file-alt me-2"></i>Report Integration Guide</h4>
            <p>These diagrams are specifically designed for academic and technical reports. Each diagram includes:</p>
            <div class="usage-grid">
                <div class="usage-item">
                    <strong>High Resolution</strong><br>
                    <small>Print-ready quality</small>
                </div>
                <div class="usage-item">
                    <strong>Professional Styling</strong><br>
                    <small>Academic standards</small>
                </div>
                <div class="usage-item">
                    <strong>Detailed Legends</strong><br>
                    <small>Clear explanations</small>
                </div>
                <div class="usage-item">
                    <strong>Export Options</strong><br>
                    <small>PDF, PNG, SVG formats</small>
                </div>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-6 col-md-12">
                <div class="diagram-card">
                    <div class="diagram-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="diagram-content">
                        <h3 class="diagram-title">Entity Relationship Diagram (ERD)</h3>
                        <p class="diagram-description">
                            Complete database schema showing all entities, attributes, relationships, and constraints for the TryMeOut platform.
                        </p>
                        <ul class="diagram-features">
                            <li>15+ database tables with relationships</li>
                            <li>Primary and foreign key mappings</li>
                            <li>Data types and constraints</li>
                            <li>Normalized database design</li>
                            <li>Security and audit table structures</li>
                        </ul>
                        <a href="erd_diagram.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye me-2"></i>View ERD
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 col-md-12">
                <div class="diagram-card">
                    <div class="diagram-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="diagram-content">
                        <h3 class="diagram-title">Context Diagram</h3>
                        <p class="diagram-description">
                            High-level system overview showing the TryMeOut platform boundaries and interactions with external entities.
                        </p>
                        <ul class="diagram-features">
                            <li>System boundary definition</li>
                            <li>External entity identification</li>
                            <li>Data flow directions</li>
                            <li>Stakeholder interactions</li>
                            <li>Service dependencies</li>
                        </ul>
                        <a href="context_diagram.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye me-2"></i>View Context Diagram
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 col-md-12">
                <div class="diagram-card">
                    <div class="diagram-icon">
                        <i class="fas fa-stream"></i>
                    </div>
                    <div class="diagram-content">
                        <h3 class="diagram-title">Data Flow Diagram Level 1</h3>
                        <p class="diagram-description">
                            Detailed breakdown of system processes showing data flows between major functional components.
                        </p>
                        <ul class="diagram-features">
                            <li>6 major system processes</li>
                            <li>Data store interactions</li>
                            <li>Process decomposition</li>
                            <li>Inter-process communications</li>
                            <li>External system interfaces</li>
                        </ul>
                        <a href="dfd_level1.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye me-2"></i>View DFD Level 1
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 col-md-12">
                <div class="diagram-card">
                    <div class="diagram-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="diagram-content">
                        <h3 class="diagram-title">System Flowchart</h3>
                        <p class="diagram-description">
                            Complete user journey flowchart from registration to challenge completion with all decision points and error handling.
                        </p>
                        <ul class="diagram-features">
                            <li>End-to-end user workflows</li>
                            <li>Decision logic and branching</li>
                            <li>Error handling paths</li>
                            <li>Security checkpoints</li>
                            <li>System response flows</li>
                        </ul>
                        <a href="system_flowchart.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye me-2"></i>View Flowchart
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-12">
                <div class="diagram-card">
                    <div class="diagram-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="diagram-content">
                        <h3 class="diagram-title">System Architecture</h3>
                        <p class="diagram-description">
                            Comprehensive technical architecture showing all system layers, components, technologies, and their interactions in the TryMeOut platform.
                        </p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="diagram-features">
                                    <li>Multi-layer architecture design</li>
                                    <li>Technology stack visualization</li>
                                    <li>Component interactions</li>
                                    <li>Security layer integration</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="diagram-features">
                                    <li>Infrastructure components</li>
                                    <li>External service integrations</li>
                                    <li>Data flow patterns</li>
                                    <li>Deployment architecture</li>
                                </ul>
                            </div>
                        </div>
                        <a href="system_architecture.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye me-2"></i>View System Architecture
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="report-note mt-4">
            <h4><i class="fas fa-download me-2"></i>Export Instructions</h4>
            <p><strong>For Report Integration:</strong></p>
            <ol>
                <li><strong>Open any diagram</strong> by clicking the "View" button</li>
                <li><strong>Right-click on the diagram</strong> and select "Save image as..." or "Print to PDF"</li>
                <li><strong>Use browser print function</strong> (Ctrl+P) to save as PDF with high quality</li>
                <li><strong>For vector graphics:</strong> Use browser developer tools to export SVG</li>
                <li><strong>Recommended format:</strong> PDF for reports, PNG for presentations</li>
            </ol>
            <p><strong>Print Settings:</strong> Use "More settings" → "Paper size: A3" → "Margins: None" for best quality</p>
        </div>
        
        <div class="text-center mt-4">
            <p class="text-white">
                <i class="fas fa-info-circle me-2"></i>
                All diagrams are interactive and optimized for academic reports and technical documentation.
            </p>
        </div>
    </div>
</body>
</html>
