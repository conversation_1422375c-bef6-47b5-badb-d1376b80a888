<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - Data Flow Diagram Level 1</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            min-height: 800px;
        }
        .process-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .process-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .process-card h4 {
            margin-top: 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .process-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .legend {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .legend h3 {
            margin-top: 0;
            color: #374151;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-shape {
            width: 30px;
            height: 20px;
            margin-right: 10px;
            border: 2px solid #374151;
        }
        .print-note {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Data Flow Diagram - Level 1</h1>
            <p>TryMeOut Vulnerability Platform - Detailed Process Breakdown</p>
        </div>
        
        <div class="content">
            <div class="print-note">
                <strong>📋 Report Usage:</strong> This Level 1 DFD breaks down the TryMeOut platform into major processes, showing detailed data flows between processes, external entities, and data stores.
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
graph TB
    %% External Entities
    Students[👨‍🎓 Students]
    Instructors[👨‍🏫 Instructors]
    Admins[👨‍💼 Administrators]
    EmailSvc[📧 Email Service]
    
    %% Processes
    P1((1.0<br/>User<br/>Authentication<br/>& Registration))
    P2((2.0<br/>Challenge<br/>Management<br/>& Delivery))
    P3((3.0<br/>Progress<br/>Tracking<br/>& Assessment))
    P4((4.0<br/>Gamification<br/>& Rewards))
    P5((5.0<br/>Security<br/>Monitoring<br/>& Logging))
    P6((6.0<br/>Admin<br/>Management<br/>& Reports))
    
    %% Data Stores
    DS1[(D1: Users)]
    DS2[(D2: Challenges)]
    DS3[(D3: Progress)]
    DS4[(D4: Certificates)]
    DS5[(D5: Security Logs)]
    DS6[(D6: System Config)]
    
    %% Student Flows
    Students -->|Registration Data<br/>Login Credentials| P1
    P1 -->|Account Status<br/>Authentication Result| Students
    
    Students -->|Challenge Requests<br/>Solution Submissions| P2
    P2 -->|Challenge Content<br/>Feedback| Students
    
    Students -->|Progress Queries| P3
    P3 -->|Progress Reports<br/>Achievement Status| Students
    
    Students -->|Badge Requests| P4
    P4 -->|Badges<br/>Certificates| Students
    
    %% Instructor Flows
    Instructors -->|Student Queries<br/>Report Requests| P6
    P6 -->|Student Analytics<br/>Progress Reports| Instructors
    
    %% Admin Flows
    Admins -->|User Management<br/>System Config| P6
    P6 -->|System Status<br/>Security Reports| Admins
    
    Admins -->|Security Queries| P5
    P5 -->|Audit Reports<br/>Security Alerts| Admins
    
    %% Email Service Flows
    P1 -->|OTP Requests<br/>Verification Emails| EmailSvc
    EmailSvc -->|Delivery Status| P1
    
    P4 -->|Certificate Emails<br/>Achievement Notifications| EmailSvc
    EmailSvc -->|Delivery Confirmation| P4
    
    %% Process to Data Store Flows
    P1 <-->|User Data<br/>Authentication Records| DS1
    P1 -->|Login Attempts<br/>Security Events| DS5
    
    P2 <-->|Challenge Content<br/>Category Data| DS2
    P2 -->|Access Logs| DS5
    
    P3 <-->|Progress Records<br/>Completion Data| DS3
    P3 -->|Progress Events| DS5
    
    P4 <-->|Badge Data<br/>Certificate Records| DS4
    P4 -->|Achievement Events| DS5
    
    P5 <-->|Security Events<br/>Audit Trails| DS5
    
    P6 <-->|System Settings<br/>Admin Configs| DS6
    P6 <-->|User Management| DS1
    P6 <-->|Report Data| DS3
    P6 <-->|Security Data| DS5
    
    %% Inter-Process Flows
    P1 -->|User Validation| P2
    P1 -->|User Validation| P3
    P1 -->|User Validation| P4
    P1 -->|Authentication Events| P5
    
    P2 -->|Challenge Completion| P3
    P3 -->|Achievement Triggers| P4
    
    P2 -->|Challenge Events| P5
    P3 -->|Progress Events| P5
    P4 -->|Reward Events| P5
    P6 -->|Admin Actions| P5
    
    %% Styling
    classDef externalEntity fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef process fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#e65100
    classDef dataStore fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    
    class Students,Instructors,Admins,EmailSvc externalEntity
    class P1,P2,P3,P4,P5,P6 process
    class DS1,DS2,DS3,DS4,DS5,DS6 dataStore
                </div>
            </div>
            
            <div class="legend">
                <h3>📋 DFD Level 1 Legend</h3>
                <div class="legend-item">
                    <div class="legend-shape" style="background: #e3f2fd; border-radius: 4px;"></div>
                    <span><strong>External Entity:</strong> People or systems outside the platform boundary</span>
                </div>
                <div class="legend-item">
                    <div class="legend-shape" style="background: #fff3e0; border-radius: 50%;"></div>
                    <span><strong>Process:</strong> Major system functions that transform data</span>
                </div>
                <div class="legend-item">
                    <div class="legend-shape" style="background: #e8f5e8; border-radius: 0;"></div>
                    <span><strong>Data Store:</strong> Persistent storage of system data</span>
                </div>
                <div class="legend-item">
                    <div style="width: 30px; height: 2px; background: #374151; margin-right: 10px;"></div>
                    <span><strong>Data Flow:</strong> Movement of data between components</span>
                </div>
            </div>
            
            <div class="process-details">
                <div class="process-card">
                    <h4>🔐 1.0 User Authentication & Registration</h4>
                    <ul>
                        <li>User registration and email verification</li>
                        <li>Login authentication with brute force protection</li>
                        <li>Password reset and OTP verification</li>
                        <li>Session management and security</li>
                        <li>Account lockout and security monitoring</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h4>🎯 2.0 Challenge Management & Delivery</h4>
                    <ul>
                        <li>Challenge content delivery by category</li>
                        <li>Solution validation and feedback</li>
                        <li>Difficulty progression management</li>
                        <li>Hint system and guidance</li>
                        <li>Challenge access control</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h4>📈 3.0 Progress Tracking & Assessment</h4>
                    <ul>
                        <li>User progress recording and updates</li>
                        <li>Completion status tracking</li>
                        <li>Performance analytics generation</li>
                        <li>Learning path recommendations</li>
                        <li>Progress report generation</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h4>🏆 4.0 Gamification & Rewards</h4>
                    <ul>
                        <li>Badge earning and validation</li>
                        <li>Certificate generation and issuance</li>
                        <li>Achievement milestone tracking</li>
                        <li>Reward notification system</li>
                        <li>Leaderboard and ranking</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h4>🛡️ 5.0 Security Monitoring & Logging</h4>
                    <ul>
                        <li>Security event detection and logging</li>
                        <li>Audit trail maintenance</li>
                        <li>Anomaly detection and alerting</li>
                        <li>Access control monitoring</li>
                        <li>Compliance reporting</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h4>⚙️ 6.0 Admin Management & Reports</h4>
                    <ul>
                        <li>User account management</li>
                        <li>System configuration and settings</li>
                        <li>Analytics and reporting dashboard</li>
                        <li>Platform monitoring and maintenance</li>
                        <li>Security report generation</li>
                    </ul>
                </div>
            </div>
            
            <div class="legend">
                <h3>💾 Data Store Details</h3>
                <ul>
                    <li><strong>D1: Users</strong> - User accounts, profiles, authentication data</li>
                    <li><strong>D2: Challenges</strong> - Challenge content, categories, solutions</li>
                    <li><strong>D3: Progress</strong> - User progress, completions, achievements</li>
                    <li><strong>D4: Certificates</strong> - Badges, certificates, rewards</li>
                    <li><strong>D5: Security Logs</strong> - Audit trails, security events, monitoring data</li>
                    <li><strong>D6: System Config</strong> - Platform settings, configurations, admin data</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            },
            flowchart: {
                nodeSpacing: 60,
                rankSpacing: 100,
                curve: 'basis',
                padding: 30
            }
        });
    </script>
</body>
</html>
