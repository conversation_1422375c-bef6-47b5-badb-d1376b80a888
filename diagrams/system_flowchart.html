<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - System Flowchart</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            min-height: 1000px;
        }
        .flow-description {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .flow-description h3 {
            margin-top: 0;
            color: #374151;
        }
        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .step-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .step-card h4 {
            margin-top: 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .print-note {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 System Flowchart</h1>
            <p>TryMeOut Vulnerability Platform - Complete User Journey & System Flow</p>
        </div>
        
        <div class="content">
            <div class="print-note">
                <strong>📋 Report Usage:</strong> This flowchart illustrates the complete user journey through the TryMeOut platform, from registration to challenge completion, showing decision points and system responses.
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
flowchart TD
    Start([🚀 User Visits Platform]) --> Landing{Landing Page}
    Landing --> Register[📝 Register Account]
    Landing --> Login[🔐 Login]
    Landing --> Browse[👀 Browse Public Content]
    
    Register --> ValidateReg{Valid Registration?}
    ValidateReg -->|No| RegError[❌ Show Validation Errors]
    RegError --> Register
    ValidateReg -->|Yes| SendOTP[📧 Send OTP Email]
    SendOTP --> EnterOTP[🔢 Enter OTP Code]
    EnterOTP --> VerifyOTP{OTP Valid?}
    VerifyOTP -->|No| OTPError[❌ Invalid OTP]
    OTPError --> EnterOTP
    VerifyOTP -->|Yes| AccountVerified[✅ Account Verified]
    AccountVerified --> Dashboard
    
    Login --> CheckCreds{Valid Credentials?}
    CheckCreds -->|No| LoginError[❌ Login Failed]
    LoginError --> BruteCheck{Too Many Attempts?}
    BruteCheck -->|Yes| AccountLocked[🔒 Account Locked]
    AccountLocked --> WaitUnlock[⏰ Wait for Unlock]
    WaitUnlock --> Login
    BruteCheck -->|No| Login
    CheckCreds -->|Yes| CheckVerified{Account Verified?}
    CheckVerified -->|No| NotVerified[⚠️ Account Not Verified]
    NotVerified --> ResendOTP[📧 Resend OTP]
    ResendOTP --> EnterOTP
    CheckVerified -->|Yes| CheckRole{User Role?}
    CheckRole -->|Admin| AdminDash[👨‍💼 Admin Dashboard]
    CheckRole -->|Student| Dashboard[📊 Student Dashboard]
    
    Dashboard --> ViewProgress[📈 View Progress]
    Dashboard --> SelectCategory[🎯 Select Challenge Category]
    Dashboard --> ViewCerts[🏆 View Certificates]
    Dashboard --> Settings[⚙️ Account Settings]
    
    SelectCategory --> ShowChallenges[📋 Show Available Challenges]
    ShowChallenges --> SelectChallenge[🎯 Select Challenge]
    SelectChallenge --> CheckAccess{Has Access?}
    CheckAccess -->|No| AccessDenied[❌ Access Denied]
    AccessDenied --> Dashboard
    CheckAccess -->|Yes| LoadChallenge[📖 Load Challenge Content]
    
    LoadChallenge --> ShowChallenge[🎮 Display Challenge]
    ShowChallenge --> ReadInstructions[📚 Read Instructions]
    ReadInstructions --> AttemptSolution[💡 Attempt Solution]
    AttemptSolution --> SubmitAnswer[📤 Submit Answer]
    
    SubmitAnswer --> ValidateAnswer{Correct Answer?}
    ValidateAnswer -->|No| ShowHint{Show Hint?}
    ShowHint -->|Yes| DisplayHint[💡 Display Hint]
    DisplayHint --> AttemptSolution
    ShowHint -->|No| TryAgain[🔄 Try Again]
    TryAgain --> AttemptSolution
    
    ValidateAnswer -->|Yes| MarkComplete[✅ Mark Challenge Complete]
    MarkComplete --> UpdateProgress[📊 Update Progress]
    UpdateProgress --> CheckBadge{Earned Badge?}
    CheckBadge -->|Yes| AwardBadge[🏅 Award Badge]
    CheckBadge -->|No| CheckCert{Earned Certificate?}
    AwardBadge --> CheckCert
    CheckCert -->|Yes| IssueCert[📜 Issue Certificate]
    CheckCert -->|No| NextChallenge{More Challenges?}
    IssueCert --> SendCertEmail[📧 Send Certificate Email]
    SendCertEmail --> NextChallenge
    
    NextChallenge -->|Yes| ShowNext[➡️ Show Next Challenge]
    ShowNext --> SelectChallenge
    NextChallenge -->|No| CategoryComplete[🎉 Category Complete]
    CategoryComplete --> Dashboard
    
    ViewProgress --> ShowStats[📊 Display Statistics]
    ShowStats --> Dashboard
    
    ViewCerts --> ShowCertList[📜 Display Certificates]
    ShowCertList --> DownloadCert{Download Certificate?}
    DownloadCert -->|Yes| GeneratePDF[📄 Generate PDF]
    GeneratePDF --> Dashboard
    DownloadCert -->|No| Dashboard
    
    Settings --> ChangePassword[🔑 Change Password]
    Settings --> UpdateProfile[👤 Update Profile]
    Settings --> UploadPicture[📸 Upload Picture]
    ChangePassword --> Dashboard
    UpdateProfile --> Dashboard
    UploadPicture --> Dashboard
    
    AdminDash --> ManageUsers[👥 Manage Users]
    AdminDash --> ViewLogs[📋 View System Logs]
    AdminDash --> SystemSettings[⚙️ System Settings]
    AdminDash --> GenerateReports[📊 Generate Reports]
    ManageUsers --> AdminDash
    ViewLogs --> AdminDash
    SystemSettings --> AdminDash
    GenerateReports --> AdminDash
    
    Browse --> PublicInfo[ℹ️ View Public Information]
    PublicInfo --> Landing
    
    %% Logout Flow
    Dashboard --> Logout[🚪 Logout]
    AdminDash --> Logout
    Logout --> ClearSession[🗑️ Clear Session]
    ClearSession --> Landing
    
    %% Styling
    classDef startEnd fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#e65100
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#b71c1c
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    
    class Start,Landing,Dashboard,AdminDash startEnd
    class Register,Login,SendOTP,LoadChallenge,ShowChallenge,SubmitAnswer,UpdateProgress process
    class ValidateReg,VerifyOTP,CheckCreds,CheckRole,ValidateAnswer,CheckBadge decision
    class RegError,LoginError,OTPError,AccessDenied,AccountLocked error
    class AccountVerified,MarkComplete,AwardBadge,IssueCert,CategoryComplete success
                </div>
            </div>
            
            <div class="flow-description">
                <h3>🎯 System Flow Overview</h3>
                <p>This flowchart represents the complete user journey through the TryMeOut vulnerability platform, from initial registration to challenge completion and certification. It shows all major decision points, error handling, and system responses.</p>
            </div>
            
            <div class="flow-steps">
                <div class="step-card">
                    <h4><span class="step-number">1</span> User Registration & Verification</h4>
                    <ul>
                        <li>User provides registration details</li>
                        <li>System validates input data</li>
                        <li>OTP sent via email for verification</li>
                        <li>Account activated upon OTP confirmation</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4><span class="step-number">2</span> Authentication & Access Control</h4>
                    <ul>
                        <li>Credential validation with security checks</li>
                        <li>Brute force protection mechanisms</li>
                        <li>Role-based dashboard redirection</li>
                        <li>Session management and security</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4><span class="step-number">3</span> Challenge Selection & Access</h4>
                    <ul>
                        <li>Category-based challenge organization</li>
                        <li>Progressive difficulty unlocking</li>
                        <li>Access control validation</li>
                        <li>Challenge content delivery</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4><span class="step-number">4</span> Solution Submission & Validation</h4>
                    <ul>
                        <li>Answer submission and processing</li>
                        <li>Automated solution validation</li>
                        <li>Hint system for guidance</li>
                        <li>Multiple attempt handling</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4><span class="step-number">5</span> Progress Tracking & Rewards</h4>
                    <ul>
                        <li>Completion status updates</li>
                        <li>Badge earning validation</li>
                        <li>Certificate generation</li>
                        <li>Achievement notifications</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4><span class="step-number">6</span> Administrative Functions</h4>
                    <ul>
                        <li>User account management</li>
                        <li>System monitoring and logs</li>
                        <li>Platform configuration</li>
                        <li>Analytics and reporting</li>
                    </ul>
                </div>
            </div>
            
            <div class="flow-description">
                <h3>🔄 Key Decision Points</h3>
                <ul>
                    <li><strong>Registration Validation:</strong> Ensures data integrity and security requirements</li>
                    <li><strong>OTP Verification:</strong> Confirms email ownership and prevents fake accounts</li>
                    <li><strong>Authentication Checks:</strong> Multi-layer security with brute force protection</li>
                    <li><strong>Access Control:</strong> Role-based permissions and challenge prerequisites</li>
                    <li><strong>Solution Validation:</strong> Automated checking with hint system support</li>
                    <li><strong>Achievement Logic:</strong> Badge and certificate earning criteria</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            },
            flowchart: {
                nodeSpacing: 50,
                rankSpacing: 80,
                curve: 'basis',
                padding: 20
            }
        });
    </script>
</body>
</html>
