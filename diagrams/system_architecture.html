<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut Platform - System Architecture</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            min-height: 800px;
        }
        .architecture-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .layer-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .layer-card h4 {
            margin-top: 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .layer-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .tech-stack {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tech-stack h3 {
            margin-top: 0;
            color: #374151;
        }
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .tech-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
        }
        .tech-item strong {
            color: #1f2937;
        }
        .print-note {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ System Architecture</h1>
            <p>TryMeOut Vulnerability Platform - Technical Architecture & Components</p>
        </div>
        
        <div class="content">
            <div class="print-note">
                <strong>📋 Report Usage:</strong> This architecture diagram shows the technical structure of the TryMeOut platform, including all layers, components, technologies, and their interactions.
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
graph TB
    subgraph "🌐 Presentation Layer"
        Browser[🌍 Web Browser<br/>Chrome, Firefox, Safari]
        Mobile[📱 Mobile Browser<br/>Responsive Design]
    end
    
    subgraph "🔗 Web Server Layer"
        WebServer[🌐 Apache/Nginx<br/>HTTP Server]
        SSL[🔒 SSL/TLS<br/>HTTPS Encryption]
        LoadBalancer[⚖️ Load Balancer<br/>Traffic Distribution]
    end
    
    subgraph "💻 Application Layer"
        subgraph "🎯 Core Application"
            AuthModule[🔐 Authentication Module<br/>• User Registration<br/>• Login/Logout<br/>• OTP Verification<br/>• Session Management]
            ChallengeEngine[🎮 Challenge Engine<br/>• Challenge Delivery<br/>• Solution Validation<br/>• Progress Tracking<br/>• Hint System]
            UserMgmt[👤 User Management<br/>• Profile Management<br/>• Role-based Access<br/>• Account Settings<br/>• File Uploads]
            Gamification[🏆 Gamification System<br/>• Badge Management<br/>• Certificate Generation<br/>• Achievement Tracking<br/>• Leaderboards]
        end
        
        subgraph "🛡️ Security Layer"
            SecurityMonitor[🔍 Security Monitor<br/>• Brute Force Protection<br/>• Anomaly Detection<br/>• Access Control<br/>• Threat Detection]
            AuditLogger[📋 Audit Logger<br/>• User Actions<br/>• System Events<br/>• Security Logs<br/>• Compliance Tracking]
        end
        
        subgraph "⚙️ Admin Layer"
            AdminPanel[👨‍💼 Admin Panel<br/>• User Management<br/>• System Configuration<br/>• Analytics Dashboard<br/>• Report Generation]
            LogViewer[📊 Log Viewer<br/>• Security Logs<br/>• Audit Trails<br/>• System Monitoring<br/>• Alert Management]
        end
    end
    
    subgraph "🗄️ Data Layer"
        subgraph "📊 Database Cluster"
            MySQL[(🗄️ MySQL Database<br/>vuln_platform_beta<br/>• User Data<br/>• Challenge Content<br/>• Progress Records<br/>• Security Logs)]
            Backup[(💾 Database Backup<br/>• Automated Backups<br/>• Point-in-time Recovery<br/>• Data Redundancy)]
        end
        
        subgraph "📁 File Storage"
            FileSystem[📁 Local File System<br/>• Profile Pictures<br/>• Certificates<br/>• System Files<br/>• Upload Storage]
            TempFiles[🗂️ Temporary Storage<br/>• Session Data<br/>• Cache Files<br/>• Processing Files]
        end
    end
    
    subgraph "🌍 External Services"
        EmailService[📧 Email Service<br/>Gmail SMTP<br/>• OTP Delivery<br/>• Notifications<br/>• Certificate Emails]
        GeoLocation[🌍 GeoLocation API<br/>• IP Geolocation<br/>• Country Detection<br/>• Security Analytics]
    end
    
    subgraph "🔧 Infrastructure Layer"
        OS[🖥️ Operating System<br/>Linux/Windows Server]
        PHP[🐘 PHP Runtime<br/>Version 8.x<br/>• Core Engine<br/>• Extensions<br/>• Configuration]
        WebStack[📚 Web Stack<br/>• HTML5/CSS3<br/>• JavaScript<br/>• Bootstrap<br/>• Font Awesome]
    end
    
    %% Connections
    Browser --> WebServer
    Mobile --> WebServer
    WebServer --> SSL
    SSL --> LoadBalancer
    LoadBalancer --> AuthModule
    LoadBalancer --> ChallengeEngine
    LoadBalancer --> UserMgmt
    LoadBalancer --> Gamification
    
    AuthModule --> SecurityMonitor
    ChallengeEngine --> AuditLogger
    UserMgmt --> AuditLogger
    Gamification --> AuditLogger
    
    SecurityMonitor --> AdminPanel
    AuditLogger --> LogViewer
    AdminPanel --> LogViewer
    
    AuthModule --> MySQL
    ChallengeEngine --> MySQL
    UserMgmt --> MySQL
    Gamification --> MySQL
    SecurityMonitor --> MySQL
    AuditLogger --> MySQL
    AdminPanel --> MySQL
    
    UserMgmt --> FileSystem
    Gamification --> FileSystem
    AuthModule --> TempFiles
    
    AuthModule --> EmailService
    Gamification --> EmailService
    SecurityMonitor --> GeoLocation
    
    MySQL --> Backup
    
    WebServer --> OS
    AuthModule --> PHP
    ChallengeEngine --> PHP
    UserMgmt --> PHP
    Gamification --> PHP
    SecurityMonitor --> PHP
    AuditLogger --> PHP
    AdminPanel --> PHP
    
    Browser --> WebStack
    Mobile --> WebStack
    
    %% Styling
    classDef presentation fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef webserver fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#4a148c
    classDef application fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#e65100
    classDef security fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#b71c1c
    classDef admin fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    classDef data fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#880e4f
    classDef infrastructure fill:#f9fbe7,stroke:#827717,stroke-width:2px,color:#558b2f
    
    class Browser,Mobile presentation
    class WebServer,SSL,LoadBalancer webserver
    class AuthModule,ChallengeEngine,UserMgmt,Gamification application
    class SecurityMonitor,AuditLogger security
    class AdminPanel,LogViewer admin
    class MySQL,Backup,FileSystem,TempFiles data
    class EmailService,GeoLocation external
    class OS,PHP,WebStack infrastructure
                </div>
            </div>
            
            <div class="architecture-details">
                <div class="layer-card">
                    <h4>🌐 Presentation Layer</h4>
                    <ul>
                        <li><strong>Web Browsers:</strong> Cross-browser compatibility</li>
                        <li><strong>Responsive Design:</strong> Mobile-first approach</li>
                        <li><strong>User Interface:</strong> Bootstrap 5 framework</li>
                        <li><strong>Interactive Elements:</strong> JavaScript & AJAX</li>
                        <li><strong>Accessibility:</strong> WCAG compliance</li>
                    </ul>
                </div>
                
                <div class="layer-card">
                    <h4>🔗 Web Server Layer</h4>
                    <ul>
                        <li><strong>HTTP Server:</strong> Apache/Nginx configuration</li>
                        <li><strong>SSL/TLS:</strong> HTTPS encryption</li>
                        <li><strong>Load Balancing:</strong> Traffic distribution</li>
                        <li><strong>Caching:</strong> Static content optimization</li>
                        <li><strong>Compression:</strong> Gzip/Brotli compression</li>
                    </ul>
                </div>
                
                <div class="layer-card">
                    <h4>💻 Application Layer</h4>
                    <ul>
                        <li><strong>MVC Architecture:</strong> Separation of concerns</li>
                        <li><strong>Modular Design:</strong> Component-based structure</li>
                        <li><strong>API Endpoints:</strong> RESTful services</li>
                        <li><strong>Session Management:</strong> Secure state handling</li>
                        <li><strong>Error Handling:</strong> Comprehensive logging</li>
                    </ul>
                </div>
                
                <div class="layer-card">
                    <h4>🛡️ Security Layer</h4>
                    <ul>
                        <li><strong>Authentication:</strong> Multi-factor verification</li>
                        <li><strong>Authorization:</strong> Role-based access control</li>
                        <li><strong>Input Validation:</strong> XSS/SQL injection prevention</li>
                        <li><strong>Monitoring:</strong> Real-time threat detection</li>
                        <li><strong>Compliance:</strong> Security standards adherence</li>
                    </ul>
                </div>
                
                <div class="layer-card">
                    <h4>🗄️ Data Layer</h4>
                    <ul>
                        <li><strong>Database:</strong> MySQL with optimized queries</li>
                        <li><strong>Data Integrity:</strong> ACID compliance</li>
                        <li><strong>Backup Strategy:</strong> Automated backups</li>
                        <li><strong>File Storage:</strong> Secure file management</li>
                        <li><strong>Caching:</strong> Performance optimization</li>
                    </ul>
                </div>
                
                <div class="layer-card">
                    <h4>🌍 External Services</h4>
                    <ul>
                        <li><strong>Email Service:</strong> SMTP integration</li>
                        <li><strong>Geolocation:</strong> IP-based location services</li>
                        <li><strong>CDN:</strong> Content delivery network</li>
                        <li><strong>Monitoring:</strong> External health checks</li>
                        <li><strong>Analytics:</strong> Usage tracking</li>
                    </ul>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-grid">
                    <div class="tech-item">
                        <strong>Frontend</strong><br>
                        HTML5, CSS3, JavaScript<br>
                        Bootstrap 5, Font Awesome
                    </div>
                    <div class="tech-item">
                        <strong>Backend</strong><br>
                        PHP 8.x<br>
                        PDO, Sessions, OOP
                    </div>
                    <div class="tech-item">
                        <strong>Database</strong><br>
                        MySQL 8.0<br>
                        InnoDB Engine
                    </div>
                    <div class="tech-item">
                        <strong>Web Server</strong><br>
                        Apache/Nginx<br>
                        SSL/TLS, HTTP/2
                    </div>
                    <div class="tech-item">
                        <strong>Security</strong><br>
                        Password Hashing<br>
                        CSRF Protection, XSS Prevention
                    </div>
                    <div class="tech-item">
                        <strong>Email</strong><br>
                        PHPMailer<br>
                        SMTP Gmail
                    </div>
                    <div class="tech-item">
                        <strong>File Handling</strong><br>
                        Secure Upload<br>
                        Image Processing
                    </div>
                    <div class="tech-item">
                        <strong>Logging</strong><br>
                        Custom Logger<br>
                        JSON Structured Logs
                    </div>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3>🔧 Deployment Architecture</h3>
                <ul>
                    <li><strong>Environment:</strong> LAMP/LEMP stack (Linux, Apache/Nginx, MySQL, PHP)</li>
                    <li><strong>Configuration:</strong> Environment-based settings (development, staging, production)</li>
                    <li><strong>Security:</strong> Firewall rules, intrusion detection, regular updates</li>
                    <li><strong>Monitoring:</strong> System health checks, performance metrics, error tracking</li>
                    <li><strong>Backup:</strong> Automated database backups, file system snapshots</li>
                    <li><strong>Scalability:</strong> Horizontal scaling capability, load balancing ready</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            },
            flowchart: {
                nodeSpacing: 50,
                rankSpacing: 80,
                curve: 'basis',
                padding: 20
            }
        });
    </script>
</body>
</html>
