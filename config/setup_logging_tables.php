<?php
/**
 * Database Migration Script for Enhanced Logging System
 * Run this script once to create all required tables for the logging system
 */

require 'db_connect.php';

echo "Setting up enhanced logging tables...\n";

try {
    // Create security_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(50) NOT NULL,
            username VARCHAR(100),
            user_id INT,
            ip_address VARCHAR(45) NOT NULL,
            country VARCHAR(100),
            city VARCHAR(100),
            user_agent TEXT,
            session_id VARCHAR(255),
            risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
            details JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_type (event_type),
            INDEX idx_ip_address (ip_address),
            INDEX idx_created_at (created_at),
            INDEX idx_risk_level (risk_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ security_logs table created successfully\n";

    // Create login_attempts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            country VARCHAR(100),
            city VARCHAR(100),
            user_agent TEXT,
            success BOOLEAN DEFAULT FALSE,
            failure_reason VARCHAR(255),
            session_duration INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_ip_address (ip_address),
            INDEX idx_created_at (created_at),
            INDEX idx_success (success)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ login_attempts table created successfully\n";

    // Create anomaly_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS anomaly_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            anomaly_type VARCHAR(50) NOT NULL,
            user_id INT,
            ip_address VARCHAR(45),
            severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
            description TEXT,
            data JSON,
            resolved BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_anomaly_type (anomaly_type),
            INDEX idx_severity (severity),
            INDEX idx_resolved (resolved),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ anomaly_logs table created successfully\n";

    // Check if system_logs table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_logs'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS system_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category VARCHAR(50) NOT NULL,
                log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                message TEXT NOT NULL,
                ip_address VARCHAR(45),
                details JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_log_level (log_level),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ system_logs table created successfully\n";
    } else {
        echo "✅ system_logs table already exists\n";
    }

    // Check if audit_logs table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'audit_logs'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                action VARCHAR(100) NOT NULL,
                username VARCHAR(100),
                user_id INT,
                resource_type VARCHAR(50),
                resource_id INT,
                old_values JSON,
                new_values JSON,
                result ENUM('SUCCESS', 'FAILURE') DEFAULT 'SUCCESS',
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_action (action),
                INDEX idx_username (username),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ audit_logs table created successfully\n";
    } else {
        echo "✅ audit_logs table already exists\n";
    }

    // Check if application_logs table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'application_logs'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS application_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                component VARCHAR(50) NOT NULL,
                log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                message TEXT NOT NULL,
                username VARCHAR(100),
                user_id INT,
                context JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_component (component),
                INDEX idx_log_level (log_level),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ application_logs table created successfully\n";
    } else {
        echo "✅ application_logs table already exists\n";
    }

    // Check if error_logs table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'error_logs'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                error_type VARCHAR(50) NOT NULL,
                severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                error_message TEXT NOT NULL,
                file_path VARCHAR(255),
                line_number INT,
                stack_trace TEXT,
                user_id INT,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_error_type (error_type),
                INDEX idx_severity (severity),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ error_logs table created successfully\n";
    } else {
        echo "✅ error_logs table already exists\n";
    }

    // Insert some sample data for testing
    echo "\nInserting sample data for testing...\n";

    // Sample security log
    $pdo->exec("
        INSERT IGNORE INTO security_logs (event_type, username, user_id, ip_address, country, city, risk_level, details) 
        VALUES ('SYSTEM_SETUP', 'admin', 1, '127.0.0.1', 'Local', 'Localhost', 'LOW', '{\"action\": \"logging_system_setup\"}')
    ");

    // Sample login attempt
    $pdo->exec("
        INSERT IGNORE INTO login_attempts (email, ip_address, country, city, success, failure_reason) 
        VALUES ('<EMAIL>', '127.0.0.1', 'Local', 'Localhost', TRUE, NULL)
    ");

    // Sample system log
    $pdo->exec("
        INSERT IGNORE INTO system_logs (category, log_level, message, ip_address) 
        VALUES ('SECURITY', 'INFO', 'Enhanced logging system initialized', '127.0.0.1')
    ");

    echo "✅ Sample data inserted successfully\n";

    echo "\n🎉 Enhanced logging system setup completed successfully!\n";
    echo "You can now access the admin dashboard logs section.\n";

} catch (PDOException $e) {
    echo "❌ Error setting up logging tables: " . $e->getMessage() . "\n";
    exit(1);
}
?>
