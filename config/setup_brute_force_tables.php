<?php
// Setup Brute Force Protection Tables
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Setting Up Brute Force Protection Tables</h1>";

try {
    require 'db_connect.php';
    echo "✅ Database connection established<br>";
    
    // Create brute_force_attempts table
    echo "<h2>Creating brute_force_attempts table...</h2>";
    $sql_attempts = "
        CREATE TABLE IF NOT EXISTS brute_force_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_agent TEXT,
            INDEX idx_email (email),
            INDEX idx_ip_address (ip_address),
            INDEX idx_attempt_time (attempt_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql_attempts);
    echo "✅ brute_force_attempts table created successfully<br>";
    
    // Create account_lockouts table
    echo "<h2>Creating account_lockouts table...</h2>";
    $sql_lockouts = "
        CREATE TABLE IF NOT EXISTS account_lockouts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            unlock_at TIMESTAMP NULL,
            attempt_count INT DEFAULT 0,
            ip_address VARCHAR(45),
            INDEX idx_email (email),
            INDEX idx_unlock_at (unlock_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql_lockouts);
    echo "✅ account_lockouts table created successfully<br>";
    
    // Verify tables exist
    echo "<h2>Verifying table creation...</h2>";
    
    $tables_to_check = ['brute_force_attempts', 'account_lockouts'];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists<br>";
            
            // Show table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            echo "<strong>$table structure:</strong><br>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            foreach ($columns as $col) {
                echo "<tr>";
                echo "<td>" . $col['Field'] . "</td>";
                echo "<td>" . $col['Type'] . "</td>";
                echo "<td>" . $col['Null'] . "</td>";
                echo "<td>" . $col['Key'] . "</td>";
                echo "<td>" . $col['Default'] . "</td>";
                echo "<td>" . $col['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    }
    
    // Test insert operations
    echo "<h2>Testing table operations...</h2>";
    
    // Test brute_force_attempts insert
    try {
        $test_email = '<EMAIL>';
        $test_ip = '127.0.0.1';
        $test_agent = 'Test User Agent';
        
        $stmt = $pdo->prepare("INSERT INTO brute_force_attempts (email, ip_address, user_agent) VALUES (?, ?, ?)");
        $stmt->execute([$test_email, $test_ip, $test_agent]);
        echo "✅ brute_force_attempts insert test successful<br>";
        
        // Clean up test data
        $pdo->prepare("DELETE FROM brute_force_attempts WHERE email = ?")->execute([$test_email]);
        echo "✅ Test data cleaned up<br>";
        
    } catch (Exception $e) {
        echo "❌ brute_force_attempts insert test failed: " . $e->getMessage() . "<br>";
    }
    
    // Test account_lockouts insert
    try {
        $test_email = '<EMAIL>';
        $unlock_time = date('Y-m-d H:i:s', time() + 300); // 5 minutes from now
        
        $stmt = $pdo->prepare("INSERT INTO account_lockouts (email, unlock_at, attempt_count, ip_address) VALUES (?, ?, ?, ?)");
        $stmt->execute([$test_email, $unlock_time, 5, '127.0.0.1']);
        echo "✅ account_lockouts insert test successful<br>";
        
        // Test select
        $stmt = $pdo->prepare("SELECT * FROM account_lockouts WHERE email = ?");
        $stmt->execute([$test_email]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "✅ account_lockouts select test successful<br>";
            echo "Test record: Email=" . $result['email'] . ", Unlock=" . $result['unlock_at'] . "<br>";
        }
        
        // Clean up test data
        $pdo->prepare("DELETE FROM account_lockouts WHERE email = ?")->execute([$test_email]);
        echo "✅ Test data cleaned up<br>";
        
    } catch (Exception $e) {
        echo "❌ account_lockouts insert test failed: " . $e->getMessage() . "<br>";
    }
    
    // Test the BruteForceProtection class
    echo "<h2>Testing BruteForceProtection class...</h2>";
    
    try {
        require 'brute_force_protection.php';
        $bruteForceProtection = new BruteForceProtection($pdo);
        echo "✅ BruteForceProtection class loaded successfully<br>";
        
        // Test basic functionality
        $test_email = '<EMAIL>';
        
        // Test isAccountLocked
        $lockout_status = $bruteForceProtection->isAccountLocked($test_email);
        echo "✅ isAccountLocked method working: " . ($lockout_status['locked'] ? 'Locked' : 'Not locked') . "<br>";
        
        // Test recordFailedAttempt
        $attempt_result = $bruteForceProtection->recordFailedAttempt($test_email);
        echo "✅ recordFailedAttempt method working: " . $attempt_result['attempt_count'] . " attempts recorded<br>";
        
        // Test clearFailedAttempts
        $bruteForceProtection->clearFailedAttempts($test_email);
        echo "✅ clearFailedAttempts method working<br>";
        
        // Test getLockoutStats
        $stats = $bruteForceProtection->getLockoutStats();
        echo "✅ getLockoutStats method working: " . json_encode($stats) . "<br>";
        
    } catch (Exception $e) {
        echo "❌ BruteForceProtection class test failed: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ Setup Complete!</h2>";
    echo "<p><strong>All brute force protection tables and functionality are ready to use.</strong></p>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ul>";
    echo "<li><a href='../auth/test_brute_force.php'>Run Brute Force Test</a></li>";
    echo "<li><a href='../auth/signin.php'>Test Login with Wrong Password</a></li>";
    echo "<li><a href='../admin/brute_force_management.php'>View Admin Management</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Setup failed: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
