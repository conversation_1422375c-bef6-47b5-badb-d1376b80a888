<?php
/**
 * Enhanced Logger for TryMeOut Platform
 * Comprehensive logging system for security monitoring
 */

class EnhancedLogger {
    private $pdo;
    private $user_id;
    private $username;
    private $ip_address;
    private $user_agent;
    private $country;
    private $city;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->ip_address = $this->getRealIpAddress();
        $this->user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        // Get user info if logged in
        if (isset($_SESSION['user_id'])) {
            $this->user_id = $_SESSION['user_id'];
            $this->username = $_SESSION['username'] ?? 'Unknown';
        }
        
        // Get location info (simplified for now)
        $this->country = 'Unknown';
        $this->city = 'Unknown';
    }

    /**
     * Log file/directory access attempts
     */
    public function logAccess($request_uri, $access_type, $response_code, $details = []) {
        try {
            $file_path = null;
            $directory_path = null;
            $blocked = false;
            $block_reason = null;
            $risk_level = 'LOW';

            // Determine if this is a file or directory access
            if ($access_type === 'DIRECTORY') {
                $directory_path = $request_uri;
                
                // Check for suspicious directory access
                if ($this->isSuspiciousDirectoryAccess($request_uri)) {
                    $risk_level = 'HIGH';
                    $blocked = true;
                    $block_reason = 'Suspicious directory access pattern';
                }
            } else {
                $file_path = $request_uri;
                
                // Check for suspicious file access
                if ($this->isSuspiciousFileAccess($request_uri)) {
                    $risk_level = 'MEDIUM';
                }
            }

            // Determine risk level based on response code
            if ($response_code >= 400) {
                $risk_level = $response_code == 403 ? 'MEDIUM' : 'HIGH';
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO access_logs (
                    user_id, username, ip_address, country, city, user_agent,
                    request_method, request_uri, file_path, directory_path,
                    access_type, response_code, blocked, block_reason,
                    risk_level, details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $this->user_id,
                $this->username,
                $this->ip_address,
                $this->country,
                $this->city,
                $this->user_agent,
                $_SERVER['REQUEST_METHOD'] ?? 'GET',
                $request_uri,
                $file_path,
                $directory_path,
                $access_type,
                $response_code,
                $blocked,
                $block_reason,
                $risk_level,
                json_encode($details)
            ]);

            // If high risk, also log as suspicious activity
            if ($risk_level === 'HIGH' || $risk_level === 'CRITICAL') {
                $this->logSuspiciousActivity('UNAUTHORIZED_ACCESS', $risk_level, 
                    "Suspicious access to: $request_uri", [
                        'request_uri' => $request_uri,
                        'response_code' => $response_code,
                        'access_type' => $access_type
                    ]);
            }

        } catch (Exception $e) {
            error_log("Enhanced Logger Error: " . $e->getMessage());
        }
    }

    /**
     * Log file operations (upload, download, delete, etc.)
     */
    public function logFileOperation($operation_type, $file_path, $file_name, $success, $details = []) {
        try {
            $risk_level = 'LOW';
            
            // Determine risk level based on operation and file type
            if ($operation_type === 'UPLOAD') {
                $risk_level = $this->assessUploadRisk($file_name, $details);
            } elseif ($operation_type === 'DELETE') {
                $risk_level = 'MEDIUM';
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO file_operations_logs (
                    user_id, username, ip_address, operation_type, file_path,
                    file_name, file_size, file_type, success, error_message,
                    risk_level, details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $this->user_id,
                $this->username,
                $this->ip_address,
                $operation_type,
                $file_path,
                $file_name,
                $details['file_size'] ?? null,
                $details['file_type'] ?? null,
                $success,
                $details['error_message'] ?? null,
                $risk_level,
                json_encode($details)
            ]);

        } catch (Exception $e) {
            error_log("File Operation Logger Error: " . $e->getMessage());
        }
    }

    /**
     * Log suspicious activities
     */
    public function logSuspiciousActivity($activity_type, $severity, $description, $request_data = []) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO suspicious_activity_logs (
                    user_id, username, ip_address, activity_type, severity,
                    description, request_data, auto_blocked, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $auto_blocked = ($severity === 'CRITICAL' || $severity === 'HIGH');

            $stmt->execute([
                $this->user_id,
                $this->username,
                $this->ip_address,
                $activity_type,
                $severity,
                $description,
                json_encode($request_data),
                $auto_blocked
            ]);

        } catch (Exception $e) {
            error_log("Suspicious Activity Logger Error: " . $e->getMessage());
        }
    }

    /**
     * Log admin actions
     */
    public function logAdminAction($action_type, $description, $target_user_id = null, $old_values = [], $new_values = []) {
        try {
            if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
                return; // Only log admin actions
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO admin_actions_logs (
                    admin_id, admin_username, action_type, action_description,
                    target_user_id, target_username, old_values, new_values,
                    ip_address, user_agent, success, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            // Get target username if target_user_id is provided
            $target_username = null;
            if ($target_user_id) {
                $user_stmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
                $user_stmt->execute([$target_user_id]);
                $target_username = $user_stmt->fetchColumn();
            }

            $stmt->execute([
                $this->user_id,
                $this->username,
                $action_type,
                $description,
                $target_user_id,
                $target_username,
                json_encode($old_values),
                json_encode($new_values),
                $this->ip_address,
                $this->user_agent,
                true
            ]);

        } catch (Exception $e) {
            error_log("Admin Action Logger Error: " . $e->getMessage());
        }
    }

    /**
     * Log performance metrics
     */
    public function logPerformance($metric_type, $metric_name, $value, $unit, $context = []) {
        try {
            $threshold_exceeded = $this->checkPerformanceThreshold($metric_type, $value);

            $stmt = $this->pdo->prepare("
                INSERT INTO performance_logs (
                    metric_type, metric_name, value, unit, threshold_exceeded,
                    context, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $metric_type,
                $metric_name,
                $value,
                $unit,
                $threshold_exceeded,
                json_encode($context)
            ]);

        } catch (Exception $e) {
            error_log("Performance Logger Error: " . $e->getMessage());
        }
    }

    /**
     * Check if directory access is suspicious
     */
    private function isSuspiciousDirectoryAccess($uri) {
        $suspicious_patterns = [
            '/\.\./', // Directory traversal
            '/config/', // Config directory
            '/database/', // Database directory
            '/admin/', // Admin directory (if not admin)
            '/\.git/', // Git directory
            '/\.env', // Environment files
            '/backup/', // Backup directories
        ];

        foreach ($suspicious_patterns as $pattern) {
            if (preg_match($pattern, $uri)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if file access is suspicious
     */
    private function isSuspiciousFileAccess($uri) {
        $suspicious_extensions = ['.sql', '.bak', '.log', '.ini', '.conf', '.env'];
        
        foreach ($suspicious_extensions as $ext) {
            if (str_ends_with(strtolower($uri), $ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Assess upload risk level
     */
    private function assessUploadRisk($filename, $details) {
        $dangerous_extensions = ['.php', '.exe', '.bat', '.sh', '.js', '.html'];
        
        foreach ($dangerous_extensions as $ext) {
            if (str_ends_with(strtolower($filename), $ext)) {
                return 'HIGH';
            }
        }

        if (isset($details['file_size']) && $details['file_size'] > 10 * 1024 * 1024) { // 10MB
            return 'MEDIUM';
        }

        return 'LOW';
    }

    /**
     * Check performance thresholds
     */
    private function checkPerformanceThreshold($metric_type, $value) {
        $thresholds = [
            'PAGE_LOAD' => 3.0, // seconds
            'DATABASE_QUERY' => 1.0, // seconds
            'MEMORY_USAGE' => 128, // MB
            'CPU_USAGE' => 80 // percentage
        ];

        return isset($thresholds[$metric_type]) && $value > $thresholds[$metric_type];
    }

    /**
     * Get real IP address
     */
    private function getRealIpAddress() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
?>
