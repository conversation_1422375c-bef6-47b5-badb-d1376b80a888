<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Database Structure Checker - TryMeOut Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: 'Segoe UI', sans-serif; min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px; }
        .checker-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 25px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(255,255,255,0.2); }
        .table-card { background: #f8f9fa; padding: 20px; border-radius: 15px; margin: 15px 0; border-left: 4px solid #28a745; }
        .column-info { background: #e3f2fd; padding: 10px; border-radius: 8px; margin: 5px 0; }
        .status-success { background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #28a745; }
        .status-warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
        .status-error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="text-white display-4 mb-3">🔍 Database Structure Checker</h1>
            <p class="text-white lead">Analyze Your Database Structure for Compatibility</p>
        </div>

        <?php
        require_once 'db_connect.php';
        
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo '<div class="status-success">';
            echo '<h6><i class="fas fa-check-circle text-success"></i> Database Connection Successful</h6>';
            echo '<p>Connected to database: <strong>' . htmlspecialchars($dbname) . '</strong></p>';
            echo '</div>';
            
        } catch (PDOException $e) {
            echo '<div class="status-error">';
            echo '<h6><i class="fas fa-exclamation-triangle text-danger"></i> Database Connection Failed</h6>';
            echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
            exit;
        }
        ?>

        <!-- Database Overview -->
        <div class="checker-card">
            <h2><i class="fas fa-database text-primary"></i> Database Overview</h2>
            
            <?php
            try {
                // Get all tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                echo '<div class="status-success">';
                echo '<h6>Tables Found: ' . count($tables) . '</h6>';
                echo '<p>Database contains the following tables:</p>';
                echo '<ul>';
                foreach ($tables as $table) {
                    echo '<li><strong>' . htmlspecialchars($table) . '</strong></li>';
                }
                echo '</ul>';
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<div class="status-error">Error getting tables: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <!-- Table Structure Analysis -->
        <div class="checker-card">
            <h2><i class="fas fa-table text-info"></i> Table Structure Analysis</h2>
            
            <?php
            $expected_tables = [
                'users' => ['id', 'username', 'email', 'password', 'role', 'created_at'],
                'categories' => ['id', 'name', 'description'],
                'challenges' => ['id', 'title', 'description', 'category_id', 'difficulty'],
                'user_progress' => ['id', 'user_id', 'challenge_id', 'status', 'completed_at'],
                'user_badges' => ['id', 'user_id', 'earned_at'],
                'user_certificates' => ['id', 'user_id', 'category_id', 'certificate_code', 'issued_at']
            ];
            
            foreach ($expected_tables as $table_name => $expected_columns) {
                echo '<div class="table-card">';
                echo '<h5><i class="fas fa-table text-primary"></i> ' . htmlspecialchars($table_name) . '</h5>';
                
                try {
                    // Check if table exists
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table_name'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="status-success">✅ Table exists</div>';
                        
                        // Get table structure
                        $stmt = $pdo->query("DESCRIBE $table_name");
                        $actual_columns = $stmt->fetchAll();
                        
                        echo '<h6>Actual Columns:</h6>';
                        echo '<div class="row">';
                        foreach ($actual_columns as $column) {
                            $is_expected = in_array($column['Field'], $expected_columns);
                            $badge_class = $is_expected ? 'bg-success' : 'bg-info';
                            
                            echo '<div class="col-md-6 mb-2">';
                            echo '<div class="column-info">';
                            echo '<span class="badge ' . $badge_class . ' me-2">' . htmlspecialchars($column['Field']) . '</span>';
                            echo '<small>(' . htmlspecialchars($column['Type']) . ')</small>';
                            if ($column['Key'] === 'PRI') echo ' <span class="badge bg-warning">PK</span>';
                            if ($column['Null'] === 'NO') echo ' <span class="badge bg-secondary">NOT NULL</span>';
                            echo '</div>';
                            echo '</div>';
                        }
                        echo '</div>';
                        
                        // Check for missing expected columns
                        $actual_column_names = array_column($actual_columns, 'Field');
                        $missing_columns = array_diff($expected_columns, $actual_column_names);
                        
                        if (!empty($missing_columns)) {
                            echo '<div class="status-warning">';
                            echo '<h6>⚠️ Missing Expected Columns:</h6>';
                            echo '<ul>';
                            foreach ($missing_columns as $missing) {
                                echo '<li>' . htmlspecialchars($missing) . '</li>';
                            }
                            echo '</ul>';
                            echo '</div>';
                        }
                        
                        // Get row count
                        $stmt = $pdo->query("SELECT COUNT(*) FROM $table_name");
                        $row_count = $stmt->fetchColumn();
                        echo '<p><strong>Row Count:</strong> ' . number_format($row_count) . '</p>';
                        
                    } else {
                        echo '<div class="status-error">❌ Table does not exist</div>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="status-error">Error checking table: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
                
                echo '</div>';
            }
            ?>
        </div>

        <!-- Compatibility Issues -->
        <div class="checker-card">
            <h2><i class="fas fa-exclamation-triangle text-warning"></i> Compatibility Analysis</h2>
            
            <?php
            $issues = [];
            $recommendations = [];
            
            // Check for common issues
            try {
                // Check user_badges table structure
                $stmt = $pdo->query("SHOW TABLES LIKE 'user_badges'");
                if ($stmt->rowCount() > 0) {
                    $stmt = $pdo->query("DESCRIBE user_badges");
                    $badge_columns = array_column($stmt->fetchAll(), 'Field');
                    
                    if (!in_array('category_id', $badge_columns)) {
                        $issues[] = "user_badges table missing 'category_id' column";
                        $recommendations[] = "Add category_id column to user_badges table or modify queries to work without it";
                    }
                } else {
                    $issues[] = "user_badges table does not exist";
                    $recommendations[] = "Create user_badges table or disable badge functionality";
                }
                
                // Check categories table
                $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
                if ($stmt->rowCount() > 0) {
                    $stmt = $pdo->query("DESCRIBE categories");
                    $category_columns = array_column($stmt->fetchAll(), 'Field');
                    
                    if (!in_array('color', $category_columns)) {
                        $issues[] = "categories table missing 'color' column";
                        $recommendations[] = "Add color column to categories table or use default colors";
                    }
                    
                    if (!in_array('icon', $category_columns)) {
                        $issues[] = "categories table missing 'icon' column";
                        $recommendations[] = "Add icon column to categories table or use default icons";
                    }
                } else {
                    $issues[] = "categories table does not exist";
                    $recommendations[] = "Create categories table for proper categorization";
                }
                
                // Check user_certificates table
                $stmt = $pdo->query("SHOW TABLES LIKE 'user_certificates'");
                if ($stmt->rowCount() == 0) {
                    $issues[] = "user_certificates table does not exist";
                    $recommendations[] = "Create user_certificates table for certificate functionality";
                }
                
            } catch (Exception $e) {
                $issues[] = "Error during compatibility check: " . $e->getMessage();
            }
            
            if (empty($issues)) {
                echo '<div class="status-success">';
                echo '<h6>✅ No Compatibility Issues Found</h6>';
                echo '<p>Your database structure is compatible with the admin dashboard.</p>';
                echo '</div>';
            } else {
                echo '<div class="status-warning">';
                echo '<h6>⚠️ Compatibility Issues Found</h6>';
                echo '<ul>';
                foreach ($issues as $issue) {
                    echo '<li>' . htmlspecialchars($issue) . '</li>';
                }
                echo '</ul>';
                echo '</div>';
                
                echo '<div class="status-success">';
                echo '<h6>💡 Recommendations</h6>';
                echo '<ul>';
                foreach ($recommendations as $rec) {
                    echo '<li>' . htmlspecialchars($rec) . '</li>';
                }
                echo '</ul>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- SQL Fixes -->
        <div class="checker-card">
            <h2><i class="fas fa-tools text-success"></i> Suggested SQL Fixes</h2>
            
            <div class="status-success">
                <h6>🔧 Optional Database Improvements</h6>
                <p>Run these SQL commands to add missing columns for enhanced functionality:</p>
            </div>

            <div class="bg-dark text-light p-3 rounded">
                <h6>Add color and icon columns to categories:</h6>
                <code>
ALTER TABLE categories ADD COLUMN color VARCHAR(7) DEFAULT '#667eea';<br>
ALTER TABLE categories ADD COLUMN icon VARCHAR(50) DEFAULT 'fas fa-puzzle-piece';<br><br>

UPDATE categories SET color = '#dc3545', icon = 'fas fa-database' WHERE name LIKE '%SQL%';<br>
UPDATE categories SET color = '#fd7e14', icon = 'fas fa-code' WHERE name LIKE '%XSS%';<br>
UPDATE categories SET color = '#28a745', icon = 'fas fa-terminal' WHERE name LIKE '%Command%';
                </code>
            </div>

            <div class="bg-dark text-light p-3 rounded mt-3">
                <h6>Add category_id to user_badges (if needed):</h6>
                <code>
ALTER TABLE user_badges ADD COLUMN category_id INT;<br>
ALTER TABLE user_badges ADD FOREIGN KEY (category_id) REFERENCES categories(id);
                </code>
            </div>

            <div class="bg-dark text-light p-3 rounded mt-3">
                <h6>Create user_certificates table (if missing):</h6>
                <code>
CREATE TABLE user_certificates (<br>
&nbsp;&nbsp;&nbsp;&nbsp;id INT AUTO_INCREMENT PRIMARY KEY,<br>
&nbsp;&nbsp;&nbsp;&nbsp;user_id INT NOT NULL,<br>
&nbsp;&nbsp;&nbsp;&nbsp;category_id INT NOT NULL,<br>
&nbsp;&nbsp;&nbsp;&nbsp;certificate_code VARCHAR(20) UNIQUE NOT NULL,<br>
&nbsp;&nbsp;&nbsp;&nbsp;issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,<br>
&nbsp;&nbsp;&nbsp;&nbsp;download_count INT DEFAULT 0,<br>
&nbsp;&nbsp;&nbsp;&nbsp;FOREIGN KEY (user_id) REFERENCES users(id),<br>
&nbsp;&nbsp;&nbsp;&nbsp;FOREIGN KEY (category_id) REFERENCES categories(id)<br>
);
                </code>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center">
            <a href="../admin/dashboard.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
            </a>
            <a href="mcp_database_interface.php" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-database"></i> MCP Interface
            </a>
            <a href="http://localhost/phpmyadmin" class="btn btn-outline-light btn-lg" target="_blank">
                <i class="fas fa-external-link-alt"></i> phpMyAdmin
            </a>
        </div>
    </div>
</body>
</html>
