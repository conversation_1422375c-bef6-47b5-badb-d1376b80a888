<?php
/**
 * Enhanced Logging System with IP Geolocation and Anomaly Detection
 * Provides comprehensive logging for security events, user activities, and system monitoring
 */

class SecurityLogger {
    private $pdo;
    private $ip_cache = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->initializeTables();
    }
    
    /**
     * Initialize logging tables if they don't exist
     */
    private function initializeTables() {
        try {
            // Enhanced security logs table
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS security_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_type VARCHAR(50) NOT NULL,
                    username VARCHAR(100),
                    user_id INT,
                    ip_address VARCHAR(45) NOT NULL,
                    country VARCHAR(100),
                    city VARCHAR(100),
                    user_agent TEXT,
                    session_id VARCHAR(255),
                    risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
                    details JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_event_type (event_type),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_created_at (created_at),
                    INDEX idx_risk_level (risk_level)
                )
            ");
            
            // Login attempts tracking
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS login_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    country VARCHAR(100),
                    city VARCHAR(100),
                    user_agent TEXT,
                    success BOOLEAN DEFAULT FALSE,
                    failure_reason VARCHAR(255),
                    session_duration INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_created_at (created_at)
                )
            ");
            
            // Anomaly detection table
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS anomaly_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    anomaly_type VARCHAR(50) NOT NULL,
                    user_id INT,
                    ip_address VARCHAR(45),
                    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                    description TEXT,
                    data JSON,
                    resolved BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_anomaly_type (anomaly_type),
                    INDEX idx_severity (severity),
                    INDEX idx_resolved (resolved)
                )
            ");
            
        } catch (Exception $e) {
            error_log("Logger initialization error: " . $e->getMessage());
        }
    }
    
    /**
     * Get IP geolocation information
     */
    private function getIpLocation($ip) {
        // Skip for localhost/private IPs
        if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
            return ['country' => 'Local', 'city' => 'Localhost'];
        }
        
        // Check cache first
        if (isset($this->ip_cache[$ip])) {
            return $this->ip_cache[$ip];
        }
        
        try {
            // Using ip-api.com (free service, 1000 requests/month)
            $url = "http://ip-api.com/json/{$ip}?fields=status,country,city,query";
            $context = stream_context_create([
                'http' => [
                    'timeout' => 3,
                    'user_agent' => 'TryMeOut Security Logger'
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            if ($response) {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    $location = [
                        'country' => $data['country'] ?? 'Unknown',
                        'city' => $data['city'] ?? 'Unknown'
                    ];
                    $this->ip_cache[$ip] = $location;
                    return $location;
                }
            }
        } catch (Exception $e) {
            error_log("IP geolocation error: " . $e->getMessage());
        }
        
        return ['country' => 'Unknown', 'city' => 'Unknown'];
    }
    
    /**
     * Log login attempt
     */
    public function logLoginAttempt($email, $success, $user_id = null, $failure_reason = null) {
        $ip = $this->getClientIp();
        $location = $this->getIpLocation($ip);
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO login_attempts (email, user_id, ip_address, country, city, user_agent, success, failure_reason)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $email,
                $user_id,
                $ip,
                $location['country'],
                $location['city'],
                $user_agent,
                $success,
                $failure_reason
            ]);
            
            // Log security event
            $this->logSecurityEvent(
                $success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
                $email,
                $user_id,
                $success ? 'LOW' : 'MEDIUM',
                [
                    'email' => $email,
                    'failure_reason' => $failure_reason,
                    'user_agent' => $user_agent
                ]
            );
            
            // Check for anomalies
            $this->checkLoginAnomalies($email, $ip);
            
        } catch (Exception $e) {
            error_log("Login attempt logging error: " . $e->getMessage());
            // Also display error if we're in debug mode
            if (defined('DEBUG_LOGGING') && DEBUG_LOGGING) {
                echo "Login logging error: " . $e->getMessage() . "<br>";
                echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
            }
        }
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent($event_type, $username = null, $user_id = null, $risk_level = 'LOW', $details = []) {
        $ip = $this->getClientIp();
        $location = $this->getIpLocation($ip);
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $session_id = session_id();
        
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO security_logs (event_type, username, user_id, ip_address, country, city, 
                                         user_agent, session_id, risk_level, details)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_type,
                $username,
                $user_id,
                $ip,
                $location['country'],
                $location['city'],
                $user_agent,
                $session_id,
                $risk_level,
                json_encode($details)
            ]);
            
        } catch (Exception $e) {
            error_log("Security event logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for login anomalies
     */
    private function checkLoginAnomalies($email, $ip) {
        try {
            // Check for multiple failed attempts
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as failed_count 
                FROM login_attempts 
                WHERE email = ? AND success = FALSE 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            $stmt->execute([$email]);
            $failed_count = $stmt->fetchColumn();
            
            if ($failed_count >= 5) {
                $this->logAnomaly('BRUTE_FORCE_ATTEMPT', null, 'HIGH', 
                    "Multiple failed login attempts for email: {$email}", [
                        'email' => $email,
                        'failed_attempts' => $failed_count,
                        'time_window' => '15 minutes'
                    ]
                );
            }
            
            // Check for logins from multiple countries
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT country 
                FROM login_attempts 
                WHERE email = ? AND success = TRUE 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute([$email]);
            $countries = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (count($countries) > 1) {
                $this->logAnomaly('GEOGRAPHIC_ANOMALY', null, 'MEDIUM', 
                    "Login from multiple countries within 1 hour", [
                        'email' => $email,
                        'countries' => $countries
                    ]
                );
            }
            
            // Check for unusual login times
            $hour = date('H');
            if ($hour >= 2 && $hour <= 5) {
                $this->logAnomaly('UNUSUAL_TIME_LOGIN', null, 'LOW', 
                    "Login during unusual hours (2-5 AM)", [
                        'email' => $email,
                        'hour' => $hour
                    ]
                );
            }
            
        } catch (Exception $e) {
            error_log("Anomaly detection error: " . $e->getMessage());
        }
    }
    
    /**
     * Log anomaly
     */
    public function logAnomaly($type, $user_id = null, $severity = 'MEDIUM', $description = '', $data = []) {
        $ip = $this->getClientIp();
        
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO anomaly_logs (anomaly_type, user_id, ip_address, severity, description, data)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $type,
                $user_id,
                $ip,
                $severity,
                $description,
                json_encode($data)
            ]);
            
        } catch (Exception $e) {
            error_log("Anomaly logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Get client IP address
     */
    private function getClientIp() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Log user activity
     */
    public function logUserActivity($activity, $user_id, $details = []) {
        $this->logSecurityEvent('USER_ACTIVITY', null, $user_id, 'LOW', array_merge([
            'activity' => $activity
        ], $details));
    }
    
    /**
     * Log system event
     */
    public function logSystemEvent($event, $level = 'INFO', $details = []) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO system_logs (category, log_level, message, ip_address, details, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                'SECURITY',
                $level,
                $event,
                $this->getClientIp(),
                json_encode($details)
            ]);
        } catch (Exception $e) {
            error_log("System event logging error: " . $e->getMessage());
        }
    }

    /**
     * Log audit event for data changes
     */
    public function logAuditEvent($action, $table_name, $record_id, $username, $old_values = [], $new_values = []) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO audit_logs (action_type, table_name, record_id, username, old_values, new_values, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $action,
                $table_name,
                $record_id,
                $username,
                json_encode($old_values),
                json_encode($new_values),
                $this->getClientIp(),
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log("Audit event logging error: " . $e->getMessage());
        }
    }

    /**
     * Log user profile change
     */
    public function logUserProfileChange($user_id, $username, $field, $old_value, $new_value, $action_type = 'UPDATE') {
        $this->logAuditEvent(
            $action_type,
            'users',
            $user_id,
            $username,
            [$field => $old_value],
            [$field => $new_value]
        );

        // Also log as security event for profile changes
        if (in_array($field, ['password_hash', 'email', 'role', 'username'])) {
            $event_type = $field === 'username' ? 'USERNAME_CHANGED' : 'PROFILE_CHANGE_' . strtoupper($field);
            $risk_level = $field === 'password_hash' ? 'MEDIUM' : 'LOW';

            $this->logSecurityEvent(
                $event_type,
                $username,
                $user_id,
                $risk_level,
                [
                    'field_changed' => $field,
                    'action' => $action_type,
                    'old_value_hash' => $field === 'password_hash' ? 'REDACTED' : substr(md5($old_value), 0, 8),
                    'new_value_hash' => $field === 'password_hash' ? 'REDACTED' : substr(md5($new_value), 0, 8),
                    'old_value' => $field === 'username' ? $old_value : 'REDACTED',
                    'new_value' => $field === 'username' ? $new_value : 'REDACTED'
                ]
            );
        }
    }
}

// Global logger instance
function getLogger() {
    global $pdo;
    static $logger = null;
    if ($logger === null) {
        $logger = new SecurityLogger($pdo);
    }
    return $logger;
}
?>
