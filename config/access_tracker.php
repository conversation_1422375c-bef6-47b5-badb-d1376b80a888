<?php
/**
 * Access Tracker for TryMeOut Platform
 * Automatically logs directory and file access attempts
 */

// Only track if enhanced logging is available
if (file_exists(__DIR__ . '/enhanced_logger.php')) {
    require_once __DIR__ . '/enhanced_logger.php';
    require_once __DIR__ . '/db_connect.php';
    
    try {
        // Check if access_logs table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'access_logs'");
        if ($stmt->rowCount() > 0) {
            
            // Initialize the enhanced logger
            $logger = new EnhancedLogger($pdo);
            
            // Get request information
            $request_uri = $_SERVER['REQUEST_URI'] ?? '/';
            $response_code = http_response_code() ?: 403; // Default to 403 for blocked access
            
            // Determine access type
            $access_type = 'DIRECTORY'; // Default for directory access
            if (strpos($request_uri, '.') !== false) {
                $access_type = 'FILE';
            }
            
            // Additional details
            $details = [
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'referer' => $_SERVER['HTTP_REFERER'] ?? null,
                'request_time' => $_SERVER['REQUEST_TIME'] ?? time(),
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
                'blocked_by' => 'Directory Protection',
                'protection_type' => 'htaccess + index.php'
            ];
            
            // Log the access attempt
            $logger->logAccess($request_uri, $access_type, $response_code, $details);
            
            // If this is a suspicious access pattern, log additional details
            if ($response_code >= 400) {
                $suspicious_patterns = [
                    '/config/' => 'Configuration directory access',
                    '/database/' => 'Database directory access',
                    '/admin/' => 'Admin directory access (unauthorized)',
                    '/\.git/' => 'Git repository access attempt',
                    '/\.env' => 'Environment file access attempt',
                    '/backup/' => 'Backup directory access',
                    '/logs/' => 'Log directory access',
                    '/uploads/' => 'Upload directory browsing'
                ];
                
                foreach ($suspicious_patterns as $pattern => $description) {
                    if (preg_match($pattern, $request_uri)) {
                        $logger->logSuspiciousActivity(
                            'UNAUTHORIZED_ACCESS',
                            'MEDIUM',
                            $description . ': ' . $request_uri,
                            [
                                'request_uri' => $request_uri,
                                'response_code' => $response_code,
                                'access_type' => $access_type,
                                'pattern_matched' => $pattern
                            ]
                        );
                        break;
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Silently fail - don't break the error page
        error_log("Access Tracker Error: " . $e->getMessage());
    }
}
?>
