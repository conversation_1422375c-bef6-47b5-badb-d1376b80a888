<?php
/**
 * Upload Configuration and Security Settings
 * Centralized configuration for all file upload operations
 */

// File Upload Security Configuration
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB in bytes
define('UPLOAD_DIR', dirname(__DIR__) . '/assets/uploads/');
define('UPLOAD_URL_PATH', 'assets/uploads/');

// Allowed file types for profile pictures
define('ALLOWED_IMAGE_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_IMAGE_MIME_TYPES', [
    'image/jpeg',
    'image/jpg',
    'image/png', 
    'image/gif'
]);

// Image dimension limits
define('MAX_IMAGE_WIDTH', 2000);
define('MAX_IMAGE_HEIGHT', 2000);

// Default profile picture
define('DEFAULT_PROFILE_PICTURE', 'assets/uploads/default.jpg');

// Security patterns to detect malicious content
define('MALICIOUS_PATTERNS', [
    '<?php',
    '<?=',
    '<script',
    'javascript:',
    'vbscript:',
    'onload=',
    'onerror=',
    'eval(',
    'base64_decode',
    'shell_exec',
    'system(',
    'exec(',
    'passthru(',
    'file_get_contents',
    'file_put_contents',
    'fopen(',
    'fwrite(',
    'include(',
    'require(',
    'include_once(',
    'require_once('
]);

/**
 * Initialize upload directory with proper permissions
 */
function initializeUploadDirectory() {
    if (!is_dir(UPLOAD_DIR)) {
        if (!mkdir(UPLOAD_DIR, 0755, true)) {
            throw new Exception("Failed to create upload directory: " . UPLOAD_DIR);
        }
    }
    
    // Create .htaccess file for additional security
    $htaccess_content = "# Security rules for upload directory\n";
    $htaccess_content .= "Options -Indexes\n";
    $htaccess_content .= "Options -ExecCGI\n";
    $htaccess_content .= "AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\n";
    $htaccess_content .= "\n# Deny access to potentially dangerous files\n";
    $htaccess_content .= "<FilesMatch \"\\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$\">\n";
    $htaccess_content .= "    Order Allow,Deny\n";
    $htaccess_content .= "    Deny from all\n";
    $htaccess_content .= "</FilesMatch>\n";
    $htaccess_content .= "\n# Only allow image files\n";
    $htaccess_content .= "<FilesMatch \"\\.(jpg|jpeg|png|gif)$\">\n";
    $htaccess_content .= "    Order Allow,Deny\n";
    $htaccess_content .= "    Allow from all\n";
    $htaccess_content .= "</FilesMatch>\n";
    
    $htaccess_file = UPLOAD_DIR . '.htaccess';
    if (!file_exists($htaccess_file)) {
        file_put_contents($htaccess_file, $htaccess_content);
    }
}

/**
 * Get formatted file size
 */
function formatFileSize($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Get maximum upload size from PHP configuration
 */
function getMaxUploadSize() {
    $max_upload = ini_get('upload_max_filesize');
    $max_post = ini_get('post_max_size');
    $memory_limit = ini_get('memory_limit');
    
    // Convert to bytes
    $max_upload_bytes = convertToBytes($max_upload);
    $max_post_bytes = convertToBytes($max_post);
    $memory_limit_bytes = convertToBytes($memory_limit);
    
    // Return the smallest value
    return min($max_upload_bytes, $max_post_bytes, $memory_limit_bytes, UPLOAD_MAX_SIZE);
}

/**
 * Convert PHP ini values to bytes
 */
function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int) $value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}

// Initialize upload directory on include
try {
    initializeUploadDirectory();
} catch (Exception $e) {
    error_log("Upload directory initialization failed: " . $e->getMessage());
}
?>
