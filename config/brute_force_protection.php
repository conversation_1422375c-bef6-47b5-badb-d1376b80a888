<?php
/**
 * Brute Force Protection System
 * Implements account lockout and cooldown mechanisms
 */

class BruteForceProtection {
    private $pdo;
    private $max_attempts = 5;
    private $lockout_duration = 300; // 5 minutes in seconds
    
    public function __construct($pdo) {
        $this->pdo = $pdo;

        // Set consistent timezone for both PHP and MySQL
        date_default_timezone_set('Asia/Kuala_Lumpur');
        $this->pdo->exec("SET time_zone = '+08:00'");

        $this->createTables();
    }
    
    /**
     * Create necessary tables for brute force protection
     */
    private function createTables() {
        try {
            // Create brute_force_attempts table
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS brute_force_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_agent TEXT,
                    INDEX idx_email (email),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_attempt_time (attempt_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // Create account_lockouts table
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS account_lockouts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    unlock_at TIMESTAMP NULL,
                    attempt_count INT DEFAULT 0,
                    ip_address VARCHAR(45),
                    manually_unlocked TINYINT(1) DEFAULT 0,
                    unlocked_by VARCHAR(255) NULL,
                    unlocked_at TIMESTAMP NULL,
                    unlock_reason VARCHAR(500) NULL,
                    status ENUM('active', 'expired', 'manually_unlocked') DEFAULT 'active',
                    INDEX idx_email (email),
                    INDEX idx_unlock_at (unlock_at),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // Add new columns to existing table if they don't exist
            $this->addMissingColumns();

            // Verify tables were created
            $tables = ['brute_force_attempts', 'account_lockouts'];
            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SHOW TABLES LIKE '$table'");
                if (!$stmt->fetch()) {
                    throw new Exception("Failed to create table: $table");
                }
            }

        } catch (Exception $e) {
            error_log("Brute force protection table creation error: " . $e->getMessage());
            throw $e; // Re-throw to make the error visible
        }
    }

    /**
     * Add missing columns to existing account_lockouts table
     */
    private function addMissingColumns() {
        try {
            // Check if new columns exist and add them if missing
            $stmt = $this->pdo->query("DESCRIBE account_lockouts");
            $columns = array_column($stmt->fetchAll(), 'Field');

            $newColumns = [
                'manually_unlocked' => 'ADD COLUMN manually_unlocked TINYINT(1) DEFAULT 0',
                'unlocked_by' => 'ADD COLUMN unlocked_by VARCHAR(255) NULL',
                'unlocked_at' => 'ADD COLUMN unlocked_at TIMESTAMP NULL',
                'unlock_reason' => 'ADD COLUMN unlock_reason VARCHAR(500) NULL',
                'status' => 'ADD COLUMN status ENUM(\'active\', \'expired\', \'manually_unlocked\') DEFAULT \'active\''
            ];

            foreach ($newColumns as $columnName => $alterStatement) {
                if (!in_array($columnName, $columns)) {
                    $this->pdo->exec("ALTER TABLE account_lockouts $alterStatement");
                    error_log("Added missing column '$columnName' to account_lockouts table");
                }
            }

            // Add index for status if it doesn't exist
            try {
                $this->pdo->exec("ALTER TABLE account_lockouts ADD INDEX idx_status (status)");
            } catch (Exception $e) {
                // Index might already exist, ignore error
            }

        } catch (Exception $e) {
            error_log("Error adding missing columns: " . $e->getMessage());
            // Don't throw - this is not critical for basic functionality
        }
    }
    
    /**
     * Check if an account is currently locked
     */
    public function isAccountLocked($email) {
        try {
            // Debug: First check if any lockout record exists for this email
            $debug_stmt = $this->pdo->prepare("SELECT *, NOW() as now_time FROM account_lockouts WHERE email = ?");
            $debug_stmt->execute([$email]);
            $debug_lockout = $debug_stmt->fetch();

            if ($debug_lockout) {
                error_log("DEBUG: isAccountLocked - Found lockout record for $email:");
                error_log("DEBUG: - unlock_at: " . $debug_lockout['unlock_at']);
                error_log("DEBUG: - now_time: " . $debug_lockout['now_time']);
                error_log("DEBUG: - comparison: unlock_at > now_time = " . ($debug_lockout['unlock_at'] > $debug_lockout['now_time'] ? 'true' : 'false'));
            } else {
                error_log("DEBUG: isAccountLocked - No lockout record found for $email");
            }

            // Check for active lockouts only
            $stmt = $this->pdo->prepare("
                SELECT unlock_at, attempt_count, status, NOW() as now_time
                FROM account_lockouts
                WHERE email = ? AND status = 'active' AND unlock_at > NOW()
            ");
            $stmt->execute([$email]);
            $lockout = $stmt->fetch();

            if ($lockout) {
                error_log("DEBUG: isAccountLocked - Account IS locked for $email");
                return [
                    'locked' => true,
                    'unlock_at' => $lockout['unlock_at'],
                    'attempt_count' => $lockout['attempt_count'],
                    'remaining_time' => strtotime($lockout['unlock_at']) - time()
                ];
            } else {
                error_log("DEBUG: isAccountLocked - Account is NOT locked for $email (either no record or expired)");
            }

            // Clean up expired lockouts AFTER checking
            $this->cleanupExpiredLockouts();

            return ['locked' => false];

        } catch (Exception $e) {
            error_log("Account lockout check error: " . $e->getMessage());
            return ['locked' => false];
        }
    }
    
    /**
     * Record a failed login attempt
     */
    public function recordFailedAttempt($email, $ip_address = null, $user_agent = null) {
        try {
            $ip_address = $ip_address ?: $this->getClientIp();
            $user_agent = $user_agent ?: ($_SERVER['HTTP_USER_AGENT'] ?? '');
            
            // Record the failed attempt
            $stmt = $this->pdo->prepare("
                INSERT INTO brute_force_attempts (email, ip_address, user_agent) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$email, $ip_address, $user_agent]);
            
            // Count recent failed attempts (last 15 minutes)
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as attempt_count 
                FROM brute_force_attempts 
                WHERE email = ? AND attempt_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            $stmt->execute([$email]);
            $attempt_count = $stmt->fetchColumn();
            
            // If max attempts reached, lock the account
            if ($attempt_count >= $this->max_attempts) {
                $this->lockAccount($email, $ip_address, $attempt_count);
                return [
                    'locked' => true,
                    'attempt_count' => $attempt_count,
                    'lockout_duration' => $this->lockout_duration
                ];
            }
            
            return [
                'locked' => false,
                'attempt_count' => $attempt_count,
                'remaining_attempts' => $this->max_attempts - $attempt_count
            ];
            
        } catch (Exception $e) {
            error_log("Failed attempt recording error: " . $e->getMessage());
            return ['locked' => false, 'attempt_count' => 0];
        }
    }
    
    /**
     * Lock an account
     */
    private function lockAccount($email, $ip_address, $attempt_count) {
        try {
            // Use MySQL's DATE_ADD for consistent timezone handling
            // This ensures the unlock time is calculated using the database's timezone
            $stmt = $this->pdo->prepare("
                INSERT INTO account_lockouts (email, unlock_at, attempt_count, ip_address, locked_at, status)
                VALUES (?, DATE_ADD(NOW(), INTERVAL ? SECOND), ?, ?, NOW(), 'active')
                ON DUPLICATE KEY UPDATE
                    unlock_at = DATE_ADD(NOW(), INTERVAL ? SECOND),
                    attempt_count = VALUES(attempt_count),
                    ip_address = VALUES(ip_address),
                    locked_at = NOW(),
                    status = 'active',
                    manually_unlocked = 0,
                    unlocked_by = NULL,
                    unlocked_at = NULL,
                    unlock_reason = NULL
            ");
            $result = $stmt->execute([$email, $this->lockout_duration, $attempt_count, $ip_address, $this->lockout_duration]);

            // Debug logging - get the actual unlock time from database
            $debug_stmt = $this->pdo->prepare("SELECT unlock_at, NOW() as now_time FROM account_lockouts WHERE email = ?");
            $debug_stmt->execute([$email]);
            $debug_record = $debug_stmt->fetch();

            if ($debug_record) {
                error_log("DEBUG: Account locked successfully for $email");
                error_log("DEBUG: - unlock_at: " . $debug_record['unlock_at']);
                error_log("DEBUG: - now_time: " . $debug_record['now_time']);
                error_log("DEBUG: - lockout_duration: " . $this->lockout_duration . " seconds");
            }

            if ($result) {
                error_log("DEBUG: Account lockout record inserted/updated successfully for $email");
            } else {
                error_log("DEBUG: Failed to insert/update account lockout record for $email");
            }

            // Verify the record was created
            $verify_stmt = $this->pdo->prepare("SELECT * FROM account_lockouts WHERE email = ?");
            $verify_stmt->execute([$email]);
            $lockout_record = $verify_stmt->fetch();

            if ($lockout_record) {
                error_log("DEBUG: Lockout record verified in database for $email, unlock_at: " . $lockout_record['unlock_at']);
            } else {
                error_log("DEBUG: ERROR - No lockout record found after insert for $email");
            }

            // Log security event
            $this->logSecurityEvent('ACCOUNT_LOCKED', $email, [
                'attempt_count' => $attempt_count,
                'lockout_duration' => $this->lockout_duration,
                'unlock_at' => $debug_record ? $debug_record['unlock_at'] : 'unknown',
                'ip_address' => $ip_address
            ]);

        } catch (Exception $e) {
            error_log("Account locking error: " . $e->getMessage());
            error_log("Account locking stack trace: " . $e->getTraceAsString());
        }
    }
    
    /**
     * Clear failed attempts for successful login (preserves audit trail)
     */
    public function clearFailedAttempts($email) {
        try {
            // Only remove recent brute force attempts (keep older ones for audit)
            $stmt = $this->pdo->prepare("
                DELETE FROM brute_force_attempts
                WHERE email = ? AND attempt_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            $stmt->execute([$email]);

            // Update lockout status to expired instead of deleting
            $stmt = $this->pdo->prepare("
                UPDATE account_lockouts
                SET status = 'expired', unlock_at = NOW()
                WHERE email = ? AND status = 'active'
            ");
            $stmt->execute([$email]);

        } catch (Exception $e) {
            error_log("Clear failed attempts error: " . $e->getMessage());
        }
    }

    /**
     * Manually unlock an account (admin action with audit trail)
     */
    public function manualUnlock($email, $admin_username, $reason = 'Manual unlock by admin') {
        try {
            // Update the lockout record to mark as manually unlocked
            $stmt = $this->pdo->prepare("
                UPDATE account_lockouts
                SET manually_unlocked = 1,
                    unlocked_by = ?,
                    unlocked_at = NOW(),
                    unlock_reason = ?,
                    status = 'manually_unlocked'
                WHERE email = ? AND status = 'active'
            ");
            $result = $stmt->execute([$admin_username, $reason, $email]);

            if ($stmt->rowCount() > 0) {
                // Clear recent failed attempts (but preserve older ones for audit)
                $stmt = $this->pdo->prepare("
                    DELETE FROM brute_force_attempts
                    WHERE email = ? AND attempt_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                ");
                $stmt->execute([$email]);

                // Log the manual unlock action
                $this->logSecurityEvent('MANUAL_UNLOCK', $email, [
                    'unlocked_by' => $admin_username,
                    'unlock_reason' => $reason,
                    'unlock_timestamp' => date('Y-m-d H:i:s')
                ]);

                error_log("Manual unlock successful for $email by $admin_username");
                return [
                    'success' => true,
                    'message' => 'Account unlocked successfully',
                    'unlocked_by' => $admin_username,
                    'unlocked_at' => date('Y-m-d H:i:s')
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'No active lockout found for this email'
                ];
            }

        } catch (Exception $e) {
            error_log("Manual unlock error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Database error occurred during unlock'
            ];
        }
    }
    
    /**
     * Clean up expired lockouts and update status (keep history for 30 days)
     */
    private function cleanupExpiredLockouts() {
        try {
            // Update status of naturally expired lockouts
            $this->pdo->exec("
                UPDATE account_lockouts
                SET status = 'expired'
                WHERE unlock_at <= NOW() AND status = 'active'
            ");

            // Only delete lockouts older than 30 days to preserve audit history
            $this->pdo->exec("
                DELETE FROM account_lockouts
                WHERE locked_at <= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");

            // Clean up old brute force attempts (older than 7 days)
            $this->pdo->exec("
                DELETE FROM brute_force_attempts
                WHERE attempt_time <= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ");

        } catch (Exception $e) {
            error_log("Cleanup error: " . $e->getMessage());
        }
    }
    
    /**
     * Get client IP address
     */
    private function getClientIp() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Log security events
     */
    private function logSecurityEvent($event_type, $email, $details = []) {
        try {
            $ip = $this->getClientIp();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if security_logs table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'security_logs'");
            if ($stmt->fetch()) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO security_logs (event_type, username, ip_address, user_agent, risk_level, details, created_at)
                    VALUES (?, ?, ?, ?, 'HIGH', ?, NOW())
                ");
                $stmt->execute([
                    $event_type,
                    $email,
                    $ip,
                    $user_agent,
                    json_encode($details)
                ]);
            } else {
                // Table doesn't exist, just log to error log
                error_log("Security event: $event_type for $email - " . json_encode($details));
            }

        } catch (Exception $e) {
            error_log("Security event logging error: " . $e->getMessage());
            // Don't let logging errors prevent the main functionality
        }
    }
    
    /**
     * Get lockout statistics for admin dashboard
     */
    public function getLockoutStats() {
        try {
            $stats = [];
            
            // Current locked accounts (only active ones)
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as locked_count
                FROM account_lockouts
                WHERE status = 'active' AND unlock_at > NOW()
            ");
            $stats['current_locked'] = $stmt->fetchColumn();
            
            // Failed attempts in last hour
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as recent_attempts 
                FROM brute_force_attempts 
                WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stats['recent_attempts'] = $stmt->fetchColumn();
            
            // Top targeted emails
            $stmt = $this->pdo->query("
                SELECT email, COUNT(*) as attempt_count 
                FROM brute_force_attempts 
                WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY email 
                ORDER BY attempt_count DESC 
                LIMIT 10
            ");
            $stats['top_targeted'] = $stmt->fetchAll();
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Lockout stats error: " . $e->getMessage());
            return [
                'current_locked' => 0,
                'recent_attempts' => 0,
                'top_targeted' => []
            ];
        }
    }
    
    /**
     * Format remaining time for display
     */
    public function formatRemainingTime($seconds) {
        if ($seconds <= 0) return "0 seconds";
        
        $minutes = floor($seconds / 60);
        $seconds = $seconds % 60;
        
        if ($minutes > 0) {
            return $minutes . " minute" . ($minutes != 1 ? "s" : "") . 
                   ($seconds > 0 ? " and " . $seconds . " second" . ($seconds != 1 ? "s" : "") : "");
        } else {
            return $seconds . " second" . ($seconds != 1 ? "s" : "");
        }
    }
}
?>
