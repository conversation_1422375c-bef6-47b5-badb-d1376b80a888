<?php
// Include the PHPMailer classes
require '../phpmailer/src/PHPMailer.php';
require '../phpmailer/src/SMTP.php';
require '../phpmailer/src/Exception.php';

// Use PHPMailer namespace
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

date_default_timezone_set('Asia/Kuala_Lumpur');

function sendOTP($email, $otp) {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();                                            // Send using SMTP
        $mail->Host = 'smtp.gmail.com';                               // Set the SMTP server to Gmail
        $mail->SMTPAuth = true;                                       // Enable SMTP authentication
        $mail->Username = '<EMAIL>';                     // Your email address
        $mail->Password = 'swqt bdmb rrgm zjvr';                       // Your email password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // Enable TLS encryption
        $mail->Port = 587;                                            // TCP port for TLS

        // Recipients
        $mail->setFrom('<EMAIL>', 'TryMeOut');   // Set the sender's email address
        $mail->addAddress($email);                                    // Add recipient

        // Content
        $mail->isHTML(true);                                          // Set email format to HTML
        $mail->Subject = 'TryMeOut Verification Code';        // Email subject

        // HTML email body - Compact Design
        $mail->Body    = "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>TryMeOut - Verification Code</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
                    background-color: #f8fafc;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.5;
                }

                .email-container {
                    max-width: 400px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e5e7eb;
                }

                .email-header {
                    background: #6366f1;
                    padding: 20px;
                    text-align: center;
                    color: white;
                }

                .logo-text {
                    font-size: 18px;
                    font-weight: 700;
                    color: white;
                    margin-bottom: 4px;
                }

                .email-title {
                    color: white;
                    font-size: 16px;
                    font-weight: 500;
                    margin: 0;
                }

                .email-content {
                    padding: 24px 20px;
                    background: white;
                }

                .message {
                    font-size: 14px;
                    color: #374151;
                    margin-bottom: 20px;
                    line-height: 1.5;
                }

                .otp-section {
                    text-align: center;
                    margin: 20px 0;
                    padding: 20px;
                    background: #f9fafb;
                    border-radius: 8px;
                    border: 1px solid #e5e7eb;
                }

                .otp-label {
                    font-size: 12px;
                    font-weight: 600;
                    color: #6366f1;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 8px;
                }

                .otp-code {
                    font-size: 32px;
                    font-weight: 700;
                    color: #1f2937;
                    font-family: 'Courier New', monospace;
                    letter-spacing: 4px;
                    margin: 12px 0;
                    padding: 12px 16px;
                    background: white;
                    border-radius: 6px;
                    border: 2px solid #6366f1;
                    display: inline-block;
                }

                .otp-validity {
                    font-size: 12px;
                    color: #6b7280;
                    font-weight: 500;
                }

                .security-notice {
                    background: #fef2f2;
                    border: 1px solid #fecaca;
                    border-radius: 6px;
                    padding: 12px;
                    margin: 16px 0;
                    font-size: 12px;
                    color: #991b1b;
                    line-height: 1.4;
                }

                .email-footer {
                    background: #f9fafb;
                    padding: 16px 20px;
                    text-align: center;
                    border-top: 1px solid #e5e7eb;
                }

                .footer-text {
                    font-size: 12px;
                    color: #6b7280;
                    margin: 0;
                }

                /* Responsive Design */
                @media (max-width: 480px) {
                    body {
                        padding: 10px;
                    }

                    .email-container {
                        max-width: 100%;
                    }

                    .email-header,
                    .email-content,
                    .email-footer {
                        padding: 16px;
                    }

                    .otp-code {
                        font-size: 28px;
                        letter-spacing: 3px;
                    }
                }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <!-- Header Section -->
                <div class='email-header'>
                    <div class='logo-text'>TryMeOut</div>
                    <div class='email-title'>Verification Code</div>
                </div>

                <!-- Content Section -->
                <div class='email-content'>
                    <div class='message'>
                        Please use the verification code below to complete your account verification:
                    </div>

                    <!-- OTP Section -->
                    <div class='otp-section'>
                        <div class='otp-label'>Verification Code</div>
                        <div class='otp-code'>$otp</div>
                        <div class='otp-validity'>Valid for 15 minutes</div>
                    </div>

                    <!-- Security Notice -->
                    <div class='security-notice'>
                        🔒 If you didn't request this code, please ignore this email. Never share this code with anyone.
                    </div>
                </div>

                <!-- Footer Section -->
                <div class='email-footer'>
                    <div class='footer-text'>© 2025 TryMeOut Platform. All rights reserved.</div>
                </div>
            </div>
        </body>
        </html>";

        // Send the email
        $mail->send();
        return true;
    } catch (Exception $e) {
        return 'Mailer Error: ' . $mail->ErrorInfo;  // Return error if sending fails
    }
}
?>
