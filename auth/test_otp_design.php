<?php
// Test OTP Email Design
session_start();
require '../config/db_connect.php';

// Check if user is logged in (for testing purposes)
if (!isset($_SESSION['user_id'])) {
    // For testing, we'll allow access but show a note
    $test_mode = true;
} else {
    $test_mode = false;
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Test OTP Email Design</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='container mt-5'>";

echo "<h1>📧 Test OTP Email Design</h1>";
echo "<p class='text-muted'>Preview of the new compact OTP email layout</p>";

if ($test_mode) {
    echo "<div class='alert alert-info'>";
    echo "<i class='fas fa-info-circle me-2'></i>";
    echo "<strong>Test Mode:</strong> You can preview the email design without being logged in.";
    echo "</div>";
}

echo "<div class='row'>";
echo "<div class='col-md-6'>";

echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h5>🎨 New Compact Design</h5>";
echo "</div>";
echo "<div class='card-body'>";

// Generate a sample OTP for preview
$sample_otp = '123456';

// Include the OTP email HTML directly for preview
echo "<div style='background: #f8fafc; padding: 20px; border-radius: 8px;'>";
echo "<div style='max-width: 400px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;'>";

// Header
echo "<div style='background: #6366f1; padding: 20px; text-align: center; color: white;'>";
echo "<div style='font-size: 18px; font-weight: 700; color: white; margin-bottom: 4px;'>TryMeOut</div>";
echo "<div style='color: white; font-size: 16px; font-weight: 500; margin: 0;'>Verification Code</div>";
echo "</div>";

// Content
echo "<div style='padding: 24px 20px; background: white;'>";
echo "<div style='font-size: 14px; color: #374151; margin-bottom: 20px; line-height: 1.5;'>";
echo "Please use the verification code below to complete your account verification:";
echo "</div>";

// OTP Section
echo "<div style='text-align: center; margin: 20px 0; padding: 20px; background: #f9fafb; border-radius: 8px; border: 1px solid #e5e7eb;'>";
echo "<div style='font-size: 12px; font-weight: 600; color: #6366f1; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 8px;'>Verification Code</div>";
echo "<div style='font-size: 32px; font-weight: 700; color: #1f2937; font-family: \"Courier New\", monospace; letter-spacing: 4px; margin: 12px 0; padding: 12px 16px; background: white; border-radius: 6px; border: 2px solid #6366f1; display: inline-block;'>$sample_otp</div>";
echo "<div style='font-size: 12px; color: #6b7280; font-weight: 500;'>Valid for 15 minutes</div>";
echo "</div>";

// Security Notice
echo "<div style='background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 12px; margin: 16px 0; font-size: 12px; color: #991b1b; line-height: 1.4;'>";
echo "🔒 If you didn't request this code, please ignore this email. Never share this code with anyone.";
echo "</div>";

echo "</div>";

// Footer
echo "<div style='background: #f9fafb; padding: 16px 20px; text-align: center; border-top: 1px solid #e5e7eb;'>";
echo "<div style='font-size: 12px; color: #6b7280; margin: 0;'>© 2025 TryMeOut Platform. All rights reserved.</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";

echo "<div class='col-md-6'>";

echo "<div class='card mb-4'>";
echo "<div class='card-header'>";
echo "<h5>📊 Design Comparison</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<table class='table table-sm'>";
echo "<tr><th>Aspect</th><th>Old Design</th><th>New Design</th></tr>";
echo "<tr><td><strong>Width</strong></td><td>600px</td><td>400px</td></tr>";
echo "<tr><td><strong>Header Height</strong></td><td>~120px</td><td>~60px</td></tr>";
echo "<tr><td><strong>Content Padding</strong></td><td>40px</td><td>24px</td></tr>";
echo "<tr><td><strong>OTP Font Size</strong></td><td>48px</td><td>32px</td></tr>";
echo "<tr><td><strong>Sections</strong></td><td>5 sections</td><td>3 sections</td></tr>";
echo "<tr><td><strong>Total Height</strong></td><td>~800px</td><td>~400px</td></tr>";
echo "</table>";
echo "</div>";
echo "</div>";

echo "<div class='card mb-4'>";
echo "<div class='card-header'>";
echo "<h5>✅ Improvements</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul>";
echo "<li><strong>50% smaller</strong> - More compact layout</li>";
echo "<li><strong>Simplified header</strong> - No complex logo container</li>";
echo "<li><strong>Reduced padding</strong> - Less whitespace</li>";
echo "<li><strong>Smaller OTP code</strong> - Still readable but compact</li>";
echo "<li><strong>Condensed security notice</strong> - Essential info only</li>";
echo "<li><strong>Minimal footer</strong> - Just copyright</li>";
echo "<li><strong>Mobile optimized</strong> - Better on small screens</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h5>🧪 Test Email Sending</h5>";
echo "</div>";
echo "<div class='card-body'>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $test_email = filter_var($_POST['test_email'], FILTER_VALIDATE_EMAIL);
    
    if ($test_email) {
        require_once 'send_otp.php';
        $test_otp = rand(100000, 999999);
        
        $result = sendOTP($test_email, $test_otp);
        
        if ($result === true) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-check-circle me-2'></i>";
            echo "<strong>Success!</strong> Test OTP email sent to $test_email";
            echo "<br><small>OTP: $test_otp (for testing purposes)</small>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>Error:</strong> " . htmlspecialchars($result);
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "<strong>Invalid email address.</strong> Please enter a valid email.";
        echo "</div>";
    }
}

echo "<form method='post'>";
echo "<div class='mb-3'>";
echo "<label for='test_email' class='form-label'>Test Email Address</label>";
echo "<input type='email' class='form-control' id='test_email' name='test_email' placeholder='Enter email to test' required>";
echo "</div>";
echo "<button type='submit' class='btn btn-primary'>";
echo "<i class='fas fa-paper-plane me-2'></i>Send Test OTP";
echo "</button>";
echo "</form>";

echo "<div class='mt-3'>";
echo "<small class='text-muted'>";
echo "<i class='fas fa-info-circle me-1'></i>";
echo "This will send a real email with the new compact design for testing.";
echo "</small>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div class='row mt-4'>";
echo "<div class='col-12'>";

echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h5>📱 Mobile Preview</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p>The new design is optimized for mobile devices:</p>";

echo "<div style='max-width: 320px; margin: 0 auto; background: #f8fafc; padding: 10px; border-radius: 8px;'>";
echo "<div style='max-width: 100%; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;'>";

// Mobile Header
echo "<div style='background: #6366f1; padding: 16px; text-align: center; color: white;'>";
echo "<div style='font-size: 16px; font-weight: 700; color: white; margin-bottom: 4px;'>TryMeOut</div>";
echo "<div style='color: white; font-size: 14px; font-weight: 500; margin: 0;'>Verification Code</div>";
echo "</div>";

// Mobile Content
echo "<div style='padding: 16px; background: white;'>";
echo "<div style='font-size: 13px; color: #374151; margin-bottom: 16px; line-height: 1.4;'>";
echo "Please use the verification code below:";
echo "</div>";

// Mobile OTP Section
echo "<div style='text-align: center; margin: 16px 0; padding: 16px; background: #f9fafb; border-radius: 6px; border: 1px solid #e5e7eb;'>";
echo "<div style='font-size: 11px; font-weight: 600; color: #6366f1; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 6px;'>Code</div>";
echo "<div style='font-size: 28px; font-weight: 700; color: #1f2937; font-family: \"Courier New\", monospace; letter-spacing: 3px; margin: 8px 0; padding: 10px 12px; background: white; border-radius: 4px; border: 2px solid #6366f1; display: inline-block;'>$sample_otp</div>";
echo "<div style='font-size: 11px; color: #6b7280; font-weight: 500;'>Valid 15 min</div>";
echo "</div>";

echo "</div>";

// Mobile Footer
echo "<div style='background: #f9fafb; padding: 12px 16px; text-align: center; border-top: 1px solid #e5e7eb;'>";
echo "<div style='font-size: 10px; color: #6b7280; margin: 0;'>© 2025 TryMeOut</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<p class='text-center mt-3'><small class='text-muted'>Mobile view (320px width)</small></p>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div class='mt-4 p-3 bg-light rounded'>";
echo "<h3>🎯 Summary of Changes</h3>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>📏 Size Reduction</h5>";
echo "<ul>";
echo "<li><strong>Width:</strong> 600px → 400px (33% smaller)</li>";
echo "<li><strong>Height:</strong> ~800px → ~400px (50% smaller)</li>";
echo "<li><strong>File size:</strong> Reduced CSS and HTML</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>🎨 Design Improvements</h5>";
echo "<ul>";
echo "<li><strong>Cleaner layout</strong> - Less visual clutter</li>";
echo "<li><strong>Better mobile experience</strong> - Responsive design</li>";
echo "<li><strong>Faster loading</strong> - Smaller email size</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body></html>";
?>
