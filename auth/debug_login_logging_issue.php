<?php
// Debug Login Logging Issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../config/db_connect.php';
require '../config/logger.php';

echo "<h2>🔍 Debug Login Logging Issue</h2>";

try {
    $logger = getLogger();
    
    // Get a test user
    $stmt = $pdo->query("SELECT id, email FROM users LIMIT 1");
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "❌ No users found<br>";
        exit;
    }
    
    echo "<h3>Testing with user: " . htmlspecialchars($test_user['email']) . " (ID: " . $test_user['id'] . ")</h3>";
    
    echo "<h4>Step 1: Check Current Database State</h4>";
    
    // Check all login attempts for this user
    $stmt = $pdo->prepare("
        SELECT email, user_id, success, failure_reason, created_at 
        FROM login_attempts 
        WHERE email = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$test_user['email']]);
    $all_attempts = $stmt->fetchAll();
    
    echo "All login attempts for this user (last 10):<br>";
    if ($all_attempts) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
        foreach ($all_attempts as $attempt) {
            $status = $attempt['success'] ? 'SUCCESS' : 'FAILED';
            $status_color = $attempt['success'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($attempt['email']) . "</td>";
            echo "<td>" . ($attempt['user_id'] ?? 'NULL') . "</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>$status</td>";
            echo "<td>" . htmlspecialchars($attempt['failure_reason'] ?? 'N/A') . "</td>";
            echo "<td>" . $attempt['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No login attempts found for this user.<br>";
    }
    
    echo "<h4>Step 2: Test Direct Logger Call</h4>";
    
    // Test direct logger call with detailed debugging
    echo "Calling logger->logLoginAttempt() directly...<br>";
    
    try {
        // Get current time for reference
        $before_time = date('Y-m-d H:i:s');
        echo "Time before logging: $before_time<br>";
        
        // Call the logger
        $result = $logger->logLoginAttempt($test_user['email'], false, $test_user['id'], 'Debug test attempt');
        echo "✅ Logger call completed<br>";
        
        // Check if it was inserted
        $stmt = $pdo->prepare("
            SELECT email, user_id, success, failure_reason, created_at 
            FROM login_attempts 
            WHERE email = ? AND failure_reason = 'Debug test attempt'
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$test_user['email']]);
        $debug_attempt = $stmt->fetch();
        
        if ($debug_attempt) {
            echo "✅ Debug attempt found in database:<br>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
            echo "<tr>";
            echo "<td>" . htmlspecialchars($debug_attempt['email']) . "</td>";
            echo "<td>" . ($debug_attempt['user_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($debug_attempt['success'] ? 'TRUE' : 'FALSE') . "</td>";
            echo "<td>" . htmlspecialchars($debug_attempt['failure_reason']) . "</td>";
            echo "<td>" . $debug_attempt['created_at'] . "</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "❌ Debug attempt NOT found in database!<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Logger error: " . $e->getMessage() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h4>Step 3: Check Database Connection and Autocommit</h4>";
    
    // Check PDO settings
    echo "PDO autocommit: " . ($pdo->getAttribute(PDO::ATTR_AUTOCOMMIT) ? 'ON' : 'OFF') . "<br>";
    echo "PDO in transaction: " . ($pdo->inTransaction() ? 'YES' : 'NO') . "<br>";
    
    // Test direct database insert
    echo "<br>Testing direct database insert...<br>";
    try {
        $stmt = $pdo->prepare("
            INSERT INTO login_attempts (email, user_id, ip_address, country, city, user_agent, success, failure_reason)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $result = $stmt->execute([
            $test_user['email'],
            $test_user['id'],
            '127.0.0.1',
            'Test Country',
            'Test City',
            'Test User Agent',
            0,
            'Direct database test'
        ]);
        
        if ($result) {
            echo "✅ Direct database insert successful<br>";
            
            // Check if it's there
            $stmt = $pdo->prepare("
                SELECT * FROM login_attempts 
                WHERE email = ? AND failure_reason = 'Direct database test'
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$test_user['email']]);
            $direct_test = $stmt->fetch();
            
            if ($direct_test) {
                echo "✅ Direct insert found in database<br>";
            } else {
                echo "❌ Direct insert NOT found in database<br>";
            }
        } else {
            echo "❌ Direct database insert failed<br>";
        }
    } catch (Exception $e) {
        echo "❌ Direct insert error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h4>Step 4: Check Logger Implementation</h4>";
    
    // Check if logger is using the same PDO connection
    echo "Checking logger implementation...<br>";
    
    // Get logger's PDO connection info
    $reflection = new ReflectionClass($logger);
    $pdoProperty = $reflection->getProperty('pdo');
    $pdoProperty->setAccessible(true);
    $loggerPdo = $pdoProperty->getValue($logger);
    
    echo "Logger PDO same as global PDO: " . ($loggerPdo === $pdo ? 'YES' : 'NO') . "<br>";
    
    if ($loggerPdo !== $pdo) {
        echo "⚠️ Logger is using a different PDO connection!<br>";
        echo "Logger PDO autocommit: " . ($loggerPdo->getAttribute(PDO::ATTR_AUTOCOMMIT) ? 'ON' : 'OFF') . "<br>";
        echo "Logger PDO in transaction: " . ($loggerPdo->inTransaction() ? 'YES' : 'NO') . "<br>";
    }
    
    echo "<h4>Step 5: Test Count Queries</h4>";
    
    // Test different count queries
    $count_queries = [
        "Simple count" => "SELECT COUNT(*) FROM login_attempts WHERE email = ?",
        "Failed only" => "SELECT COUNT(*) FROM login_attempts WHERE email = ? AND success = 0",
        "Last 10 min" => "SELECT COUNT(*) FROM login_attempts WHERE email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)",
        "Failed last 10 min" => "SELECT COUNT(*) FROM login_attempts WHERE email = ? AND success = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)",
        "Last hour" => "SELECT COUNT(*) FROM login_attempts WHERE email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)"
    ];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Query Type</th><th>Count</th></tr>";
    
    foreach ($count_queries as $name => $query) {
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute([$test_user['email']]);
            $count = $stmt->fetchColumn();
            echo "<tr><td>$name</td><td>$count</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>$name</td><td>ERROR: " . $e->getMessage() . "</td></tr>";
        }
    }
    echo "</table>";
    
    echo "<h4>Step 6: Check Recent Records</h4>";
    
    // Show all recent records
    $stmt = $pdo->query("
        SELECT email, user_id, success, failure_reason, created_at 
        FROM login_attempts 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $recent_records = $stmt->fetchAll();
    
    echo "All recent login attempts (last hour):<br>";
    if ($recent_records) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
        foreach ($recent_records as $record) {
            $status = $record['success'] ? 'SUCCESS' : 'FAILED';
            $status_color = $record['success'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['email']) . "</td>";
            echo "<td>" . ($record['user_id'] ?? 'NULL') . "</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>$status</td>";
            echo "<td>" . htmlspecialchars($record['failure_reason'] ?? 'N/A') . "</td>";
            echo "<td>" . $record['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No recent records found.<br>";
    }
    
    echo "<h4>Step 7: Cleanup</h4>";
    
    // Clean up test records
    $cleanup_queries = [
        "DELETE FROM login_attempts WHERE email = ? AND failure_reason = 'Debug test attempt'",
        "DELETE FROM login_attempts WHERE email = ? AND failure_reason = 'Direct database test'"
    ];
    
    foreach ($cleanup_queries as $query) {
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute([$test_user['email']]);
            echo "✅ Cleanup query executed<br>";
        } catch (Exception $e) {
            echo "⚠️ Cleanup error: " . $e->getMessage() . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Debug Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffc107;'>";
echo "<h4>🔍 Analysis</h4>";
echo "<p>This debug script will help identify:</p>";
echo "<ul>";
echo "<li>Whether records are actually being inserted</li>";
echo "<li>If there's a PDO connection issue</li>";
echo "<li>If the count queries are working correctly</li>";
echo "<li>If there's a transaction or autocommit issue</li>";
echo "</ul>";
echo "</div>";

echo "<p style='margin-top: 20px;'>";
echo "<a href='test_fixed_failed_login.php'>← Back to Test</a> | ";
echo "<a href='../admin/logs.php'>Admin Logs</a>";
echo "</p>";
?>
