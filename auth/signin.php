<?php
session_start();
require '../config/db_connect.php';
require '../config/logger.php';
require '../config/brute_force_protection.php';

$error_message = "";
$error_type = "danger"; // Default error type
$logger = getLogger();
$bruteForceProtection = new BruteForceProtection($pdo);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $remember_me = isset($_POST['remember_me']);

    // FIRST: Check if the email exists in the system
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    // Only apply brute force protection to REGISTERED users
    if ($user) {
        // Check if this registered account is locked due to brute force attempts
        $lockout_status = $bruteForceProtection->isAccountLocked($email);

        if ($lockout_status['locked']) {
            $remaining_time = $bruteForceProtection->formatRemainingTime($lockout_status['remaining_time']);
            $error_message = "🔒 Account temporarily locked due to multiple failed login attempts. Please try again in $remaining_time.";

            // Log the blocked attempt
            $logger->logSecurityEvent('LOGIN_BLOCKED_LOCKED_ACCOUNT', $email, $user['id'], 'HIGH', [
                'remaining_time' => $lockout_status['remaining_time'],
                'attempt_count' => $lockout_status['attempt_count'],
                'blocked_reason' => 'Registered account locked - login attempt blocked'
            ]);

            // DO NOT proceed with password verification for locked registered accounts
        } else {
            // Registered account is not locked, proceed with password verification

            if (password_verify($password, $user['password_hash'])) {
                if ($user['is_verified']) {
                    // Clear any failed attempts for successful login
                    $bruteForceProtection->clearFailedAttempts($email);

                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['login_time'] = time();

                    // Log successful login
                    $logger->logLoginAttempt($email, true, $user['id']);

                    // Log additional security events
                    $logger->logSecurityEvent('SESSION_START', $user['username'], $user['id'], 'LOW', [
                        'role' => $user['role'],
                        'remember_me' => $remember_me,
                        'last_login' => $user['last_login'] ?? 'Never'
                    ]);

                    // Update last login time (check if column exists first)
                    try {
                        $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                        $stmt->execute([$user['id']]);
                    } catch (PDOException $e) {
                        // If last_login column doesn't exist, skip this update
                        if (strpos($e->getMessage(), 'Unknown column') === false) {
                            throw $e; // Re-throw if it's a different error
                        }
                    }

                    // Set remember me cookie if requested
                    if ($remember_me) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);

                        // Store token in database (you may want to create a remember_tokens table)
                        $logger->logSecurityEvent('REMEMBER_TOKEN_SET', $user['username'], $user['id'], 'LOW');
                    }

                    if ($user['role'] === 'admin') {
                        header('Location: ../admin/dashboard.php');
                    } else {
                        header('Location: ../pages/dashboard.php');
                    }
                    exit;
                } else {
                    // Account not verified
                    $error_message = "Please verify your account.";
                    $logger->logLoginAttempt($email, false, $user['id'], 'Account not verified');

                    // Record failed attempt for unverified registered accounts
                    $attempt_result = $bruteForceProtection->recordFailedAttempt($email);
                    if ($attempt_result['locked']) {
                        $remaining_time = $bruteForceProtection->formatRemainingTime(300); // 5 minutes
                        $error_message = "🔒 Account temporarily locked due to multiple failed login attempts. Please try again in $remaining_time.";
                    }
                }
            } else {
                // Wrong password for registered user
                $attempt_result = $bruteForceProtection->recordFailedAttempt($email);

                if ($attempt_result['locked']) {
                    $remaining_time = $bruteForceProtection->formatRemainingTime(300); // 5 minutes
                    $error_message = "🔒 Account temporarily locked due to multiple failed login attempts. Please try again in $remaining_time.";
                } else {
                    $remaining_attempts = $attempt_result['remaining_attempts'] ?? 0;
                    if ($remaining_attempts <= 2 && $remaining_attempts > 0) {
                        $error_message = "Invalid credentials. ⚠️ Warning: $remaining_attempts attempt(s) remaining before account lockout.";
                    } else {
                        $error_message = "Invalid credentials.";
                    }
                }

                $logger->logLoginAttempt($email, false, $user['id'], 'Invalid password');
            }
        }
    } else {
        // Email not found in system - NO brute force protection applied

        // Check for similar email addresses (typo suggestions)
        $suggestion = "";
        $domain = substr(strrchr($email, "@"), 1);
        $common_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com'];

        foreach ($common_domains as $common_domain) {
            if (levenshtein($domain, $common_domain) <= 2 && $domain !== $common_domain) {
                $suggested_email = substr($email, 0, strpos($email, '@')) . '@' . $common_domain;
                $suggestion = "<br><small>💡 Did you mean: <strong>$suggested_email</strong>?</small>";
                break;
            }
        }

        $error_message = "📧 This email address is not registered with us. Please <a href='signup.php' style='color: #fff; text-decoration: underline; font-weight: bold;'>create an account</a> or double-check your email address.$suggestion";
        $error_type = "info"; // Use info style for unregistered emails

        $logger->logLoginAttempt($email, false, null, 'Email not found');

        // Log potential security threat for non-existent emails
        $logger->logSecurityEvent('LOGIN_ATTEMPT_INVALID_EMAIL', $email, null, 'MEDIUM', [
            'attempted_email' => $email,
            'note' => 'No brute force protection applied to non-registered email',
            'suggestion_provided' => !empty($suggestion)
        ]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Sign In</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --button-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin: 0;
        }

        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle, rgba(255,255,255,0.08) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }



        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-container img {
            width: 70px;
            height: 70px;
            object-fit: contain;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .auth-subtitle {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .form-floating {
            margin-bottom: 20px;
            position: relative;
        }

        .form-floating .form-control {
            height: 60px;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            padding: 20px 20px 8px 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--text-primary);
        }

        .form-floating .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 20px 20px 8px 20px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-check {
            margin: 25px 0;
        }

        .form-check-input {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid var(--border-color);
            margin-top: 0;
            background-color: transparent;
            background-image: none;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .form-check-input:checked {
            background-color: transparent;
            border-color: var(--accent-color);
            background-image: none;
        }

        .form-check-input:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--accent-color);
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
        }

        .form-check-input:hover {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.1rem rgba(99, 102, 241, 0.1);
        }

        .form-check-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-left: 10px;
        }

        .btn-auth {
            height: 60px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--button-gradient);
            border: none;
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
        }

        .btn-auth::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-auth:hover::before {
            left: 100%;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
        }

        .btn-auth:active {
            transform: translateY(-1px);
        }

        .auth-links {
            text-align: center;
            margin-top: 30px;
        }

        .auth-links a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #4f46e5;
            text-decoration: underline;
        }

        .divider {
            margin: 20px 0;
            text-align: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 0%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #dee2e6, transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .alert-info {
            background: linear-gradient(135deg,rgb(128, 77, 247),rgb(49, 133, 202));
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .auth-card {
                padding: 30px 25px;
                margin: 10px;
            }

            .auth-title {
                font-size: 1.75rem;
            }

            .form-floating .form-control {
                height: 55px;
                padding: 18px 15px 6px 15px;
            }

            .form-floating label {
                padding: 18px 15px 6px 15px;
            }

            .btn-auth {
                height: 55px;
                font-size: 1rem;
            }
        }

        /* Loading Animation */
        .btn-auth.loading {
            pointer-events: none;
        }

        .btn-auth.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Password Toggle Button */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.1rem;
            cursor: pointer;
            z-index: 10;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .password-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut Logo">
                </div>
                <h1 class="auth-title">Welcome Back</h1>
                <p class="auth-subtitle">Sign in to continue your security journey</p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-<?php echo $error_type; ?>" role="alert">
                    <?php if ($error_type === 'info'): ?>
                        <i class="fas fa-info-circle me-2"></i>
                    <?php else: ?>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php endif; ?>
                    <?php echo $error_message; // Don't escape HTML for links ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="signin.php" id="signinForm">
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                    <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
                </div>

                <div class="form-floating position-relative">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                    </button>
                </div>

                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me" value="1">
                    <label class="form-check-label" for="rememberMe">
                        <i class="fas fa-heart me-1"></i>Remember me
                    </label>
                </div>

                <button type="submit" class="btn btn-auth w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                </button>
            </form>

            <div class="divider">
            </div>

            <div class="auth-links">
                <a href="forgot_password.php">
                    <i class="fas fa-key me-1"></i>Forgot your password?
                </a>
                <p class="mt-3 mb-0">
                    Don't have an account?
                    <a href="signup.php">
                        <i class="fas fa-user-plus me-1"></i>Create Account
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission with loading state
        document.getElementById('signinForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Signing In...</span>';
        });

        // Enhanced form validation
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
