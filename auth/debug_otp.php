<?php
// OTP Debugging Tool
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../config/db_connect.php';

$email = $_GET['email'] ?? '';

echo "<h1>OTP Debugging Tool</h1>";

if ($email) {
    echo "<h2>Debugging OTP for: " . htmlspecialchars($email) . "</h2>";
    
    // Get all OTP records for this email
    $stmt = $pdo->prepare("SELECT *, NOW() as current_time FROM otp_verifications WHERE email = ? ORDER BY created_at DESC");
    $stmt->execute([$email]);
    $records = $stmt->fetchAll();
    
    echo "<h3>All OTP Records for this email:</h3>";
    if (empty($records)) {
        echo "<p style='color: red;'>❌ No OTP records found for this email!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>OTP Code</th><th>Type</th><th>Created</th><th>Expires</th><th>Current Time</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($records as $record) {
            $is_expired = strtotime($record['expires_at']) < strtotime($record['current_time']);
            $status = $is_expired ? '❌ EXPIRED' : '✅ VALID';
            $row_color = $is_expired ? '#ffebee' : '#e8f5e8';
            
            echo "<tr style='background: $row_color;'>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td><strong>" . $record['otp_code'] . "</strong></td>";
            echo "<td>" . $record['type'] . "</td>";
            echo "<td>" . $record['created_at'] . "</td>";
            echo "<td>" . $record['expires_at'] . "</td>";
            echo "<td>" . $record['current_time'] . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test OTP validation
    echo "<h3>Test OTP Validation:</h3>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='email' value='" . htmlspecialchars($email) . "'>";
    echo "<label>Enter OTP to test: </label>";
    echo "<input type='text' name='test_otp' maxlength='6' pattern='[0-9]{6}' required>";
    echo "<button type='submit'>Test OTP</button>";
    echo "</form>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_otp'])) {
        $test_otp = trim($_POST['test_otp']);
        echo "<h4>Testing OTP: $test_otp</h4>";
        
        // Test the exact query used in verification
        $test_stmt = $pdo->prepare("SELECT *, NOW() as current_time FROM otp_verifications WHERE otp_code = ? AND email = ? AND type = 'forgot_password' AND expires_at > NOW()");
        $test_stmt->execute([$test_otp, $email]);
        $test_result = $test_stmt->fetch();
        
        if ($test_result) {
            echo "<p style='color: green;'>✅ OTP is VALID and would work!</p>";
            echo "<pre>" . print_r($test_result, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>❌ OTP validation failed</p>";
            
            // Check if OTP exists but is expired
            $check_stmt = $pdo->prepare("SELECT *, NOW() as current_time FROM otp_verifications WHERE otp_code = ? AND email = ? AND type = 'forgot_password'");
            $check_stmt->execute([$test_otp, $email]);
            $check_result = $check_stmt->fetch();
            
            if ($check_result) {
                echo "<p style='color: orange;'>⚠️ OTP exists but is EXPIRED</p>";
                echo "<p>Expires at: " . $check_result['expires_at'] . "</p>";
                echo "<p>Current time: " . $check_result['current_time'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ OTP does not exist in database</p>";
            }
        }
    }
    
} else {
    echo "<p>Please provide an email parameter: <code>debug_otp.php?email=<EMAIL></code></p>";
}

echo "<hr>";
echo "<h3>Database Table Structure:</h3>";
try {
    $structure = $pdo->query("DESCRIBE otp_verifications")->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $field) {
        echo "<tr>";
        echo "<td>" . $field['Field'] . "</td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . $field['Default'] . "</td>";
        echo "<td>" . $field['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting table structure: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Recent OTP Records (All Types):</h3>";
try {
    $recent = $pdo->query("SELECT *, NOW() as current_time FROM otp_verifications ORDER BY created_at DESC LIMIT 10")->fetchAll();
    if (empty($recent)) {
        echo "<p>No OTP records found in database.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Email</th><th>OTP</th><th>Type</th><th>Created</th><th>Expires</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($recent as $record) {
            $is_expired = strtotime($record['expires_at']) < strtotime($record['current_time']);
            $status = $is_expired ? '❌ EXPIRED' : '✅ VALID';
            $row_color = $is_expired ? '#ffebee' : '#e8f5e8';
            
            echo "<tr style='background: $row_color;'>";
            echo "<td>" . htmlspecialchars($record['email']) . "</td>";
            echo "<td><strong>" . $record['otp_code'] . "</strong></td>";
            echo "<td>" . $record['type'] . "</td>";
            echo "<td>" . $record['created_at'] . "</td>";
            echo "<td>" . $record['expires_at'] . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting recent records: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='forgot_password.php'>← Back to Forgot Password</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
form { margin: 10px 0; }
input, button { padding: 5px; margin: 2px; }
</style>
