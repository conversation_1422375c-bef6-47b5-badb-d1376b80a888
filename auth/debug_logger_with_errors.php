<?php
// Debug Logger with Error Display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Enable debug logging
define('DEBUG_LOGGING', true);

require '../config/db_connect.php';
require '../config/logger.php';

echo "<h2>🔬 Debug Logger with Error Display</h2>";

try {
    // Get a test user
    $stmt = $pdo->query("SELECT id, email FROM users LIMIT 1");
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "❌ No users found<br>";
        exit;
    }
    
    echo "<h3>Testing with: " . htmlspecialchars($test_user['email']) . " (ID: " . $test_user['id'] . ")</h3>";
    
    echo "<h4>Step 1: Test Logger with Debug Mode</h4>";
    
    $logger = getLogger();
    
    echo "Calling logger->logLoginAttempt() with debug mode enabled...<br>";
    
    // This should now show any errors that occur
    $logger->logLoginAttempt($test_user['email'], false, $test_user['id'], 'Debug mode test');
    
    echo "✅ Logger call completed<br>";
    
    echo "<h4>Step 2: Check if Record was Inserted</h4>";
    
    // Check if the record was actually inserted
    $stmt = $pdo->prepare("
        SELECT email, user_id, success, failure_reason, created_at 
        FROM login_attempts 
        WHERE email = ? AND failure_reason = 'Debug mode test'
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$test_user['email']]);
    $debug_record = $stmt->fetch();
    
    if ($debug_record) {
        echo "✅ Record found in database:<br>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
        echo "<tr>";
        echo "<td>" . htmlspecialchars($debug_record['email']) . "</td>";
        echo "<td>" . ($debug_record['user_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($debug_record['success'] ? 'TRUE' : 'FALSE') . "</td>";
        echo "<td>" . htmlspecialchars($debug_record['failure_reason']) . "</td>";
        echo "<td>" . $debug_record['created_at'] . "</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "❌ Record NOT found in database<br>";
    }
    
    echo "<h4>Step 3: Test Multiple Attempts</h4>";
    
    echo "Testing 3 failed attempts with debug mode...<br>";
    
    for ($i = 1; $i <= 3; $i++) {
        echo "<strong>Attempt $i:</strong><br>";
        $logger->logLoginAttempt($test_user['email'], false, $test_user['id'], "Debug test attempt $i");
        echo "Attempt $i completed<br><br>";
    }
    
    echo "<h4>Step 4: Count All Debug Records</h4>";
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM login_attempts 
        WHERE email = ? AND failure_reason LIKE 'Debug%'
    ");
    $stmt->execute([$test_user['email']]);
    $debug_count = $stmt->fetchColumn();
    
    echo "Total debug records found: $debug_count<br>";
    
    if ($debug_count > 0) {
        echo "<h4>Step 5: Show All Debug Records</h4>";
        
        $stmt = $pdo->prepare("
            SELECT email, user_id, success, failure_reason, created_at 
            FROM login_attempts 
            WHERE email = ? AND failure_reason LIKE 'Debug%'
            ORDER BY created_at DESC
        ");
        $stmt->execute([$test_user['email']]);
        $debug_records = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
        
        foreach ($debug_records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['email']) . "</td>";
            echo "<td>" . ($record['user_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($record['success'] ? 'TRUE' : 'FALSE') . "</td>";
            echo "<td>" . htmlspecialchars($record['failure_reason']) . "</td>";
            echo "<td>" . $record['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h4>Step 6: Test Count Query Issue</h4>";
    
    // Test the exact query that was failing
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM login_attempts 
        WHERE email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    ");
    $stmt->execute([$test_user['email']]);
    $count_10min = $stmt->fetchColumn();
    
    echo "Records in last 10 minutes: $count_10min<br>";
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM login_attempts 
        WHERE email = ? AND success = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    ");
    $stmt->execute([$test_user['email']]);
    $failed_10min = $stmt->fetchColumn();
    
    echo "Failed records in last 10 minutes: $failed_10min<br>";
    
    echo "<h4>Step 7: Check Database Time</h4>";
    
    $stmt = $pdo->query("SELECT NOW() as current_time");
    $db_time = $stmt->fetchColumn();
    echo "Database current time: $db_time<br>";
    echo "PHP current time: " . date('Y-m-d H:i:s') . "<br>";
    
    // Check the most recent record time
    $stmt = $pdo->prepare("
        SELECT created_at FROM login_attempts 
        WHERE email = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$test_user['email']]);
    $latest_time = $stmt->fetchColumn();
    echo "Latest record time: $latest_time<br>";
    
    if ($latest_time) {
        $time_diff = strtotime($db_time) - strtotime($latest_time);
        echo "Time difference: $time_diff seconds<br>";
    }
    
    echo "<h4>Step 8: Cleanup</h4>";
    
    $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE email = ? AND failure_reason LIKE 'Debug%'");
    $stmt->execute([$test_user['email']]);
    echo "✅ Debug records cleaned up<br>";
    
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Test Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<br><div style='background: #e6f7ff; padding: 15px; border-radius: 8px; border: 1px solid #17a2b8;'>";
echo "<h4>🔍 Debug Analysis</h4>";
echo "<p>This test enables debug mode to show any hidden errors in the logger.</p>";
echo "<p>If records are being inserted but counts are wrong, it suggests a timing or query issue.</p>";
echo "<p>If no records are inserted, it suggests a database connection or permission issue.</p>";
echo "</div>";

echo "<p style='margin-top: 20px;'>";
echo "<a href='test_fixed_failed_login.php'>← Back to Main Test</a> | ";
echo "<a href='simple_login_test.php'>Simple Test</a> | ";
echo "<a href='../admin/logs.php'>Admin Logs</a>";
echo "</p>";
?>
