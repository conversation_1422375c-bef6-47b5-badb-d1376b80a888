<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$error_message = '';
$success_message = '';

try {
    require '../config/db_connect.php';
    require 'send_otp.php';  // Include the PHPMailer function

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $email = trim($_POST['email']);

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Please enter a valid email address.';
        } else {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            // Debug logging
            error_log("Forgot Password - Looking for user with email: $email");
            if ($user) {
                error_log("Forgot Password - User found: ID=" . $user['id'] . ", Email=" . $user['email']);
            } else {
                error_log("Forgot Password - No user found with email: $email");
            }

            if ($user) {
                // Delete any existing OTP for this email and type to prevent conflicts
                $cleanup_stmt = $pdo->prepare("DELETE FROM otp_verifications WHERE email = ? AND type = 'forgot_password'");
                $cleanup_stmt->execute([$email]);

                // Generate OTP for forgot password
                $otp = rand(100000, 999999);
                $expires_at = date('Y-m-d H:i:s', strtotime('+15 minutes')); // Increased to 15 minutes

                // Debug logging
                error_log("Creating OTP for forgot password - Email: $email, OTP: $otp, Expires: $expires_at");

                // Store OTP in the database for forgot password
                // Use the standard otp_code column (the table has both otp and otp_code, we'll use otp_code consistently)
                try {
                    $stmt = $pdo->prepare("INSERT INTO otp_verifications (user_id, otp_code, email, type, expires_at) VALUES (?, ?, ?, 'forgot_password', ?)");
                    $stmt->execute([$user['id'], $otp, $email, $expires_at]);
                } catch (PDOException $e) {
                    error_log("Failed to insert OTP for forgot password: " . $e->getMessage());
                    echo "<div class='alert alert-danger'>Failed to generate reset code. Please try again.</div>";
                    return;
                }

                // Send OTP to the user email using PHPMailer
                $sendStatus = sendOTP($email, $otp);

                if ($sendStatus === true) {
                    // Redirect to OTP verification page after OTP is sent
                    header('Location: verify_password_otp.php?email=' . urlencode($email));
                    exit;
                } else {
                    $error_message = "Error sending OTP: " . $sendStatus;
                }
            } else {
                $error_message = "If this email is registered, you will receive a reset code shortly.";
            }
        }
    }
} catch (Exception $e) {
    // Show detailed error for debugging (remove this in production)
    $error_message = "Debug Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
    // Log the actual error for debugging
    error_log("Forgot Password Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Forgot Password</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --button-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin: 0;
        }

        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle, rgba(255,255,255,0.08) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-container img {
            width: 70px;
            height: 70px;
            object-fit: contain;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .form-floating {
            margin-bottom: 20px;
            position: relative;
        }

        .form-floating .form-control {
            height: 60px;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            padding: 20px 20px 8px 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--text-primary);
        }

        .form-floating .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 20px 20px 8px 20px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .btn-auth {
            height: 60px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--button-gradient);
            border: none;
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
        }

        .btn-auth::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-auth:hover::before {
            left: 100%;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
        }

        .btn-auth:active {
            transform: translateY(-1px);
        }

        .auth-links {
            text-align: center;
            margin-top: 30px;
        }

        .auth-links a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #4f46e5;
            text-decoration: underline;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .auth-card {
                padding: 30px 25px;
                margin: 10px;
            }

            .auth-title {
                font-size: 1.75rem;
            }

            .form-floating .form-control {
                height: 55px;
                padding: 18px 15px 6px 15px;
            }

            .form-floating label {
                padding: 18px 15px 6px 15px;
            }

            .btn-auth {
                height: 55px;
                font-size: 1rem;
            }
        }

        /* Loading Animation */
        .btn-auth.loading {
            pointer-events: none;
        }

        .btn-auth.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut Logo">
                </div>
                <h1 class="auth-title">Forgot Password?</h1>
                <p class="auth-subtitle">Enter your email to receive a reset code</p>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="forgot_password.php" id="forgotPasswordForm">
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                    <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
                </div>

                <button type="submit" class="btn btn-auth w-100">
                    <i class="fas fa-paper-plane me-2"></i>Send Reset Code
                </button>
            </form>

            <div class="auth-links">
                <a href="signin.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Sign In
                </a>
                <p class="mt-3 mb-0">
                    Don't have an account?
                    <a href="signup.php">
                        <i class="fas fa-user-plus me-1"></i>Create Account
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission with loading state
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Sending Code...</span>';
        });

        // Enhanced form validation
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });
    </script>
</body>
</html>
