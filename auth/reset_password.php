<?php
require '../config/db_connect.php';
require '../config/logger.php';

$error_message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'];
    $newPassword = $_POST['new_password'];

    // Password policy validation
    if (!preg_match("/^(?=.*[A-Za-z])(?=.*\d)(?=.*[^\w\s]).{12,20}$/", $newPassword)) {
        $error_message = "Password must be 12-20 characters long, contain at least one letter, one number, and one special character (no spaces allowed).";
    } else {
        try {
            $logger = getLogger();

            // Get user info for logging
            $stmt = $pdo->prepare("SELECT id, username FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if ($user) {
                // Hash the new password for security
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

                // Update the user's password
                $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
                $stmt->execute([$hashedPassword, $email]);

                // Log the password reset
                $logger->logUserProfileChange($user['id'], $user['username'], 'password_hash', 'REDACTED', 'REDACTED');

                // Log security event
                $logger->logSecurityEvent('PASSWORD_RESET', $user['username'], $user['id'], 'HIGH', [
                    'email' => $email,
                    'method' => 'password_reset_form',
                    'reset_via' => 'email_verification'
                ]);

                // Optionally, delete OTP record to prevent reuse
                $pdo->prepare("DELETE FROM otp_verifications WHERE email = ?")->execute([$email]);

                $success = true;
            } else {
                $error_message = "User not found.";
            }
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            $error_message = "An error occurred while resetting your password.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Reset Password</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --button-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin: 0;
        }

        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle, rgba(255,255,255,0.08) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-container img {
            width: 70px;
            height: 70px;
            object-fit: contain;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .form-floating {
            margin-bottom: 20px;
            position: relative;
        }

        .form-floating .form-control {
            height: 60px;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            padding: 20px 50px 8px 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--text-primary);
        }

        .form-floating .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 20px 20px 8px 20px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.1rem;
            cursor: pointer;
            z-index: 10;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--accent-color);
        }

        .password-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.15);
        }

        .btn-auth {
            height: 40px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--button-gradient);
            border: none;
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
        }

        .btn-auth::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-auth:hover::before {
            left: 100%;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
        }

        .btn-auth:active {
            transform: translateY(-1px);
        }

        .auth-links {
            text-align: center;
            margin-top: 30px;
        }

        .auth-links a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #4f46e5;
            text-decoration: underline;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        .password-requirements {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .password-requirements h6 {
            color: var(--accent-color);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .password-requirements ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .password-requirements li {
            padding: 5px 0;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .password-requirements li i {
            width: 16px;
            margin-right: 8px;
            font-size: 0.8rem;
        }

        .password-requirements li.valid {
            color: #28a745;
        }

        .password-requirements li.invalid {
            color: #dc3545;
        }

        .password-requirements li.valid i::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .password-requirements li.invalid i::before {
            content: '\f00d';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .password-requirements li i::before {
            content: '\f111';
            font-family: 'Font Awesome 6 Free';
            font-weight: 400;
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .auth-card {
                padding: 30px 25px;
                margin: 10px;
            }

            .auth-title {
                font-size: 1.75rem;
            }

            .form-floating .form-control {
                height: 55px;
                padding: 18px 45px 6px 15px;
            }

            .form-floating label {
                padding: 18px 15px 6px 15px;
            }

            .btn-auth {
                height: 55px;
                font-size: 1rem;
            }
        }

        /* Loading Animation */
        .btn-auth.loading {
            pointer-events: none;
        }

        .btn-auth.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut Logo">
                </div>
                <?php if ($success): ?>
                    <h1 class="auth-title">Password Updated!</h1>
                    <p class="auth-subtitle">Your account is now secure with your new password</p>
                <?php else: ?>
                    <h1 class="auth-title">Reset Password</h1>
                    <p class="auth-subtitle">Create a new secure password for your account</p>
                <?php endif; ?>
            </div>

            <?php if ($success): ?>
                <!-- Success State -->
                <div class="success-section text-center">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle" style="font-size: 4rem; color: #10b981;"></i>
                    </div>
                    <h3 style="color: var(--text-primary); margin-bottom: 15px;">Password Reset Successful!</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px;">
                        Your password has been successfully updated. You can now sign in with your new password.
                    </p>

                    <a href="signin.php" class="btn btn-auth w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In Now
                    </a>

                    <div class="success-info" style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 12px; padding: 15px; margin-top: 20px;">
                        <i class="fas fa-info-circle me-2" style="color: #10b981;"></i>
                        <small style="color: var(--text-secondary);">
                            For security reasons, you'll need to sign in again with your new password.
                        </small>
                    </div>
                </div>
            <?php else: ?>
                <!-- Password Reset Form -->
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <div class="password-requirements">
                    <h6><i class="fas fa-shield-alt me-2"></i>Password Requirements:</h6>
                    <ul>
                        <li id="req-length"><i></i>12-20 characters long</li>
                        <li id="req-letter"><i></i>Contains letters</li>
                        <li id="req-number"><i></i>Contains numbers</li>
                        <li id="req-special"><i></i>At least one special character</li>
                        <li id="req-space"><i></i>No spaces or emoji</li>
                    </ul>
                </div>

                <form method="POST" action="reset_password.php" id="resetPasswordForm">
                    <input type="hidden" name="email" value="<?php echo htmlspecialchars($_GET['email'] ?? ''); ?>" />

                    <div class="form-floating position-relative">
                        <input type="password" class="form-control" id="new_password" name="new_password"
                               placeholder="Enter new password" required>
                        <label for="new_password"><i class="fas fa-lock me-2"></i>New Password</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                            <i class="fas fa-eye" id="new_password-toggle-icon"></i>
                        </button>
                    </div>

                    <button type="submit" class="btn btn-auth w-100">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </button>
                </form>
            <?php endif; ?>

            <div class="auth-links">
                <?php if ($success): ?>
                    <!-- Success State Links -->
                    <p class="mt-3 mb-0">
                        Need help?
                        <a href="mailto:<EMAIL>">
                            <i class="fas fa-envelope me-1"></i>Contact Support
                        </a>
                    </p>
                <?php else: ?>
                    <!-- Form State Links -->
                    <a href="signin.php">
                        <i class="fas fa-arrow-left me-1"></i>Back to Sign In
                    </a>
                    <p class="mt-3 mb-0">
                        Remember your password?
                        <a href="signin.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Sign In
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission with loading state
        document.getElementById('resetPasswordForm')?.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Resetting Password...</span>';
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Enhanced form validation
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });

        // Enhanced password validation with live requirements checking
        const passwordInput = document.getElementById('new_password');
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;

                // Define requirements with their corresponding elements
                const requirements = {
                    length: {
                        test: password.length >= 12 && password.length <= 20,
                        element: document.getElementById('req-length')
                    },
                    letter: {
                        test: /[A-Za-z]/.test(password),
                        element: document.getElementById('req-letter')
                    },
                    number: {
                        test: /\d/.test(password),
                        element: document.getElementById('req-number')
                    },
                    special: {
                        test: /[^\w\s]/.test(password),
                        element: document.getElementById('req-special')
                    },
                    space: {
                        test: !/\s/.test(password) && !/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(password),
                        element: document.getElementById('req-space')
                    }
                };

                let allValid = true;
                let hasContent = password.length > 0;

                // Update each requirement
                Object.keys(requirements).forEach(key => {
                    const req = requirements[key];
                    const element = req.element;

                    if (!hasContent) {
                        // Reset to default state when no input
                        element.className = '';
                    } else if (req.test) {
                        element.className = 'valid';
                    } else {
                        element.className = 'invalid';
                        allValid = false;
                    }
                });

                // Update input border based on overall validation
                if (!hasContent) {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                } else if (allValid) {
                    this.style.borderColor = '#28a745';
                    this.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                } else {
                    this.style.borderColor = '#dc3545';
                    this.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
                }

                // Enable/disable submit button based on validation
                const submitBtn = document.querySelector('#resetPasswordForm button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = !allValid || !hasContent;
                    if (!allValid || !hasContent) {
                        submitBtn.style.opacity = '0.6';
                        submitBtn.style.cursor = 'not-allowed';
                    } else {
                        submitBtn.style.opacity = '1';
                        submitBtn.style.cursor = 'pointer';
                    }
                }
            });
        }
    </script>
</body>
</html>
