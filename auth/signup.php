<?php
require '../config/db_connect.php';
require '../config/logger.php';

$logger = getLogger();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $email = $_POST['email'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        // Basic input validation
        if (empty($email) || empty($username) || empty($password)) {
            echo "<div class='alert alert-danger'>All fields are required.</div>";
            $logger->logSecurityEvent('SIGNUP_MISSING_FIELDS', $email, null, 'LOW', [
                'email' => $email,
                'username' => $username,
                'reason' => 'Missing required fields'
            ]);
        } else {
            // Password policy validation
            if (!preg_match("/^(?=.*[A-Za-z])(?=.*\d)(?=.*[^\w\s]).{12,20}$/", $password)) {
                echo "<div class='alert alert-danger'>Password must be 12-20 characters long, contain at least one letter, one number, and one special character (no spaces allowed).</div>";
                $logger->logSecurityEvent('SIGNUP_WEAK_PASSWORD', $email, null, 'MEDIUM', [
                    'email' => $email,
                    'username' => $username,
                    'reason' => 'Password does not meet policy requirements'
                ]);
            } else {
                // Email uniqueness check
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    echo "<div class='alert alert-danger'>This email is already registered.</div>";
                    $logger->logSecurityEvent('SIGNUP_DUPLICATE_EMAIL', $email, null, 'LOW', [
                        'email' => $email,
                        'username' => $username,
                        'reason' => 'Attempted signup with existing email'
                    ]);
                } else {
                    // Set the timezone for PHP
                    date_default_timezone_set('Asia/Kuala_Lumpur');
                    $otp = rand(100000, 999999);
                    $expires_at = date('Y-m-d H:i:s', strtotime('+10 minutes'));

                    // Insert OTP into database
                    $stmt = $pdo->prepare("INSERT INTO otp_verifications (email, otp_code, expires_at, type) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$email, $otp, $expires_at, 'signup']);

                    // Load OTP sender and send OTP to the user's email
                    require_once 'send_otp.php';
                    $result = sendOTP($email, $otp);

                    if ($result === true) {
                        // Log successful OTP generation and sending
                        $logger->logSecurityEvent('SIGNUP_OTP_SENT', $email, null, 'LOW', [
                            'email' => $email,
                            'username' => $username,
                            'otp_expires_at' => $expires_at
                        ]);

                        // Redirect to OTP verification page
                        header("Location: verify_otp.php?email=" . urlencode($email) . "&username=" . urlencode($username) . "&password=" . urlencode($password));
                        exit;
                    } else {
                        // Log OTP sending failure
                        $logger->logSecurityEvent('SIGNUP_OTP_FAILED', $email, null, 'MEDIUM', [
                            'email' => $email,
                            'username' => $username,
                            'error' => $result
                        ]);
                        echo $result;
                    }
                }
            }
        }
    } catch (Exception $e) {
        error_log("Signup error: " . $e->getMessage());
        echo "<div class='alert alert-danger'>An error occurred during signup. Please try again.</div>";
        $logger->logSecurityEvent('SIGNUP_ERROR', $email ?? 'unknown', null, 'HIGH', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Sign Up</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --button-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            margin: 0;
        }

        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle, rgba(255,255,255,0.08) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: visible;
        }



        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-container img {
            width: 70px;
            height: 70px;
            object-fit: contain;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .auth-subtitle {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .form-floating {
            margin-bottom: 20px;
            position: relative;
        }

        .form-floating .form-control {
            height: 60px;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            padding: 20px 20px 8px 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--text-primary);
        }

        .form-floating .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 20px 20px 8px 20px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Password Toggle Button */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.1rem;
            cursor: pointer;
            z-index: 10;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .password-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }

        /* Custom Tooltip Styling */
        .tooltip {
            font-size: 0.875rem;
        }

        .tooltip-inner {
            background: rgba(255, 255, 255, 0.98);
            color: #495057;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            padding: 15px;
            max-width: 300px;
            text-align: left;
        }

        .tooltip-requirements {
            font-family: 'Inter', sans-serif;
        }

        .tooltip-requirements strong {
            color: #667eea;
            display: block;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .tooltip-requirements ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tooltip-requirements li {
            padding: 4px 0;
            font-size: 0.8rem;
            color: #6c757d;
            transition: color 0.3s ease;
        }

        .tooltip-requirements li.valid {
            color: #28a745;
            font-weight: 500;
        }

        .bs-tooltip-end .tooltip-arrow::before {
            border-right-color: rgba(255, 255, 255, 0.98);
        }

        /* Live Password Requirements */
        .password-requirements-live {
            position: absolute;
            top: 0;
            left: calc(100% + 20px);
            width: 280px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .password-requirements-live.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        /* Arrow pointing to the password field */
        .password-requirements-live::before {
            content: '';
            position: absolute;
            top: 20px;
            left: -8px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 8px solid rgba(255, 255, 255, 0.98);
            filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.1));
        }

        .requirements-header {
            color: var(--accent-color);
            font-size: 0.9rem;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .requirements-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            padding: 6px 0;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .requirement-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            color: #dc3545;
            transition: all 0.3s ease;
        }

        .requirement-item.valid .requirement-icon {
            color: #28a745;
        }

        .requirement-item.valid .requirement-icon:before {
            content: '\f00c'; /* fa-check */
        }

        .requirement-item span {
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .requirement-item.valid span {
            color: #28a745;
            font-weight: 500;
        }

        /* Show requirements when password field is focused */
        .form-floating:focus-within .password-requirements-live {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .form-check {
            margin: 25px 0;
        }

        .form-check-input {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid var(--border-color);
            margin-top: 0;
            background-color: transparent;
            background-image: none;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .form-check-input:checked {
            background-color: transparent;
            border-color: var(--accent-color);
            background-image: none;
        }

        .form-check-input:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--accent-color);
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
        }

        .form-check-input:hover {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.1rem rgba(99, 102, 241, 0.1);
        }

        .form-check-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-left: 10px;
        }

        .btn-auth {
            height: 60px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--button-gradient);
            border: none;
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
        }

        .btn-auth::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-auth:hover::before {
            left: 100%;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
        }

        .btn-auth:active {
            transform: translateY(-1px);
        }

        .auth-links {
            text-align: center;
            margin-top: 30px;
        }

        .auth-links a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #4f46e5;
            text-decoration: underline;
        }

        .divider {
            margin: 20px 0;
            text-align: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 0%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #dee2e6, transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .password-requirements-live {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                width: auto;
                margin-top: 8px;
                transform: translateY(-10px);
            }

            .password-requirements-live.show {
                transform: translateY(0);
            }

            .password-requirements-live::before {
                display: none;
            }
        }

        @media (max-width: 576px) {
            .auth-card {
                padding: 30px 25px;
                margin: 10px;
            }

            .auth-title {
                font-size: 1.75rem;
            }

            .form-floating .form-control {
                height: 55px;
                padding: 18px 15px 6px 15px;
            }

            .form-floating label {
                padding: 18px 15px 6px 15px;
            }

            .btn-auth {
                height: 55px;
                font-size: 1rem;
            }

            .password-requirements-live {
                width: auto;
                left: 0;
                right: 0;
            }
        }

        /* Loading Animation */
        .btn-auth.loading {
            pointer-events: none;
        }

        .btn-auth.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut Logo">
                </div>
                <h1 class="auth-title">Join TryMeOut</h1>
                <p class="auth-subtitle">Start your cybersecurity learning journey today</p>
            </div>

            <form method="POST" action="signup.php" id="signupForm">
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
                    <label for="username"><i class="fas fa-user me-2"></i>Username</label>
                </div>

                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                    <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
                </div>

                <div class="form-floating position-relative">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                    </button>
                    <div class="password-requirements-live" id="password-requirements">
                        <div class="requirements-header">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>Password Requirements</strong>
                        </div>
                        <ul class="requirements-list">
                            <li id="length-req" class="requirement-item">
                                <i class="fas fa-times requirement-icon"></i>
                                <span>12-20 characters long</span>
                            </li>
                            <li id="letter-req" class="requirement-item">
                                <i class="fas fa-times requirement-icon"></i>
                                <span>Contains letters</span>
                            </li>
                            <li id="number-req" class="requirement-item">
                                <i class="fas fa-times requirement-icon"></i>
                                <span>Contains numbers</span>
                            </li>
                            <li id="special-req" class="requirement-item">
                                <i class="fas fa-times requirement-icon"></i>
                                <span>Contains special characters</span>
                            </li>
                            <li id="space-req" class="requirement-item">
                                <i class="fas fa-times requirement-icon"></i>
                                <span>No spaces or emoji</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                    <label class="form-check-label" for="agreeTerms">
                        <i class="fas fa-check-circle me-1"></i>I agree to the
                        <a href="#" class="text-decoration-none">Terms and Conditions</a>
                    </label>
                </div>

                <button type="submit" class="btn btn-auth w-100">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </button>
            </form>

            <div class="divider">
            </div>

            <div class="auth-links">
                <p class="mb-0">
                    Already have an account?
                    <a href="signin.php">
                        <i class="fas fa-sign-in-alt me-1"></i>Sign In
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission with loading state
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Creating Account...</span>';
        });

        // Enhanced form validation
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Live password requirements checking
        const passwordInput = document.getElementById('password');
        const requirementsContainer = document.getElementById('password-requirements');
        const requirements = {
            'length-req': /^.{12,20}$/,
            'letter-req': /[A-Za-z]/,
            'number-req': /\d/,
            'special-req': /[^\w\s]/,
            'space-req': /^[^\s]*$/
        };

        // Show/hide requirements on focus/blur
        passwordInput.addEventListener('focus', function() {
            requirementsContainer.classList.add('show');
        });

        passwordInput.addEventListener('blur', function() {
            // Hide after a short delay to allow for interaction
            setTimeout(() => {
                if (document.activeElement !== passwordInput) {
                    requirementsContainer.classList.remove('show');
                }
            }, 150);
        });

        // Real-time password validation
        passwordInput.addEventListener('input', function() {
            const password = this.value;

            // Show requirements if there's input
            if (password.length > 0) {
                requirementsContainer.classList.add('show');
            }

            // Check each requirement
            Object.keys(requirements).forEach(reqId => {
                const element = document.getElementById(reqId);
                if (element) {
                    if (requirements[reqId].test(password)) {
                        element.classList.add('valid');
                    } else {
                        element.classList.remove('valid');
                    }
                }
            });

            // Check if all requirements are met
            const allValid = Object.keys(requirements).every(reqId => {
                return requirements[reqId].test(password);
            });

            // Update password field styling based on validation
            if (password.length > 0) {
                if (allValid) {
                    passwordInput.style.borderColor = '#28a745';
                    passwordInput.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                } else {
                    passwordInput.style.borderColor = '#dc3545';
                    passwordInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
                }
            } else {
                passwordInput.style.borderColor = '';
                passwordInput.style.boxShadow = '';
            }
        });

        // Hide requirements when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.form-floating')) {
                requirementsContainer.classList.remove('show');
            }
        });

        // Terms and conditions modal (placeholder)
        document.querySelector('a[href="#"]').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Terms and Conditions modal would open here in a real implementation.');
        });
    </script>
</body>
</html>
