<?php
// Debug Failed Login Logging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../config/db_connect.php';
require '../config/logger.php';
require '../config/brute_force_protection.php';

echo "<h2>🔍 Debug Failed Login Logging</h2>";

try {
    $logger = getLogger();
    $bruteForceProtection = new BruteForceProtection($pdo);
    
    // Get a real user for testing
    $stmt = $pdo->query("SELECT id, email, password_hash FROM users LIMIT 1");
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "❌ No users found for testing<br>";
        exit;
    }
    
    echo "<h3>Testing with user: " . htmlspecialchars($test_user['email']) . " (ID: " . $test_user['id'] . ")</h3>";
    
    // Simulate the exact signin.php logic for failed password
    $email = $test_user['email'];
    $wrong_password = 'definitely_wrong_password_123';
    
    echo "<h4>Step 1: Check if user exists</h4>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ User found: " . htmlspecialchars($user['email']) . " (ID: " . $user['id'] . ")<br>";
        
        echo "<h4>Step 2: Check if account is locked</h4>";
        if ($bruteForceProtection->isAccountLocked($email)) {
            echo "⚠️ Account is currently locked<br>";
        } else {
            echo "✅ Account is not locked<br>";
            
            echo "<h4>Step 3: Verify password</h4>";
            if (password_verify($wrong_password, $user['password_hash'])) {
                echo "✅ Password is correct (this shouldn't happen!)<br>";
            } else {
                echo "❌ Password is incorrect (expected)<br>";
                
                echo "<h4>Step 4: Record failed attempt in brute force protection</h4>";
                try {
                    $attempt_result = $bruteForceProtection->recordFailedAttempt($email);
                    echo "✅ Brute force protection recorded failed attempt<br>";
                    echo "Remaining attempts: " . ($attempt_result['remaining_attempts'] ?? 'Unknown') . "<br>";
                    echo "Account locked: " . ($attempt_result['locked'] ? 'Yes' : 'No') . "<br>";
                } catch (Exception $e) {
                    echo "❌ Brute force protection error: " . $e->getMessage() . "<br>";
                }
                
                echo "<h4>Step 5: Log failed attempt</h4>";
                try {
                    echo "Calling: \$logger->logLoginAttempt('$email', false, {$user['id']}, 'Invalid password')<br>";
                    $logger->logLoginAttempt($email, false, $user['id'], 'Invalid password');
                    echo "✅ Failed login attempt logged<br>";
                } catch (Exception $e) {
                    echo "❌ Logging error: " . $e->getMessage() . "<br>";
                    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
                }
                
                echo "<h4>Step 6: Verify the log was inserted</h4>";
                try {
                    $stmt = $pdo->prepare("
                        SELECT email, user_id, success, failure_reason, created_at 
                        FROM login_attempts 
                        WHERE email = ? AND success = 0 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    ");
                    $stmt->execute([$email]);
                    $logged_attempt = $stmt->fetch();
                    
                    if ($logged_attempt) {
                        echo "✅ Failed attempt found in database:<br>";
                        echo "<table border='1' style='border-collapse: collapse;'>";
                        echo "<tr><th>Email</th><th>User ID</th><th>Success</th><th>Failure Reason</th><th>Time</th></tr>";
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($logged_attempt['email']) . "</td>";
                        echo "<td>" . ($logged_attempt['user_id'] ?? 'NULL') . "</td>";
                        echo "<td>" . ($logged_attempt['success'] ? 'TRUE' : 'FALSE') . "</td>";
                        echo "<td>" . htmlspecialchars($logged_attempt['failure_reason'] ?? 'NULL') . "</td>";
                        echo "<td>" . $logged_attempt['created_at'] . "</td>";
                        echo "</tr>";
                        echo "</table>";
                    } else {
                        echo "❌ No failed attempt found in database!<br>";
                        
                        // Check if any attempt was logged at all
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) 
                            FROM login_attempts 
                            WHERE email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                        ");
                        $stmt->execute([$email]);
                        $recent_count = $stmt->fetchColumn();
                        echo "Recent attempts for this email: $recent_count<br>";
                    }
                } catch (Exception $e) {
                    echo "❌ Database check error: " . $e->getMessage() . "<br>";
                }
            }
        }
    } else {
        echo "❌ User not found<br>";
    }
    
    echo "<h4>Step 7: Test Admin Dashboard Query</h4>";
    try {
        $admin_query = "
            SELECT id, email, user_id, ip_address, country, city, user_agent, success, failure_reason, created_at
            FROM login_attempts
            WHERE email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY created_at DESC
        ";
        $stmt = $pdo->prepare($admin_query);
        $stmt->execute([$email]);
        $admin_results = $stmt->fetchAll();
        
        echo "Admin dashboard query returned " . count($admin_results) . " records for this email<br>";
        
        if ($admin_results) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Status</th><th>Email</th><th>User ID</th><th>Failure Reason</th><th>Time</th></tr>";
            
            foreach ($admin_results as $result) {
                $status = $result['success'] ? 'SUCCESS' : 'FAILED';
                $status_color = $result['success'] ? 'green' : 'red';
                
                echo "<tr>";
                echo "<td style='color: $status_color; font-weight: bold;'>$status</td>";
                echo "<td>" . htmlspecialchars($result['email']) . "</td>";
                echo "<td>" . ($result['user_id'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($result['failure_reason'] ?? 'N/A') . "</td>";
                echo "<td>" . date('M j, g:i A', strtotime($result['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "❌ Admin query error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h4>Step 8: Cleanup</h4>";
    try {
        // Clean up the test failed attempt
        $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE email = ? AND failure_reason = 'Invalid password' AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
        $stmt->execute([$email]);
        echo "✅ Test records cleaned up<br>";
        
        // Clear brute force attempts
        $bruteForceProtection->clearFailedAttempts($email);
        echo "✅ Brute force attempts cleared<br>";
        
    } catch (Exception $e) {
        echo "⚠️ Cleanup error: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Debug Test Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffc107;'>";
echo "<h4>🧪 Manual Test Instructions</h4>";
echo "<p>After running this debug test:</p>";
echo "<ol>";
echo "<li>Go to <a href='signin.php' target='_blank'>signin.php</a></li>";
echo "<li>Use the email: <strong>" . htmlspecialchars($test_user['email'] ?? 'your-email') . "</strong></li>";
echo "<li>Enter a wrong password</li>";
echo "<li>Submit the form</li>";
echo "<li>Check <a href='../admin/logs.php' target='_blank'>Admin Logs</a> → Login Attempts tab</li>";
echo "<li>Should see a red 'FAILED' entry</li>";
echo "</ol>";
echo "</div>";

echo "<p style='margin-top: 20px;'>";
echo "<a href='signin.php'>← Test Sign In</a> | ";
echo "<a href='../admin/logs.php'>Admin Logs</a>";
echo "</p>";
?>
