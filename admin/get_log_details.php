<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

$log_id = $_GET['id'] ?? null;
$log_type = $_GET['type'] ?? null;

if (!$log_id || !$log_type) {
    echo json_encode(['success' => false, 'message' => 'Log ID and type required']);
    exit;
}

try {
    $details = [];

    switch ($log_type) {
        case 'system':
            // Check if table exists
            $table_check = $pdo->query("SHOW TABLES LIKE 'system_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM system_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Log Level' => $log['log_level'],
                        'Category' => $log['category'],
                        'Message' => $log['message'],
                        'IP Address' => $log['ip_address'] ?? 'N/A',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Details' => json_decode($log['details'], true),
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'security':
            $table_check = $pdo->query("SHOW TABLES LIKE 'security_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM security_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Event Type' => $log['event_type'],
                        'Username' => $log['username'] ?? 'System',
                        'User ID' => $log['user_id'] ?? 'N/A',
                        'IP Address' => $log['ip_address'],
                        'Country' => $log['country'] ?? 'Unknown',
                        'City' => $log['city'] ?? 'Unknown',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Session ID' => $log['session_id'] ?? 'N/A',
                        'Risk Level' => $log['risk_level'],
                        'Details' => json_decode($log['details'], true),
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;
            
        case 'anomaly':
            $stmt = $pdo->prepare("SELECT * FROM anomaly_logs WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();
            
            if ($log) {
                $details = [
                    'Anomaly Type' => $log['anomaly_type'],
                    'User ID' => $log['user_id'] ?? 'N/A',
                    'IP Address' => $log['ip_address'],
                    'Severity' => $log['severity'],
                    'Description' => $log['description'],
                    'Data' => json_decode($log['data'], true),
                    'Resolved' => $log['resolved'] ? 'Yes' : 'No',
                    'Created At' => $log['created_at']
                ];
            }
            break;
            
        case 'login':
            $stmt = $pdo->prepare("SELECT * FROM login_attempts WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();

            if ($log) {
                $details = [
                    'Email' => $log['email'],
                    'IP Address' => $log['ip_address'],
                    'Country' => $log['country'],
                    'City' => $log['city'],
                    'User Agent' => $log['user_agent'],
                    'Success' => $log['success'] ? 'Yes' : 'No',
                    'Failure Reason' => $log['failure_reason'] ?? 'N/A',
                    'Session Duration' => $log['session_duration'] ?? 'N/A',
                    'Created At' => $log['created_at']
                ];
            }
            break;

        case 'audit':
            $table_check = $pdo->query("SHOW TABLES LIKE 'audit_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM audit_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $old_values = json_decode($log['old_values'], true);
                    $new_values = json_decode($log['new_values'], true);

                    $details = [
                        'Action Type' => $log['action_type'],
                        'Table Name' => $log['table_name'],
                        'Record ID' => $log['record_id'] ?? 'N/A',
                        'Username' => $log['username'] ?? 'System',
                        'IP Address' => $log['ip_address'] ?? 'N/A',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Old Values' => $old_values,
                        'New Values' => $new_values,
                        'Changes Summary' => generateChangesSummary($old_values, $new_values),
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'access':
            $table_check = $pdo->query("SHOW TABLES LIKE 'access_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM access_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Username' => $log['username'] ?? 'Anonymous',
                        'IP Address' => $log['ip_address'],
                        'Country' => $log['country'] ?? 'Unknown',
                        'City' => $log['city'] ?? 'Unknown',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Request Method' => $log['request_method'],
                        'Request URI' => $log['request_uri'],
                        'File Path' => $log['file_path'] ?? 'N/A',
                        'Directory Path' => $log['directory_path'] ?? 'N/A',
                        'Access Type' => $log['access_type'],
                        'Response Code' => $log['response_code'],
                        'Response Size' => $log['response_size'] ?? 'N/A',
                        'Referer' => $log['referer'] ?? 'N/A',
                        'Execution Time' => $log['execution_time'] ?? 'N/A',
                        'Blocked' => $log['blocked'] ? 'Yes' : 'No',
                        'Block Reason' => $log['block_reason'] ?? 'N/A',
                        'Risk Level' => $log['risk_level'],
                        'Details' => json_decode($log['details'], true),
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'file_operations':
            $table_check = $pdo->query("SHOW TABLES LIKE 'file_operations_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM file_operations_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Username' => $log['username'] ?? 'Anonymous',
                        'IP Address' => $log['ip_address'],
                        'Operation Type' => $log['operation_type'],
                        'File Path' => $log['file_path'],
                        'File Name' => $log['file_name'],
                        'File Size' => $log['file_size'] ? formatBytes($log['file_size']) : 'N/A',
                        'File Type' => $log['file_type'] ?? 'Unknown',
                        'Old File Path' => $log['old_file_path'] ?? 'N/A',
                        'New File Path' => $log['new_file_path'] ?? 'N/A',
                        'Success' => $log['success'] ? 'Yes' : 'No',
                        'Error Message' => $log['error_message'] ?? 'N/A',
                        'Risk Level' => $log['risk_level'],
                        'Details' => json_decode($log['details'], true),
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'suspicious':
            $table_check = $pdo->query("SHOW TABLES LIKE 'suspicious_activity_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM suspicious_activity_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Username' => $log['username'] ?? 'Anonymous',
                        'IP Address' => $log['ip_address'],
                        'Activity Type' => $log['activity_type'],
                        'Severity' => $log['severity'],
                        'Description' => $log['description'],
                        'Request Data' => json_decode($log['request_data'], true),
                        'Pattern Matched' => $log['pattern_matched'] ?? 'N/A',
                        'Auto Blocked' => $log['auto_blocked'] ? 'Yes' : 'No',
                        'Resolved' => $log['resolved'] ? 'Yes' : 'No',
                        'Resolved By' => $log['resolved_by'] ?? 'N/A',
                        'Resolved At' => $log['resolved_at'] ?? 'N/A',
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'admin_actions':
            $table_check = $pdo->query("SHOW TABLES LIKE 'admin_actions_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM admin_actions_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Admin Username' => $log['admin_username'],
                        'Action Type' => $log['action_type'],
                        'Action Description' => $log['action_description'],
                        'Target User ID' => $log['target_user_id'] ?? 'N/A',
                        'Target Username' => $log['target_username'] ?? 'N/A',
                        'Old Values' => json_decode($log['old_values'], true),
                        'New Values' => json_decode($log['new_values'], true),
                        'IP Address' => $log['ip_address'],
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Success' => $log['success'] ? 'Yes' : 'No',
                        'Error Message' => $log['error_message'] ?? 'N/A',
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'error':
            $table_check = $pdo->query("SHOW TABLES LIKE 'error_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM error_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Error Type' => $log['error_type'],
                        'Error Message' => $log['error_message'],
                        'File Path' => $log['file_path'] ?? 'N/A',
                        'Line Number' => $log['line_number'] ?? 'N/A',
                        'Stack Trace' => $log['stack_trace'] ?? 'N/A',
                        'User ID' => $log['user_id'] ?? 'N/A',
                        'Session ID' => $log['session_id'] ?? 'N/A',
                        'Request Data' => json_decode($log['request_data'], true),
                        'IP Address' => $log['ip_address'] ?? 'N/A',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'login':
            $table_check = $pdo->query("SHOW TABLES LIKE 'login_attempts'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM login_attempts WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Email' => $log['email'],
                        'User ID' => $log['user_id'] ?? 'N/A',
                        'IP Address' => $log['ip_address'],
                        'Country' => $log['country'] ?? 'Unknown',
                        'City' => $log['city'] ?? 'Unknown',
                        'User Agent' => $log['user_agent'] ?? 'N/A',
                        'Success' => $log['success'] ? 'Yes' : 'No',
                        'Failure Reason' => $log['failure_reason'] ?? 'N/A',
                        'Session ID' => $log['session_id'] ?? 'N/A',
                        'Two Factor Used' => $log['two_factor_used'] ? 'Yes' : 'No',
                        'Remember Me' => $log['remember_me'] ? 'Yes' : 'No',
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        case 'anomaly':
            $table_check = $pdo->query("SHOW TABLES LIKE 'anomaly_logs'");
            if ($table_check->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT * FROM anomaly_logs WHERE id = ?");
                $stmt->execute([$log_id]);
                $log = $stmt->fetch();

                if ($log) {
                    $details = [
                        'Anomaly Type' => $log['anomaly_type'],
                        'User ID' => $log['user_id'] ?? 'N/A',
                        'IP Address' => $log['ip_address'],
                        'Severity' => $log['severity'],
                        'Description' => $log['description'],
                        'Threshold Value' => $log['threshold_value'] ?? 'N/A',
                        'Actual Value' => $log['actual_value'] ?? 'N/A',
                        'Data' => json_decode($log['data'], true),
                        'Resolved' => $log['resolved'] ? 'Yes' : 'No',
                        'Resolved By' => $log['resolved_by'] ?? 'N/A',
                        'Resolved At' => $log['resolved_at'] ?? 'N/A',
                        'Created At' => $log['created_at']
                    ];
                }
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid log type: ' . htmlspecialchars($log_type)]);
            exit;
    }

    if (empty($details)) {
        echo json_encode(['success' => false, 'message' => 'Log entry not found or table does not exist']);
    } else {
        echo json_encode(['success' => true, 'details' => $details, 'log_type' => $log_type, 'log_id' => $log_id]);
    }

} catch (PDOException $e) {
    error_log("Database error in get_log_details.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("Error in get_log_details.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while retrieving log details']);
}

/**
 * Generate a human-readable summary of changes
 */
function generateChangesSummary($old_values, $new_values) {
    if (!is_array($old_values) || !is_array($new_values)) {
        return 'Invalid change data';
    }

    $changes = [];

    // Find all fields that changed
    $all_fields = array_unique(array_merge(array_keys($old_values), array_keys($new_values)));

    foreach ($all_fields as $field) {
        $old_val = $old_values[$field] ?? '[not set]';
        $new_val = $new_values[$field] ?? '[not set]';

        if ($old_val !== $new_val) {
            // Special handling for sensitive fields
            if (in_array($field, ['password_hash', 'password'])) {
                $changes[] = "$field: [REDACTED] → [REDACTED]";
            } else {
                $changes[] = "$field: '$old_val' → '$new_val'";
            }
        }
    }

    return empty($changes) ? 'No changes detected' : implode(', ', $changes);
}

/**
 * Helper function to format bytes
 */
function formatBytes($bytes, $precision = 2) {
    if (!is_numeric($bytes) || $bytes < 0) {
        return 'N/A';
    }

    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
