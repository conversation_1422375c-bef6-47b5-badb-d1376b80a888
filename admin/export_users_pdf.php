<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

// Include TCPDF library (you may need to install this via Composer)
// For now, we'll create a simple HTML-to-PDF solution using DomPDF or similar
// If TCPDF is not available, we'll use a basic HTML approach

try {
    // Get all users with their progress data
    $sql = "
        SELECT
            u.id,
            u.username,
            u.email,
            u.role,
            u.created_at,
            u.is_verified,
            COUNT(DISTINCT up.id) as total_completions,
            COUNT(DISTINCT ub.id) as total_badges,
            COUNT(DISTINCT uc.id) as total_certificates,
            MAX(up.completed_at) as last_activity
        FROM users u
        LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        LEFT JOIN user_badges ub ON u.id = ub.user_id
        LEFT JOIN user_certificates uc ON u.id = uc.user_id
        GROUP BY u.id, u.username, u.email, u.role, u.created_at, u.is_verified
        ORDER BY u.created_at DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate statistics
    $total_users = count($users);
    $admin_users = count(array_filter($users, function($u) { return $u['role'] === 'admin'; }));
    $student_users = count(array_filter($users, function($u) { return $u['role'] === 'student'; }));
    $verified_users = count(array_filter($users, function($u) { return $u['is_verified']; }));
    $active_users = count(array_filter($users, function($u) { return $u['total_completions'] > 0; }));

    // Set headers for PDF download
    $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.pdf';

    // Start output buffering
    ob_start();

    // Create HTML content for PDF
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Users Export Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
                margin: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #2563eb;
                padding-bottom: 20px;
            }
            .header h1 {
                color: #2563eb;
                margin: 0;
                font-size: 24px;
            }
            .header p {
                margin: 5px 0;
                color: #666;
            }
            .summary {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 30px;
                border-left: 4px solid #2563eb;
            }
            .summary h2 {
                color: #2563eb;
                margin-top: 0;
                font-size: 18px;
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                margin-top: 15px;
            }
            .summary-item {
                text-align: center;
                padding: 10px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
            .summary-value {
                font-size: 24px;
                font-weight: bold;
                color: #2563eb;
                display: block;
            }
            .summary-label {
                font-size: 11px;
                color: #666;
                margin-top: 5px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                font-size: 10px;
            }
            th {
                background: #2563eb;
                color: white;
                padding: 8px 6px;
                text-align: left;
                font-weight: bold;
            }
            td {
                padding: 6px;
                border-bottom: 1px solid #e5e7eb;
            }
            tr:nth-child(even) {
                background: #f9fafb;
            }
            .role-admin {
                background: #fee2e2;
                color: #dc2626;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
            }
            .role-student {
                background: #dbeafe;
                color: #2563eb;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
            }
            .verified-yes {
                color: #059669;
                font-weight: bold;
            }
            .verified-no {
                color: #d97706;
                font-weight: bold;
            }
            .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #666;
                border-top: 1px solid #e5e7eb;
                padding-top: 15px;
            }
            .page-break {
                page-break-before: always;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>TryMeOut - Users Export Report</h1>
            <p>Vulnerability Web Application Platform</p>
            <p>Generated on: <?= date('F j, Y \a\t g:i A') ?></p>
            <p>Exported by: <?= htmlspecialchars($_SESSION['username'] ?? 'Admin') ?></p>
        </div>

        <div class="summary">
            <h2>Summary Statistics</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-value"><?= $total_users ?></span>
                    <div class="summary-label">Total Users</div>
                </div>
                <div class="summary-item">
                    <span class="summary-value"><?= $admin_users ?></span>
                    <div class="summary-label">Admin Users</div>
                </div>
                <div class="summary-item">
                    <span class="summary-value"><?= $student_users ?></span>
                    <div class="summary-label">Student Users</div>
                </div>
                <div class="summary-item">
                    <span class="summary-value"><?= $verified_users ?></span>
                    <div class="summary-label">Verified Users</div>
                </div>
                <div class="summary-item">
                    <span class="summary-value"><?= $active_users ?></span>
                    <div class="summary-label">Active Users</div>
                </div>
                <div class="summary-item">
                    <span class="summary-value"><?= round(($active_users / max($total_users, 1)) * 100, 1) ?>%</span>
                    <div class="summary-label">Engagement Rate</div>
                </div>
            </div>
        </div>

        <h2 style="color: #2563eb; margin-bottom: 15px;">User Details</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Verified</th>
                    <th>Joined</th>
                    <th>Completions</th>
                    <th>Badges</th>
                    <th>Certificates</th>
                    <th>Last Activity</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                    <tr>
                        <td><?= $user['id'] ?></td>
                        <td><?= htmlspecialchars($user['username']) ?></td>
                        <td><?= htmlspecialchars($user['email']) ?></td>
                        <td>
                            <span class="role-<?= $user['role'] ?>">
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </td>
                        <td class="verified-<?= $user['is_verified'] ? 'yes' : 'no' ?>">
                            <?= $user['is_verified'] ? 'Yes' : 'No' ?>
                        </td>
                        <td><?= date('M j, Y', strtotime($user['created_at'])) ?></td>
                        <td style="text-align: center;"><?= $user['total_completions'] ?></td>
                        <td style="text-align: center;"><?= $user['total_badges'] ?></td>
                        <td style="text-align: center;"><?= $user['total_certificates'] ?></td>
                        <td>
                            <?= $user['last_activity'] ? date('M j, Y', strtotime($user['last_activity'])) : 'No Activity' ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="footer">
            <p>This report contains confidential information. Handle with care.</p>
            <p>TryMeOut Vulnerability Platform - Admin Dashboard Export</p>
        </div>
    </body>
    </html>
    <?php

    $html = ob_get_clean();

    // Check if we want to download directly or show in browser
    $download = isset($_GET['download']) && $_GET['download'] === 'true';

    if ($download) {
        // For direct download, we'll use a different approach
        // Set headers for HTML display that will be printed to PDF
        header('Content-Type: text/html; charset=utf-8');

        // Add enhanced print styles and auto-print JavaScript
        echo '<script>
            window.onload = function() {
                // Auto-trigger print dialog for PDF generation
                setTimeout(function() {
                    window.print();
                    // Close window after printing (optional)
                    setTimeout(function() {
                        window.close();
                    }, 2000);
                }, 1000);
            };

            // Add comprehensive print-specific styles
            var style = document.createElement("style");
            style.textContent = `
                @media print {
                    body {
                        margin: 0;
                        font-size: 12px;
                        line-height: 1.3;
                    }
                    .no-print { display: none; }
                    @page {
                        margin: 1.5cm;
                        size: A4;
                    }
                    table {
                        page-break-inside: auto;
                        font-size: 10px;
                    }
                    tr {
                        page-break-inside: avoid;
                        page-break-after: auto;
                    }
                    .header {
                        page-break-after: avoid;
                    }
                    .summary {
                        page-break-after: avoid;
                        margin-bottom: 20px;
                    }
                    .summary-grid {
                        display: block !important;
                    }
                    .summary-item {
                        display: inline-block;
                        width: 30%;
                        margin: 5px 1%;
                    }
                }
                @media screen {
                    body {
                        background: #f5f5f5;
                        padding: 20px;
                    }
                    .container {
                        background: white;
                        max-width: 210mm;
                        margin: 0 auto;
                        padding: 20mm;
                        box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    }
                }
            `;
            document.head.appendChild(style);
        </script>';

        echo '<div class="container">' . $html . '</div>';

        // Add a print button for manual printing
        echo '<div class="no-print" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-print"></i> Print to PDF
                </button>
                <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                    Close
                </button>
              </div>';
    } else {
        // Just display the HTML for preview
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo '<h1>Export Error</h1><p>Failed to generate PDF: ' . htmlspecialchars($e->getMessage()) . '</p>';
}
?>
