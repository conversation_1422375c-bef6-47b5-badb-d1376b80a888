<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get badge statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_badges,
            COUNT(DISTINCT user_id) as unique_recipients,
            COUNT(CASE WHEN DATE(earned_at) = CURDATE() THEN 1 END) as earned_today,
            COUNT(CASE WHEN DATE(earned_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as earned_this_week,
            COUNT(CASE WHEN DATE(earned_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as earned_this_month
        FROM user_badges
    ");
    $stats = $stmt->fetch();
    
    // Get category distribution
    $stmt = $pdo->query("
        SELECT 
            cat.name as category_name,
            cat.icon as category_icon,
            COUNT(ub.id) as badge_count,
            COUNT(DISTINCT ub.user_id) as unique_users
        FROM categories cat
        LEFT JOIN user_badges ub ON cat.id = ub.category_id
        GROUP BY cat.id, cat.name, cat.icon
        ORDER BY badge_count DESC
    ");
    $category_distribution = $stmt->fetchAll();
    
    // Get top badge earners
    $stmt = $pdo->query("
        SELECT 
            u.username,
            u.email,
            COUNT(ub.id) as total_badges,
            COUNT(DISTINCT ub.category_id) as categories_completed,
            MAX(ub.earned_at) as last_badge_earned
        FROM users u
        JOIN user_badges ub ON u.id = ub.user_id
        GROUP BY u.id, u.username, u.email
        ORDER BY total_badges DESC, last_badge_earned DESC
        LIMIT 10
    ");
    $top_earners = $stmt->fetchAll();
    
    // Get recent badges
    $stmt = $pdo->query("
        SELECT 
            ub.earned_at,
            u.username,
            cat.name as category_name
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        JOIN categories cat ON ub.category_id = cat.id
        ORDER BY ub.earned_at DESC
        LIMIT 15
    ");
    $recent_badges = $stmt->fetchAll();
    
    // Start output buffering
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Badge Management Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: #333;
                margin: 15px;
            }
            .header {
                text-align: center;
                margin-bottom: 25px;
                border-bottom: 2px solid #2563eb;
                padding-bottom: 15px;
            }
            .header h1 {
                color: #2563eb;
                margin: 0;
                font-size: 22px;
            }
            .header p {
                margin: 3px 0;
                color: #666;
                font-size: 10px;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 10px;
                margin-bottom: 25px;
            }
            .stat-card {
                text-align: center;
                padding: 12px 8px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
            .stat-value {
                font-size: 18px;
                font-weight: bold;
                color: #2563eb;
                display: block;
            }
            .stat-label {
                font-size: 9px;
                color: #666;
                margin-top: 3px;
            }
            .section {
                margin-bottom: 25px;
            }
            .section h2 {
                color: #2563eb;
                font-size: 14px;
                margin-bottom: 10px;
                border-bottom: 1px solid #e5e7eb;
                padding-bottom: 3px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 8px;
                font-size: 9px;
            }
            th {
                background: #2563eb;
                color: white;
                padding: 6px 4px;
                text-align: left;
                font-weight: bold;
                font-size: 9px;
            }
            td {
                padding: 4px;
                border-bottom: 1px solid #e5e7eb;
                vertical-align: top;
            }
            tr:nth-child(even) {
                background: #f9fafb;
            }
            .category-stats {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                margin-top: 10px;
            }
            .category-item {
                text-align: center;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
            }
            .category-count {
                font-size: 16px;
                font-weight: bold;
                color: #2563eb;
            }
            .category-name {
                font-size: 9px;
                color: #666;
                margin-top: 2px;
            }
            .two-column {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            .footer {
                margin-top: 25px;
                text-align: center;
                font-size: 8px;
                color: #666;
                border-top: 1px solid #e5e7eb;
                padding-top: 10px;
            }
            @media print {
                body { margin: 0; }
                @page { 
                    margin: 1cm; 
                    size: A4;
                }
                .stats-grid, .category-stats, .two-column {
                    display: block !important;
                }
                .stat-card, .category-item {
                    display: inline-block;
                    width: 18%;
                    margin: 1%;
                }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Badge Management Report</h1>
            <p>TryMeOut Vulnerability Platform</p>
            <p>Generated on: <?= date('F j, Y \a\t g:i A') ?></p>
            <p>Report by: <?= htmlspecialchars($_SESSION['username'] ?? 'Admin') ?></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['total_badges']) ?></span>
                <div class="stat-label">Total Badges</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['unique_recipients']) ?></span>
                <div class="stat-label">Badge Recipients</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['earned_today']) ?></span>
                <div class="stat-label">Earned Today</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['earned_this_week']) ?></span>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['earned_this_month']) ?></span>
                <div class="stat-label">This Month</div>
            </div>
        </div>

        <div class="section">
            <h2>Category Badge Distribution</h2>
            <div class="category-stats">
                <?php foreach ($category_distribution as $category): ?>
                    <div class="category-item">
                        <div class="category-count"><?= $category['badge_count'] ?></div>
                        <div class="category-name"><?= htmlspecialchars($category['category_name']) ?></div>
                        <div style="font-size: 8px; color: #999; margin-top: 2px;">
                            <?= $category['unique_users'] ?> users
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="two-column">
            <div class="section">
                <h2>Top Badge Earners</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Username</th>
                            <th>Total Badges</th>
                            <th>Categories</th>
                            <th>Last Earned</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_earners as $index => $earner): ?>
                            <tr>
                                <td style="text-align: center; font-weight: bold;">
                                    <?= $index + 1 ?>
                                </td>
                                <td><?= htmlspecialchars($earner['username']) ?></td>
                                <td style="text-align: center; font-weight: bold;">
                                    <?= $earner['total_badges'] ?>
                                </td>
                                <td style="text-align: center;">
                                    <?= $earner['categories_completed'] ?>
                                </td>
                                <td style="font-size: 8px;">
                                    <?= date('M j, Y', strtotime($earner['last_badge_earned'])) ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>Recent Badge Awards</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Category</th>
                            <th>Earned Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_badges as $badge): ?>
                            <tr>
                                <td><?= htmlspecialchars($badge['username']) ?></td>
                                <td><?= htmlspecialchars($badge['category_name']) ?></td>
                                <td style="font-size: 8px;">
                                    <?= date('M j, Y g:i A', strtotime($badge['earned_at'])) ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p>This report contains information about badge achievements and user progress.</p>
            <p>TryMeOut Vulnerability Platform - Badge Management System</p>
        </div>
    </body>
    </html>
    <?php
    
    $html = ob_get_clean();
    
    // Check if we want to download directly or show in browser
    $download = isset($_GET['download']) && $_GET['download'] === 'true';
    
    if ($download) {
        // Set headers for HTML display that will be printed to PDF
        header('Content-Type: text/html; charset=utf-8');
        
        // Add print functionality
        echo '<script>
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                    setTimeout(function() {
                        window.close();
                    }, 2000);
                }, 1000);
            };
        </script>';
        
        echo $html;
        
        // Add print button
        echo '<div style="position: fixed; top: 20px; right: 20px; z-index: 1000; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
                    Print Report
                </button>
                <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    Close
                </button>
              </div>';
    } else {
        // Just display the HTML for preview
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo '<h1>Report Error</h1><p>Failed to generate report: ' . htmlspecialchars($e->getMessage()) . '</p>';
}
?>
