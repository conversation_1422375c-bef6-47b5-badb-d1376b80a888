<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'Challenge Management';
$page_subtitle = 'Manage platform challenges and content';

// Handle challenge actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_challenge':
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO challenges (level, title, description, hint, solution, difficulty, category_id) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $_POST['level'],
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['hint'],
                        $_POST['solution'],
                        $_POST['difficulty'],
                        $_POST['category_id']
                    ]);
                    $success_message = "Challenge added successfully!";
                } catch (Exception $e) {
                    $error_message = "Error adding challenge: " . $e->getMessage();
                }
                break;
                
            case 'update_challenge':
                try {
                    $stmt = $pdo->prepare("
                        UPDATE challenges 
                        SET title = ?, description = ?, hint = ?, solution = ?, difficulty = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['hint'],
                        $_POST['solution'],
                        $_POST['difficulty'],
                        $_POST['challenge_id']
                    ]);
                    $success_message = "Challenge updated successfully!";
                } catch (Exception $e) {
                    $error_message = "Error updating challenge: " . $e->getMessage();
                }
                break;
                
            case 'delete_challenge':
                try {
                    $pdo->beginTransaction();
                    
                    // Delete related progress first
                    $stmt = $pdo->prepare("DELETE FROM user_progress WHERE challenge_id = ?");
                    $stmt->execute([$_POST['challenge_id']]);
                    
                    // Delete the challenge
                    $stmt = $pdo->prepare("DELETE FROM challenges WHERE id = ?");
                    $stmt->execute([$_POST['challenge_id']]);
                    
                    $pdo->commit();
                    $success_message = "Challenge deleted successfully!";
                } catch (Exception $e) {
                    $pdo->rollBack();
                    $error_message = "Error deleting challenge: " . $e->getMessage();
                }
                break;
        }
    }
}

// Get categories for dropdown
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();

// Pagination and filtering
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$difficulty_filter = isset($_GET['difficulty']) ? $_GET['difficulty'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build WHERE conditions
$where_conditions = [];
$params = [];

if ($category_filter > 0) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category_filter;
}

if ($difficulty_filter) {
    $where_conditions[] = "c.difficulty = ?";
    $params[] = $difficulty_filter;
}

if ($search) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get total challenges count
$count_sql = "SELECT COUNT(*) FROM challenges c $where_clause";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total_challenges = $stmt->fetchColumn();
$total_pages = ceil($total_challenges / $per_page);

// Get challenges with category info and completion stats
$sql = "
    SELECT 
        c.*,
        cat.name as category_name,
        cat.icon as category_icon,
        COUNT(DISTINCT up.user_id) as completion_count,
        COUNT(DISTINCT CASE WHEN up.status = 'completed' THEN up.user_id END) as completed_users
    FROM challenges c
    JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN user_progress up ON c.id = up.challenge_id
    $where_clause
    GROUP BY c.id, c.level, c.title, c.description, c.hint, c.solution, c.difficulty, c.category_id, cat.name, cat.icon
    ORDER BY c.category_id, c.level
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$challenges = $stmt->fetchAll();

include 'includes/admin_header.php';
?>

<style>
    .challenge-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        height: 100%;
    }

    .challenge-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .challenge-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .challenge-category {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background: var(--gray-100);
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        color: var(--gray-600);
    }

    .challenge-level {
        background: var(--primary-color);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
    }

    .challenge-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--gray-900);
        margin: 12px 0 8px;
        line-height: 1.4;
    }

    .challenge-description {
        color: var(--gray-600);
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .challenge-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid var(--gray-100);
    }

    .challenge-difficulty {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .difficulty-easy {
        background: rgba(5, 150, 105, 0.1);
        color: var(--success-color);
    }

    .difficulty-medium {
        background: rgba(217, 119, 6, 0.1);
        color: var(--warning-color);
    }

    .difficulty-hard {
        background: rgba(220, 38, 38, 0.1);
        color: var(--danger-color);
    }

    .challenge-actions {
        display: flex;
        gap: 8px;
    }

    .filters-container {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;
    }

    .filter-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .filter-label {
        font-size: 12px;
        font-weight: 500;
        color: var(--gray-600);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-item {
        background: white;
        padding: 20px;
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 12px;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
</style>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= $success_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= $error_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Statistics Overview -->
<div class="stats-overview">
    <div class="stat-item">
        <div class="stat-value"><?= number_format($total_challenges) ?></div>
        <div class="stat-label">Total Challenges</div>
    </div>
    <div class="stat-item">
        <div class="stat-value"><?= count($categories) ?></div>
        <div class="stat-label">Categories</div>
    </div>
    <div class="stat-item">
        <div class="stat-value">
            <?php
            $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM user_progress WHERE status = 'completed'");
            echo number_format($stmt->fetchColumn());
            ?>
        </div>
        <div class="stat-label">Active Users</div>
    </div>
    <div class="stat-item">
        <div class="stat-value">
            <?php
            $stmt = $pdo->query("SELECT COUNT(*) FROM user_progress WHERE status = 'completed'");
            echo number_format($stmt->fetchColumn());
            ?>
        </div>
        <div class="stat-label">Total Completions</div>
    </div>
</div>

<!-- Filters -->
<div class="filters-container">
    <form method="GET" class="filter-group">
        <div class="filter-item">
            <label class="filter-label">Search</label>
            <input type="text" name="search" class="form-control" placeholder="Search challenges..." 
                   value="<?= htmlspecialchars($search) ?>" style="min-width: 200px;">
        </div>
        
        <div class="filter-item">
            <label class="filter-label">Category</label>
            <select name="category" class="form-select">
                <option value="">All Categories</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category['name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="filter-item">
            <label class="filter-label">Difficulty</label>
            <select name="difficulty" class="form-select">
                <option value="">All Difficulties</option>
                <option value="Easy" <?= $difficulty_filter === 'Easy' ? 'selected' : '' ?>>Easy</option>
                <option value="Medium" <?= $difficulty_filter === 'Medium' ? 'selected' : '' ?>>Medium</option>
                <option value="Hard" <?= $difficulty_filter === 'Hard' ? 'selected' : '' ?>>Hard</option>
            </select>
        </div>
        
        <div class="filter-item" style="align-self: end;">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Filter
            </button>
            <a href="challenges.php" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times me-2"></i>Clear
            </a>
        </div>
        
    </form>
</div>

<!-- Challenges Grid -->
<div class="row g-4">
    <?php if (empty($challenges)): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No challenges found</h4>
                <p class="text-muted">Try adjusting your filters or add a new challenge.</p>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($challenges as $challenge): ?>
            <div class="col-lg-4 col-md-6">
                <div class="challenge-card">
                    <div class="challenge-header">
                        <div class="challenge-category">
                            <i class="<?= htmlspecialchars($challenge['category_icon']) ?>"></i>
                            <?= htmlspecialchars($challenge['category_name']) ?>
                        </div>
                        <div class="challenge-level">Level <?= $challenge['level'] ?></div>
                    </div>
                    
                    <h3 class="challenge-title"><?= htmlspecialchars($challenge['title']) ?></h3>
                    <p class="challenge-description"><?= htmlspecialchars($challenge['description']) ?></p>
                    
                    <div class="challenge-stats">
                        <div class="challenge-difficulty difficulty-<?= strtolower($challenge['difficulty']) ?>">
                            <?= $challenge['difficulty'] ?>
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-users me-1"></i>
                            <?= $challenge['completed_users'] ?> completed
                        </div>
                    </div>
                    
                    <div class="challenge-actions">
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="deleteChallenge(<?= $challenge['id'] ?>)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
    <nav aria-label="Challenges pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                    <a class="page-link" href="?page=<?= $i ?><?= $category_filter ? '&category=' . $category_filter : '' ?><?= $difficulty_filter ? '&difficulty=' . urlencode($difficulty_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                        <?= $i ?>
                    </a>
                </li>
            <?php endfor; ?>
        </ul>
    </nav>
<?php endif; ?>

<script>
function editChallenge(id) {
    showNotification('Edit challenge feature coming soon!', 'info');
}

function viewChallenge(id) {
    showNotification('View challenge feature coming soon!', 'info');
}

function deleteChallenge(id) {
    if (confirm('Are you sure you want to delete this challenge? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_challenge">
            <input type="hidden" name="challenge_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/admin_footer.php'; ?>
