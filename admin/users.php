<?php
session_start();
require '../config/db_connect.php';
require '../config/upload_config.php';

// Set page variables
$page_title = 'User Management';
$page_subtitle = 'Manage platform users and their progress';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'remove_profile_picture':
                $user_id = (int)$_POST['user_id'];
                try {
                    // Get user info for logging
                    $stmt = $pdo->prepare("SELECT username, profile_picture FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user_info = $stmt->fetch();

                    if ($user_info) {
                        // Remove physical file if it's not the default
                        if ($user_info['profile_picture'] && $user_info['profile_picture'] !== DEFAULT_PROFILE_PICTURE) {
                            $file_path = '../' . $user_info['profile_picture'];
                            if (file_exists($file_path) && is_file($file_path)) {
                                unlink($file_path);
                            }
                        }

                        // Update database to default picture
                        $stmt = $pdo->prepare("UPDATE users SET profile_picture = ? WHERE id = ?");
                        $stmt->execute([DEFAULT_PROFILE_PICTURE, $user_id]);

                        // Log admin action
                        $stmt = $pdo->prepare("INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $_SESSION['user_id'],
                            'remove_profile_picture',
                            'user',
                            $user_id,
                            "Removed profile picture for user: " . $user_info['username'],
                            $_SERVER['REMOTE_ADDR']
                        ]);

                        $_SESSION['success_message'] = "Profile picture removed successfully for user: " . htmlspecialchars($user_info['username']);
                    } else {
                        $_SESSION['error_message'] = "User not found.";
                    }
                } catch (Exception $e) {
                    $_SESSION['error_message'] = "Failed to remove profile picture: " . $e->getMessage();
                }
                header("Location: users.php");
                exit;

            case 'reset_progress':
                $user_id = (int)$_POST['user_id'];
                try {
                    $pdo->beginTransaction();

                    // Delete user progress
                    $stmt = $pdo->prepare("DELETE FROM user_progress WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    // Delete user badges
                    $stmt = $pdo->prepare("DELETE FROM user_badges WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    // Delete user certificates
                    $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    $pdo->commit();
                    $success_message = "User progress reset successfully!";
                } catch (Exception $e) {
                    $pdo->rollBack();
                    $error_message = "Error resetting user progress: " . $e->getMessage();
                }
                break;

            case 'add_user':
                try {
                    $username = trim($_POST['username']);
                    $email = trim($_POST['email']);
                    $password = $_POST['password'];
                    $role = $_POST['role'];

                    // Validate input
                    if (empty($username) || empty($email) || empty($password)) {
                        throw new Exception("All fields are required");
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception("Invalid email format");
                    }

                    // Check if username or email already exists
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
                    $stmt->execute([$username, $email]);
                    if ($stmt->fetchColumn() > 0) {
                        throw new Exception("Username or email already exists");
                    }

                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                    // Insert new user
                    $stmt = $pdo->prepare("
                        INSERT INTO users (username, email, password_hash, role, is_verified, created_at)
                        VALUES (?, ?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute([$username, $email, $hashed_password, $role]);

                    $success_message = "User added successfully!";
                } catch (Exception $e) {
                    $error_message = "Error adding user: " . $e->getMessage();
                }
                break;

            case 'delete_user':
                $user_id = (int)$_POST['user_id'];
                try {
                    $pdo->beginTransaction();

                    // Delete user progress
                    $stmt = $pdo->prepare("DELETE FROM user_progress WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    // Delete user badges
                    $stmt = $pdo->prepare("DELETE FROM user_badges WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    // Delete user certificates
                    $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE user_id = ?");
                    $stmt->execute([$user_id]);

                    // Delete the user
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ? AND role != 'admin'");
                    $result = $stmt->execute([$user_id]);

                    if ($stmt->rowCount() === 0) {
                        throw new Exception("Cannot delete admin users or user not found");
                    }

                    $pdo->commit();
                    $success_message = "User deleted successfully!";
                } catch (Exception $e) {
                    $pdo->rollBack();
                    $error_message = "Error deleting user: " . $e->getMessage();
                }
                break;

            case 'update_role':
                $user_id = (int)$_POST['user_id'];
                $new_role = $_POST['new_role'];

                try {
                    if (!in_array($new_role, ['admin', 'student'])) {
                        throw new Exception("Invalid role");
                    }

                    $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
                    $stmt->execute([$new_role, $user_id]);

                    $success_message = "User role updated successfully!";
                } catch (Exception $e) {
                    $error_message = "Error updating user role: " . $e->getMessage();
                }
                break;

            case 'update_user':
                $user_id = (int)$_POST['user_id'];
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $role = $_POST['role'];

                try {
                    // Validate input
                    if (empty($username) || empty($email)) {
                        throw new Exception("Username and email are required");
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception("Invalid email format");
                    }

                    // Check if username or email already exists for other users
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE (username = ? OR email = ?) AND id != ?");
                    $stmt->execute([$username, $email, $user_id]);
                    if ($stmt->fetchColumn() > 0) {
                        throw new Exception("Username or email already exists");
                    }

                    // Update user
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?");
                    $stmt->execute([$username, $email, $role, $user_id]);

                    $success_message = "User updated successfully!";
                } catch (Exception $e) {
                    $error_message = "Error updating user: " . $e->getMessage();
                }
                break;
        }
    }
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$search_condition = '';
$search_params = [];

if ($search) {
    $search_condition = "WHERE u.username LIKE ? OR u.email LIKE ?";
    $search_params = ["%$search%", "%$search%"];
}

// Get total users count
$count_sql = "SELECT COUNT(*) FROM users u $search_condition";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($search_params);
$total_users = $stmt->fetchColumn();
$total_pages = ceil($total_users / $per_page);

// Get users with their progress
$sql = "
    SELECT
        u.id,
        u.username,
        u.email,
        u.role,
        u.created_at,
        u.is_verified,
        u.profile_picture,
        COUNT(DISTINCT up.id) as total_completions,
        COUNT(DISTINCT ub.id) as total_badges,
        COUNT(DISTINCT uc.id) as total_certificates,
        MAX(up.completed_at) as last_activity
    FROM users u
    LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
    LEFT JOIN user_badges ub ON u.id = ub.user_id
    LEFT JOIN user_certificates uc ON u.id = uc.user_id
    $search_condition
    GROUP BY u.id, u.username, u.email, u.role, u.created_at, u.is_verified, u.profile_picture
    ORDER BY u.created_at DESC
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($search_params);
$users = $stmt->fetchAll();

include 'includes/admin_header.php';
?>

<style>
    .table-container {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
    }

    .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--gray-200);
    }

    .table-title {
        font-size: 18px;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
    }

    .table-actions {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .btn-professional {
        padding: 8px 16px;
        border-radius: var(--border-radius);
        font-size: 14px;
        font-weight: 500;
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700);
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 6px;
        text-decoration: none;
    }

    .btn-professional:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-900);
    }

    .btn-professional.btn-primary {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-professional.btn-primary:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .table-modern {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .table-modern th {
        background: var(--gray-50);
        color: var(--gray-700);
        font-weight: 600;
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--gray-200);
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table-modern td {
        padding: 16px;
        border-bottom: 1px solid var(--gray-100);
        color: var(--gray-700);
        vertical-align: middle;
    }

    .table-modern tbody tr {
        transition: var(--transition);
    }

    .table-modern tbody tr:hover {
        background: var(--gray-50);
    }

    .table-modern tbody tr:last-child td {
        border-bottom: none;
    }

    .badge-professional {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge-success {
        background: rgba(5, 150, 105, 0.1);
        color: var(--success-color);
    }

    .badge-warning {
        background: rgba(217, 119, 6, 0.1);
        color: var(--warning-color);
    }

    .badge-danger {
        background: rgba(220, 38, 38, 0.1);
        color: var(--danger-color);
    }

    .badge-info {
        background: rgba(8, 145, 178, 0.1);
        color: var(--info-color);
    }

    .badge-secondary {
        background: var(--gray-100);
        color: var(--gray-600);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        border: none;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
    }

    .btn-action.btn-sm {
        padding: 4px 8px;
        font-size: 11px;
    }

    .search-container {
        position: relative;
        max-width: 400px;
    }

    .search-input {
        padding-left: 40px;
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        font-size: 14px;
    }

    .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
    }

    /* User Details Modal Styling */
    .user-avatar-large {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .user-avatar-large:hover {
        transform: scale(1.05);
    }

    #userDetailsModal .modal-content {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    #userDetailsModal .table td {
        padding: 8px 12px;
        border: none;
        vertical-align: middle;
    }

    #userDetailsModal .table tr:nth-child(even) {
        background-color: rgba(0,0,0,0.02);
    }

    #userDetailsModal .card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    #userDetailsModal .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .profile-picture-status {
        font-size: 0.9em;
        padding: 4px 8px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }
</style>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($_SESSION['success_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($_SESSION['error_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Users Table -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">Platform Users</h2>
        <div class="table-actions">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="form-control search-input" placeholder="Search users..."
                       value="<?= htmlspecialchars($search) ?>" id="userSearch">
            </div>
            <button class="btn-professional btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i>
                Add User
            </button>
            <div class="dropdown d-inline">
                <button class="btn-professional dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><h6 class="dropdown-header">Export Format</h6></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="exportUsers('csv')">
                            <i class="fas fa-file-csv me-2 text-success"></i>
                            Export as CSV
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="exportUsers('pdf')">
                            <i class="fas fa-file-pdf me-2 text-danger"></i>
                            Export as PDF
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <span class="dropdown-item text-muted small">
                            <i class="fas fa-info-circle me-2"></i>
                            Choose your preferred format
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table-modern" id="usersTable">
            <thead>
                <tr>
                    <th data-sortable>User</th>
                    <th data-sortable>Email</th>
                    <th data-sortable>Role</th>
                    <th data-sortable>Progress</th>
                    <th data-sortable>Joined</th>
                    <th data-sortable>Last Activity</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <?php
                                // Handle profile picture display
                                $profile_pic_path = '../assets/uploads/default.jpg';
                                if (!empty($user['profile_picture'])) {
                                    if (strpos($user['profile_picture'], 'assets/') === 0) {
                                        $profile_pic_path = '../' . $user['profile_picture'];
                                    } else {
                                        $profile_pic_path = $user['profile_picture'];
                                    }
                                }
                                $is_default_pic = ($user['profile_picture'] === DEFAULT_PROFILE_PICTURE || empty($user['profile_picture']));
                                ?>
                                <div class="user-avatar-container me-3 position-relative">
                                    <img src="<?= htmlspecialchars($profile_pic_path) ?>"
                                         alt="<?= htmlspecialchars($user['username']) ?>"
                                         class="user-avatar-img rounded-circle"
                                         style="width: 40px; height: 40px; object-fit: cover; border: 2px solid #e9ecef;">
                                    <?php if (!$is_default_pic): ?>
                                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning"
                                              title="Custom profile picture">
                                            <i class="fas fa-image" style="font-size: 8px;"></i>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <div class="fw-semibold"><?= htmlspecialchars($user['username']) ?></div>
                                    <?php if ($user['is_verified']): ?>
                                        <small class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>Verified
                                        </small>
                                    <?php else: ?>
                                        <small class="text-warning">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td><?= htmlspecialchars($user['email']) ?></td>
                        <td>
                            <span class="badge-professional <?= $user['role'] === 'admin' ? 'badge-danger' : 'badge-info' ?>">
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-primary"><?= $user['total_completions'] ?></span>
                                <span class="badge bg-warning"><?= $user['total_badges'] ?></span>
                                <span class="badge bg-success"><?= $user['total_certificates'] ?></span>
                            </div>
                            <small class="text-muted">Challenges • Badges • Certificates</small>
                        </td>
                        <td>
                            <div><?= date('M j, Y', strtotime($user['created_at'])) ?></div>
                            <small class="text-muted"><?= date('g:i A', strtotime($user['created_at'])) ?></small>
                        </td>
                        <td>
                            <?php if ($user['last_activity']): ?>
                                <div><?= date('M j, Y', strtotime($user['last_activity'])) ?></div>
                                <small class="text-muted"><?= date('g:i A', strtotime($user['last_activity'])) ?></small>
                            <?php else: ?>
                                <span class="text-muted">No activity</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="viewUserDetails(<?= $user['id'] ?>)"
                                        data-bs-toggle="tooltip" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>

                                <div class="dropdown d-inline">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown"
                                            data-bs-toggle="tooltip" title="More Actions">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><h6 class="dropdown-header">User Actions</h6></li>

                                        <?php if ($user['role'] !== 'admin'): ?>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="editUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>', '<?= htmlspecialchars($user['email']) ?>', '<?= $user['role'] ?>')">
                                                    <i class="fas fa-edit me-2"></i>Edit User
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="changeRole(<?= $user['id'] ?>, '<?= $user['role'] ?>')">
                                                    <i class="fas fa-user-tag me-2"></i>Change Role
                                                </a>
                                            </li>
                                            <?php if (!$is_default_pic): ?>
                                            <li>
                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to remove this user\'s profile picture? It will be replaced with the default image.')">
                                                    <input type="hidden" name="action" value="remove_profile_picture">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <button type="submit" class="dropdown-item text-warning">
                                                        <i class="fas fa-image me-2"></i>Remove Profile Picture
                                                    </button>
                                                </form>
                                            </li>
                                            <?php endif; ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to reset this user\'s progress? This will delete all their completions, badges, and certificates.')">
                                                    <input type="hidden" name="action" value="reset_progress">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <button type="submit" class="dropdown-item text-warning">
                                                        <i class="fas fa-redo me-2"></i>Reset Progress
                                                    </button>
                                                </form>
                                            </li>
                                            <li>
                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                    <input type="hidden" name="action" value="delete_user">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <button type="submit" class="dropdown-item text-danger">
                                                        <i class="fas fa-trash me-2"></i>Delete User
                                                    </button>
                                                </form>
                                            </li>
                                        <?php else: ?>
                                            <li>
                                                <span class="dropdown-item text-muted">
                                                    <i class="fas fa-shield-alt me-2"></i>Admin Protected
                                                </span>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Users pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                            <?= $i ?>
                        </a>
                    </li>
                <?php endfor; ?>
            </ul>
        </nav>
    <?php endif; ?>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Add New User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="addUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_user">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="8">
                        <div class="form-text">Password must be at least 8 characters long</div>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="student">Student</option>
                            <option value="admin">Admin</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Add User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit me-2"></i>Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" id="edit_user_id">

                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="student">Student</option>
                            <option value="admin">Admin</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="userDetailsModalLabel">
                    <i class="fas fa-user-circle me-2 text-primary"></i>User Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4" id="userDetailsContent">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading user details...</p>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-primary" onclick="printUserDetails()">
                    <i class="fas fa-print me-2"></i>Print Details
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Change Role Modal -->
<div class="modal fade" id="changeRoleModal" tabindex="-1" aria-labelledby="changeRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeRoleModalLabel">
                    <i class="fas fa-user-tag me-2"></i>Change User Role
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="changeRoleForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_role">
                    <input type="hidden" name="user_id" id="role_user_id">

                    <div class="mb-3">
                        <label for="new_role" class="form-label">New Role</label>
                        <select class="form-select" id="new_role" name="new_role" required>
                            <option value="student">Student</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Changing user role will affect their access permissions.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Change Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include external JavaScript file -->
<script src="js/users.js"></script>

<script>
// Page-specific data and functions that need PHP variables
document.addEventListener('DOMContentLoaded', function() {
    // Initialize data table functionality
    if (typeof initializeDataTable === 'function') {
        initializeDataTable('usersTable');
    }

    // Search functionality
    const searchInput = document.getElementById('userSearch');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchValue = this.value.trim();
                if (searchValue !== '<?= $search ?>') {
                    window.location.href = '?search=' + encodeURIComponent(searchValue);
                }
            }, 500);
        });
    }
});

function viewUserDetails(userId) {
    // Find user data from the current page data
    const users = <?= json_encode($users) ?>;
    const user = users.find(u => u.id == userId);

    if (user) {
        showUserDetailsModal(user);
    } else {
        if (typeof showNotification === 'function') {
            showNotification('User not found', 'danger');
        }
    }
}

function showUserDetailsModal(user) {
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    const content = document.getElementById('userDetailsContent');

    // Determine profile picture path
    let profilePicPath;
    if (user.profile_picture && user.profile_picture !== '<?= DEFAULT_PROFILE_PICTURE ?>') {
        // User has custom profile picture
        if (user.profile_picture.startsWith('http')) {
            profilePicPath = user.profile_picture;
        } else {
            profilePicPath = '../' + user.profile_picture;
        }
    } else {
        // Use default profile picture
        profilePicPath = '../<?= DEFAULT_PROFILE_PICTURE ?>';
    }

    // Check if user has custom profile picture for badge display
    const hasCustomPicture = user.profile_picture && user.profile_picture !== '<?= DEFAULT_PROFILE_PICTURE ?>';

    // Format last activity
    const lastActivity = user.last_activity ?
        new Date(user.last_activity).toLocaleDateString() + ' at ' + new Date(user.last_activity).toLocaleTimeString() :
        'No activity recorded';

    // Call the external function to show user details
    if (typeof showUserDetails === 'function') {
        showUserDetails(
            user.id,
            user.username,
            user.email,
            user.role,
            user.created_at,
            user.is_verified,
            user.profile_picture,
            user.total_completions,
            user.total_badges,
            user.total_certificates,
            user.last_activity
        );
    } else {
        // Fallback: show basic modal
        modal.show();
    }
}
</script>

<?php
$additional_scripts = '
<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll(\'[data-bs-toggle="tooltip"]\'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});
</script>
';
include 'includes/admin_footer.php';
?>
