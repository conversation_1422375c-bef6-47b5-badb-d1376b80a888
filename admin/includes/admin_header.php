<?php
// Admin authentication check
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

// Get basic stats for navigation badges
$stmt = $pdo->query("SELECT COUNT(*) FROM users");
$total_users = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM user_certificates");
$total_certificates = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM challenges");
$total_challenges = $stmt->fetchColumn();

// Get security stats for navigation
try {
    require_once '../config/brute_force_protection.php';
    $bruteForceProtection = new BruteForceProtection($pdo);
    $security_stats = $bruteForceProtection->getLockoutStats();
    $nav_locked_accounts = $security_stats['current_locked'] ?? 0;
} catch (Exception $e) {
    $nav_locked_accounts = 0;
}

// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?? 'Admin Dashboard' ?> | TryMeOut</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            /* Professional Color Palette */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --accent-color: #0ea5e9;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            
            /* Neutral Colors */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* Layout */
            --sidebar-width: 280px;
            --header-height: 80px;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            
            /* Transitions */
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            background: var(--gray-50);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--gray-700);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* PROFESSIONAL SIDEBAR DESIGN */
        .sidebar {
            background: white;
            min-height: 100vh;
            width: var(--sidebar-width);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            border-right: 1px solid var(--gray-200);
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            object-fit: contain;
        }

        .sidebar-title {
            color: var(--gray-900);
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }

        .sidebar-subtitle {
            color: var(--gray-500);
            font-size: 12px;
            margin: 0;
            font-weight: 500;
        }

        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--gray-400);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 20px 8px;
            margin: 0;
        }

        .sidebar .nav-link {
            color: var(--gray-600);
            padding: 12px 20px;
            margin: 2px 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            position: relative;
        }

        .sidebar .nav-link:hover {
            background: var(--gray-100);
            color: var(--gray-900);
            transform: translateX(2px);
        }

        .sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            width: 18px;
            text-align: center;
            font-size: 16px;
        }

        .nav-badge {
            margin-left: auto;
            background: var(--gray-200);
            color: var(--gray-600);
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .sidebar .nav-link.active .nav-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .nav-badge.live {
            background: #10b981 !important;
            color: white !important;
            animation: pulse-green 2s infinite;
        }

        /* Pulse animation for live badge */
        @keyframes pulse-green {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                opacity: 1;
            }
        }

        /* Sidebar Footer / Copyright */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 5px;
        }

        .copyright-info {
            text-align: center;
            font-size: 10px;
            color: var(--gray-500);
        }

        .copyright-info p {
            margin: 0;
        }

        /* PROFESSIONAL MAIN CONTENT */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            background: var(--gray-50);
        }

        .top-navbar {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 0 32px;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-subtitle {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            color: var(--gray-600);
            padding: 8px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .mobile-toggle:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }

        /* RESPONSIVE DESIGN */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
            }
            
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .mobile-toggle {
                display: block !important;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <div class="sidebar-logo">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut Logo">
                </div>
                <div>
                    <div class="sidebar-title">TryMeOut</div>
                    <div class="sidebar-subtitle">Admin Portal</div>
                </div>
            </div>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <h6 class="nav-section-title">Overview</h6>
                <a class="nav-link <?= $current_page === 'dashboard' ? 'active' : '' ?>" href="dashboard.php">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <a class="nav-link <?= $current_page === 'analytics' ? 'active' : '' ?>" href="analytics.php">
                    <i class="fas fa-chart-line"></i>
                    Analytics
                    <span class="nav-badge live">Live</span>
                </a>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-section-title">Management</h6>
                <a class="nav-link <?= $current_page === 'users' ? 'active' : '' ?>" href="users.php">
                    <i class="fas fa-users"></i>
                    Users
                    <span class="nav-badge"><?= number_format($total_users) ?></span>
                </a>
                <a class="nav-link <?= $current_page === 'challenges' ? 'active' : '' ?>" href="challenges.php">
                    <i class="fas fa-puzzle-piece"></i>
                    Challenges
                    <span class="nav-badge"><?= number_format($total_challenges) ?></span>
                </a>
                <a class="nav-link <?= $current_page === 'certificates' ? 'active' : '' ?>" href="certificates.php">
                    <i class="fas fa-certificate"></i>
                    Certificates
                    <span class="nav-badge"><?= number_format($total_certificates) ?></span>
                </a>
                <a class="nav-link <?= $current_page === 'badges' ? 'active' : '' ?>" href="badges.php">
                    <i class="fas fa-medal"></i>
                    Badges
                </a>
                <a class="nav-link <?= $current_page === 'profile_moderation' ? 'active' : '' ?>" href="profile_moderation.php">
                    <i class="fas fa-images"></i>
                    Profile Moderation
                </a>
            </div>
            
            <div class="nav-section">
                <h6 class="nav-section-title">System</h6>
                <a class="nav-link <?= $current_page === 'brute_force_management' ? 'active' : '' ?>" href="brute_force_management.php">
                    <i class="fas fa-shield-alt"></i>
                    Security Management
                    <?php if ($nav_locked_accounts > 0): ?>
                        <span class="nav-badge" style="background: var(--danger-color); color: white;"><?= $nav_locked_accounts ?></span>
                    <?php endif; ?>
                </a>
                <a class="nav-link <?= $current_page === 'logs' ? 'active' : '' ?>" href="logs.php">
                    <i class="fas fa-clipboard-list"></i>
                    System Logs
                </a>
                <a class="nav-link <?= $current_page === 'settings' ? 'active' : '' ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
                <a class="nav-link" href="../pages/dashboard.php">
                    <i class="fas fa-external-link-alt"></i>
                    User View
                </a>
                <a class="nav-link" href="../auth/logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>

        <!-- Copyright Section -->
        <div class="sidebar-footer">
            <div class="copyright-info">
                <p>&copy; <?= date('Y') ?> TryMeOut. All Rights Reserved.</p>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Professional Header -->
        <header class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-left">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="page-title"><?= $page_title ?? 'Admin Dashboard' ?></h1>
                        <p class="page-subtitle"><?= $page_subtitle ?? 'Manage your vulnerability platform' ?></p>
                    </div>
                </div>
                
                <div class="navbar-right">
                    <!-- User Profile -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2" style="width: 32px; height: 32px; border-radius: 50%; background: var(--primary-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="d-none d-md-inline"><?= htmlspecialchars($_SESSION['username']); ?></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Account</h6></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content Container -->
        <div class="container-fluid" style="padding: 32px; max-width: 1400px; margin: 0 auto;">

<script>
// Mobile sidebar toggle
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}
</script>
