        </div> <!-- End Page Content Container -->
    </div> <!-- End Main Content -->

    <script>
        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggle.contains(event.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });

        // Professional table enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to buttons
            const buttons = document.querySelectorAll('.btn-professional');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('no-loading')) {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                        this.disabled = true;
                        
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }, 1000);
                    }
                });
            });

            // Enhanced tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Auto-refresh indicators
            const refreshElements = document.querySelectorAll('.auto-refresh');
            refreshElements.forEach(element => {
                const interval = element.dataset.interval || 30000;
                setInterval(() => {
                    element.classList.add('refreshing');
                    setTimeout(() => {
                        element.classList.remove('refreshing');
                    }, 500);
                }, interval);
            });
        });

        // Professional notification system
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: var(--shadow-lg);
                border: none;
                border-radius: var(--border-radius);
            `;
            
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Professional data table enhancements
        function initializeDataTable(tableId, options = {}) {
            const table = document.getElementById(tableId);
            if (!table) return;

            // Add search functionality
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'form-control mb-3';
            searchInput.placeholder = 'Search...';
            searchInput.style.maxWidth = '300px';
            
            table.parentNode.insertBefore(searchInput, table);

            searchInput.addEventListener('input', function() {
                const filter = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(filter) ? '' : 'none';
                });
            });

            // Add sorting functionality
            const headers = table.querySelectorAll('th[data-sortable]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.innerHTML += ' <i class="fas fa-sort ms-1"></i>';
                
                header.addEventListener('click', function() {
                    const column = this.cellIndex;
                    const rows = Array.from(table.querySelectorAll('tbody tr'));
                    const isAscending = this.classList.contains('sort-asc');
                    
                    // Reset all headers
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                        h.querySelector('i').className = 'fas fa-sort ms-1';
                    });
                    
                    // Sort rows
                    rows.sort((a, b) => {
                        const aVal = a.cells[column].textContent.trim();
                        const bVal = b.cells[column].textContent.trim();
                        
                        if (isAscending) {
                            return bVal.localeCompare(aVal);
                        } else {
                            return aVal.localeCompare(bVal);
                        }
                    });
                    
                    // Update header
                    this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
                    this.querySelector('i').className = `fas fa-sort-${isAscending ? 'down' : 'up'} ms-1`;
                    
                    // Reorder table
                    const tbody = table.querySelector('tbody');
                    rows.forEach(row => tbody.appendChild(row));
                });
            });
        }

        // Professional form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            let isValid = true;
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            
            inputs.forEach(input => {
                const errorDiv = input.parentNode.querySelector('.invalid-feedback');
                
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    if (errorDiv) {
                        errorDiv.textContent = 'This field is required';
                    }
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                }
            });

            return isValid;
        }

        // Professional AJAX helper
        function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('Request failed:', error);
                    showNotification('Request failed. Please try again.', 'danger');
                    throw error;
                });
        }

        // Professional loading overlay
        function showLoading(show = true) {
            let overlay = document.getElementById('loading-overlay');
            
            if (show && !overlay) {
                overlay = document.createElement('div');
                overlay.id = 'loading-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                `;
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-3 text-muted">Loading...</div>
                    </div>
                `;
                document.body.appendChild(overlay);
            } else if (!show && overlay) {
                overlay.remove();
            }
        }
    </script>

    <!-- Additional page-specific scripts can be added here -->
    <?php if (isset($additional_scripts)): ?>
        <?= $additional_scripts ?>
    <?php endif; ?>

</body>
</html>
