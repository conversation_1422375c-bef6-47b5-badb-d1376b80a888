<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get all certificates with user and category info
    $sql = "
        SELECT 
            uc.id,
            uc.certificate_code,
            uc.issued_at,
            u.username,
            u.email,
            cat.name as category_name,
            cat.icon as category_icon
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories cat ON uc.category_id = cat.id
        ORDER BY uc.issued_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $certificates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate statistics
    $total_certificates = count($certificates);
    $unique_recipients = count(array_unique(array_column($certificates, 'username')));
    $categories = array_count_values(array_column($certificates, 'category_name'));
    
    // Get recent statistics
    $today = date('Y-m-d');
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $month_ago = date('Y-m-d', strtotime('-30 days'));
    
    $issued_today = count(array_filter($certificates, function($cert) use ($today) {
        return date('Y-m-d', strtotime($cert['issued_at'])) === $today;
    }));
    
    $issued_this_week = count(array_filter($certificates, function($cert) use ($week_ago) {
        return date('Y-m-d', strtotime($cert['issued_at'])) >= $week_ago;
    }));
    
    $issued_this_month = count(array_filter($certificates, function($cert) use ($month_ago) {
        return date('Y-m-d', strtotime($cert['issued_at'])) >= $month_ago;
    }));
    
    // Start output buffering
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Certificates Export Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: #333;
                margin: 15px;
            }
            .header {
                text-align: center;
                margin-bottom: 25px;
                border-bottom: 2px solid #2563eb;
                padding-bottom: 15px;
            }
            .header h1 {
                color: #2563eb;
                margin: 0;
                font-size: 22px;
            }
            .header p {
                margin: 3px 0;
                color: #666;
                font-size: 10px;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 10px;
                margin-bottom: 25px;
            }
            .stat-card {
                text-align: center;
                padding: 12px 8px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
            .stat-value {
                font-size: 18px;
                font-weight: bold;
                color: #2563eb;
                display: block;
            }
            .stat-label {
                font-size: 9px;
                color: #666;
                margin-top: 3px;
            }
            .section {
                margin-bottom: 25px;
            }
            .section h2 {
                color: #2563eb;
                font-size: 14px;
                margin-bottom: 10px;
                border-bottom: 1px solid #e5e7eb;
                padding-bottom: 3px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 8px;
                font-size: 9px;
            }
            th {
                background: #2563eb;
                color: white;
                padding: 6px 4px;
                text-align: left;
                font-weight: bold;
                font-size: 9px;
            }
            td {
                padding: 4px;
                border-bottom: 1px solid #e5e7eb;
                vertical-align: top;
            }
            tr:nth-child(even) {
                background: #f9fafb;
            }
            .certificate-code {
                font-family: 'Courier New', monospace;
                font-size: 8px;
                background: #f3f4f6;
                padding: 2px 4px;
                border-radius: 3px;
            }
            .category-stats {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                margin-top: 10px;
            }
            .category-item {
                text-align: center;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
            }
            .category-count {
                font-size: 16px;
                font-weight: bold;
                color: #2563eb;
            }
            .category-name {
                font-size: 9px;
                color: #666;
                margin-top: 2px;
            }
            .footer {
                margin-top: 25px;
                text-align: center;
                font-size: 8px;
                color: #666;
                border-top: 1px solid #e5e7eb;
                padding-top: 10px;
            }
            .two-column {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            @media print {
                body { margin: 0; }
                @page { 
                    margin: 1cm; 
                    size: A4;
                }
                table { 
                    page-break-inside: auto;
                }
                tr { 
                    page-break-inside: avoid; 
                    page-break-after: auto; 
                }
                .header, .section h2 {
                    page-break-after: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>TryMeOut - Certificates Export Report</h1>
            <p>Vulnerability Web Application Platform</p>
            <p>Generated on: <?= date('F j, Y \a\t g:i A') ?></p>
            <p>Exported by: <?= htmlspecialchars($_SESSION['username'] ?? 'Admin') ?></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-value"><?= number_format($total_certificates) ?></span>
                <div class="stat-label">Total Certificates</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($unique_recipients) ?></span>
                <div class="stat-label">Unique Recipients</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($issued_today) ?></span>
                <div class="stat-label">Issued Today</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($issued_this_week) ?></span>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($issued_this_month) ?></span>
                <div class="stat-label">This Month</div>
            </div>
        </div>

        <div class="two-column">
            <div class="section">
                <h2>Category Distribution</h2>
                <div class="category-stats">
                    <?php foreach ($categories as $category => $count): ?>
                        <div class="category-item">
                            <div class="category-count"><?= $count ?></div>
                            <div class="category-name"><?= htmlspecialchars($category) ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="section">
                <h2>Export Summary</h2>
                <table style="font-size: 10px;">
                    <tr>
                        <td><strong>Total Records:</strong></td>
                        <td><?= number_format($total_certificates) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Date Range:</strong></td>
                        <td>
                            <?php if (!empty($certificates)): ?>
                                <?= date('M j, Y', strtotime(end($certificates)['issued_at'])) ?> - 
                                <?= date('M j, Y', strtotime($certificates[0]['issued_at'])) ?>
                            <?php else: ?>
                                No certificates
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Categories:</strong></td>
                        <td><?= count($categories) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Export Format:</strong></td>
                        <td>PDF Report</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section">
            <h2>Certificate Details</h2>
            <?php if (!empty($certificates)): ?>
                <table>
                    <thead>
                        <tr>
                            <th style="width: 8%;">ID</th>
                            <th style="width: 18%;">Certificate Code</th>
                            <th style="width: 15%;">Username</th>
                            <th style="width: 20%;">Email</th>
                            <th style="width: 15%;">Category</th>
                            <th style="width: 12%;">Issue Date</th>
                            <th style="width: 12%;">Issue Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($certificates as $cert): ?>
                            <tr>
                                <td><?= $cert['id'] ?></td>
                                <td>
                                    <span class="certificate-code"><?= htmlspecialchars($cert['certificate_code']) ?></span>
                                </td>
                                <td><?= htmlspecialchars($cert['username']) ?></td>
                                <td style="font-size: 8px;"><?= htmlspecialchars($cert['email']) ?></td>
                                <td><?= htmlspecialchars($cert['category_name']) ?></td>
                                <td><?= date('M j, Y', strtotime($cert['issued_at'])) ?></td>
                                <td><?= date('g:i A', strtotime($cert['issued_at'])) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666; padding: 20px;">No certificates found in the system.</p>
            <?php endif; ?>
        </div>

        <div class="footer">
            <p>This report contains confidential information about issued certificates.</p>
            <p>TryMeOut Vulnerability Platform - Certificate Management System</p>
            <p>For verification of any certificate, visit: http://<?= $_SERVER['HTTP_HOST'] ?>/vuln-platform-beta/utils/verify_certificate.php</p>
        </div>
    </body>
    </html>
    <?php
    
    $html = ob_get_clean();
    
    // Check if we want to download directly or show in browser
    $download = isset($_GET['download']) && $_GET['download'] === 'true';
    
    if ($download) {
        // Set headers for HTML display that will be printed to PDF
        header('Content-Type: text/html; charset=utf-8');
        
        // Add print functionality
        echo '<script>
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                    setTimeout(function() {
                        window.close();
                    }, 2000);
                }, 1000);
            };
            
            // Add print-specific styles
            var style = document.createElement("style");
            style.textContent = `
                @media print {
                    body { 
                        margin: 0; 
                        font-size: 10px;
                    }
                    @page { 
                        margin: 1.5cm; 
                        size: A4;
                    }
                    table { 
                        page-break-inside: auto;
                        font-size: 8px;
                    }
                    tr { 
                        page-break-inside: avoid; 
                        page-break-after: auto; 
                    }
                    .stats-grid {
                        display: block !important;
                    }
                    .stat-card {
                        display: inline-block;
                        width: 18%;
                        margin: 1%;
                    }
                    .two-column {
                        display: block !important;
                    }
                    .category-stats {
                        display: block !important;
                    }
                    .category-item {
                        display: inline-block;
                        width: 30%;
                        margin: 1%;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>';
        
        echo $html;
        
        // Add print button for manual printing
        echo '<div style="position: fixed; top: 20px; right: 20px; z-index: 1000; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
                    <i class="fas fa-print"></i> Print to PDF
                </button>
                <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    Close
                </button>
              </div>';
    } else {
        // Just display the HTML for preview
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo '<h1>Export Error</h1><p>Failed to generate PDF: ' . htmlspecialchars($e->getMessage()) . '</p>';
}
?>
