<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'Analytics';
$page_subtitle = 'Platform insights and performance metrics';

// Get time period from URL parameter
$period = isset($_GET['period']) ? $_GET['period'] : 'week';
$chart_type = isset($_GET['chart']) ? $_GET['chart'] : 'bar';

// Calculate date range based on period
switch ($period) {
    case 'day':
        $date_condition = "DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
        $date_format = "%Y-%m-%d";
        $date_label = "Daily";
        break;
    case 'week':
        $date_condition = "YEARWEEK(completed_at) >= YEARWEEK(CURDATE()) - 4";
        $date_format = "%Y-%u";
        $date_label = "Weekly";
        break;
    case 'month':
        $date_condition = "YEAR(completed_at) = YEAR(CURDATE()) AND MONTH(completed_at) >= MONTH(CURDATE()) - 6";
        $date_format = "%Y-%m";
        $date_label = "Monthly";
        break;
    case 'year':
        $date_condition = "YEAR(completed_at) >= YEAR(CURDATE()) - 2";
        $date_format = "%Y";
        $date_label = "Yearly";
        break;
    default:
        $date_condition = "YEARWEEK(completed_at) >= YEARWEEK(CURDATE()) - 4";
        $date_format = "%Y-%u";
        $date_label = "Weekly";
}

// Get completion analytics
$stmt = $pdo->query("
    SELECT
        DATE_FORMAT(completed_at, '$date_format') as period,
        COUNT(*) as completions,
        COUNT(DISTINCT user_id) as unique_users
    FROM user_progress
    WHERE status = 'completed' AND $date_condition
    GROUP BY DATE_FORMAT(completed_at, '$date_format')
    ORDER BY period ASC
");
$completion_analytics = $stmt->fetchAll();

// Get category performance
$stmt = $pdo->query("
    SELECT
        cat.name as category_name,
        COUNT(*) as total_completions,
        COUNT(DISTINCT up.user_id) as unique_users,
        AVG(CASE WHEN up.completed_at IS NOT NULL THEN 1 ELSE 0 END) * 100 as completion_rate
    FROM categories cat
    LEFT JOIN challenges c ON cat.id = c.category_id
    LEFT JOIN user_progress up ON c.id = up.challenge_id AND up.status = 'completed'
    GROUP BY cat.id, cat.name
    ORDER BY total_completions DESC
");
$category_performance = $stmt->fetchAll();

// Get user engagement metrics
$stmt = $pdo->query("
    SELECT
        COUNT(DISTINCT u.id) as total_users,
        COUNT(DISTINCT CASE WHEN up.user_id IS NOT NULL THEN u.id END) as active_users,
        COUNT(DISTINCT CASE WHEN up.completed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.id END) as weekly_active,
        COUNT(DISTINCT CASE WHEN up.completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN u.id END) as monthly_active
    FROM users u
    LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
");
$engagement_metrics = $stmt->fetch();

// Get top performers
$stmt = $pdo->query("
    SELECT
        u.username,
        COUNT(*) as total_completions,
        COUNT(DISTINCT c.category_id) as categories_completed,
        MAX(up.completed_at) as last_completion
    FROM users u
    JOIN user_progress up ON u.id = up.user_id
    JOIN challenges c ON up.challenge_id = c.id
    WHERE up.status = 'completed'
    GROUP BY u.id, u.username
    ORDER BY total_completions DESC
    LIMIT 10
");
$top_performers = $stmt->fetchAll();

// Get challenge difficulty analysis
$stmt = $pdo->query("
    SELECT
        c.difficulty,
        COUNT(*) as total_challenges,
        COUNT(DISTINCT up.user_id) as users_attempted,
        AVG(CASE WHEN up.status = 'completed' THEN 1 ELSE 0 END) * 100 as completion_rate
    FROM challenges c
    LEFT JOIN user_progress up ON c.id = up.challenge_id
    GROUP BY c.difficulty
    ORDER BY
        CASE c.difficulty
            WHEN 'Easy' THEN 1
            WHEN 'Medium' THEN 2
            WHEN 'Hard' THEN 3
        END
");
$difficulty_analysis = $stmt->fetchAll();

include 'includes/admin_header.php';
?>

<style>
    .analytics-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
        transition: var(--transition);
    }

    .analytics-card:hover {
        box-shadow: var(--shadow-md);
    }

    .analytics-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--gray-200);
    }

    .analytics-title {
        font-size: 18px;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
    }

    .chart-wrapper {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .chart-wrapper canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
    }

    .period-selector {
        display: flex;
        gap: 4px;
        background: var(--gray-100);
        padding: 4px;
        border-radius: var(--border-radius);
    }

    .period-btn {
        padding: 8px 16px;
        border: none;
        background: transparent;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: var(--gray-600);
        transition: var(--transition);
        cursor: pointer;
    }

    .period-btn.active {
        background: white;
        color: var(--primary-color);
        box-shadow: var(--shadow-sm);
    }

    .period-btn:hover:not(.active) {
        color: var(--gray-900);
    }

    .chart-controls {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .metric-item {
        text-align: center;
        padding: 16px;
        background: var(--gray-50);
        border-radius: var(--border-radius);
    }

    .metric-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 4px;
    }

    .metric-label {
        font-size: 12px;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .leaderboard-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .leaderboard-item:last-child {
        border-bottom: none;
    }

    .leaderboard-rank {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
    }

    .leaderboard-rank.gold {
        background: #ffd700;
        color: #000;
    }

    .leaderboard-rank.silver {
        background: #c0c0c0;
        color: #000;
    }

    .leaderboard-rank.bronze {
        background: #cd7f32;
        color: #fff;
    }

    .progress-ring {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(var(--primary-color) 0deg, var(--gray-200) 0deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .progress-ring::before {
        content: '';
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }

    .progress-text {
        position: relative;
        z-index: 1;
        font-weight: 600;
        font-size: 12px;
        color: var(--gray-900);
    }
</style>

<!-- Analytics Controls -->
<div class="analytics-card">
    <div class="analytics-header">
        <h2 class="analytics-title">Platform Analytics</h2>
        <div class="chart-controls">
            <div class="period-selector">
                <button class="period-btn <?= $period === 'day' ? 'active' : '' ?>"
                        onclick="changePeriod('day')">Daily</button>
                <button class="period-btn <?= $period === 'week' ? 'active' : '' ?>"
                        onclick="changePeriod('week')">Weekly</button>
                <button class="period-btn <?= $period === 'month' ? 'active' : '' ?>"
                        onclick="changePeriod('month')">Monthly</button>
                <button class="period-btn <?= $period === 'year' ? 'active' : '' ?>"
                        onclick="changePeriod('year')">Yearly</button>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary <?= $chart_type === 'bar' ? 'active' : '' ?>"
                        onclick="changeChartType('bar')">
                    <i class="fas fa-chart-bar"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary <?= $chart_type === 'line' ? 'active' : '' ?>"
                        onclick="changeChartType('line')">
                    <i class="fas fa-chart-line"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Engagement Metrics -->
    <div class="metric-grid">
        <div class="metric-item">
            <div class="metric-value"><?= number_format($engagement_metrics['total_users']) ?></div>
            <div class="metric-label">Total Users</div>
        </div>
        <div class="metric-item">
            <div class="metric-value"><?= number_format($engagement_metrics['active_users']) ?></div>
            <div class="metric-label">Active Users</div>
        </div>
        <div class="metric-item">
            <div class="metric-value"><?= number_format($engagement_metrics['weekly_active']) ?></div>
            <div class="metric-label">Weekly Active</div>
        </div>
        <div class="metric-item">
            <div class="metric-value"><?= number_format($engagement_metrics['monthly_active']) ?></div>
            <div class="metric-label">Monthly Active</div>
        </div>
        <div class="metric-item">
            <div class="metric-value">
                <?= $engagement_metrics['total_users'] > 0 ?
                    round(($engagement_metrics['active_users'] / $engagement_metrics['total_users']) * 100, 1) : 0 ?>%
            </div>
            <div class="metric-label">Engagement Rate</div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mb-4">
    <!-- Completion Trends -->
    <div class="col-lg-8">
        <div class="analytics-card">
            <div class="analytics-header">
                <h3 class="analytics-title"><?= $date_label ?> Completion Trends</h3>
            </div>
            <div class="chart-wrapper">
                <canvas id="completionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Category Performance -->
    <div class="col-lg-4">
        <div class="analytics-card">
            <div class="analytics-header">
                <h3 class="analytics-title">Category Performance</h3>
            </div>
            <div class="chart-wrapper">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Performance Analysis Row -->
<div class="row g-4 mb-4">
    <!-- Top Performers -->
    <div class="col-lg-6">
        <div class="analytics-card">
            <div class="analytics-header">
                <h3 class="analytics-title">Top Performers</h3>
            </div>
            <div class="leaderboard">
                <?php foreach ($top_performers as $index => $performer): ?>
                    <div class="leaderboard-item">
                        <div class="d-flex align-items-center gap-3">
                            <div class="leaderboard-rank <?= $index === 0 ? 'gold' : ($index === 1 ? 'silver' : ($index === 2 ? 'bronze' : '')) ?>">
                                <?= $index + 1 ?>
                            </div>
                            <div>
                                <div class="fw-semibold"><?= htmlspecialchars($performer['username']) ?></div>
                                <small class="text-muted">
                                    <?= $performer['categories_completed'] ?> categories completed
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="fw-semibold"><?= $performer['total_completions'] ?></div>
                            <small class="text-muted">completions</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Difficulty Analysis -->
    <div class="col-lg-6">
        <div class="analytics-card">
            <div class="analytics-header">
                <h3 class="analytics-title">Challenge Difficulty Analysis</h3>
            </div>
            <div class="difficulty-analysis">
                <?php foreach ($difficulty_analysis as $difficulty): ?>
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center gap-3">
                            <div class="progress-ring" style="background: conic-gradient(var(--primary-color) <?= $difficulty['completion_rate'] * 3.6 ?>deg, var(--gray-200) 0deg);">
                                <div class="progress-text"><?= round($difficulty['completion_rate']) ?>%</div>
                            </div>
                            <div>
                                <div class="fw-semibold"><?= $difficulty['difficulty'] ?></div>
                                <small class="text-muted"><?= $difficulty['total_challenges'] ?> challenges</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="fw-semibold"><?= $difficulty['users_attempted'] ?></div>
                            <small class="text-muted">users attempted</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Professional color palette
const chartColors = {
    primary: '#2563eb',
    success: '#059669',
    warning: '#d97706',
    info: '#0891b2',
    primaryLight: 'rgba(37, 99, 235, 0.1)',
    successLight: 'rgba(5, 150, 105, 0.1)'
};

// Completion trends chart
const completionData = <?= json_encode($completion_analytics) ?>;
const ctx1 = document.getElementById('completionChart').getContext('2d');
new Chart(ctx1, {
    type: '<?= $chart_type ?>',
    data: {
        labels: completionData.map(item => item.period),
        datasets: [{
            label: 'Completions',
            data: completionData.map(item => item.completions),
            backgroundColor: <?= $chart_type === 'line' ? 'chartColors.primaryLight' : 'chartColors.primary' ?>,
            borderColor: chartColors.primary,
            borderWidth: 3,
            fill: <?= $chart_type === 'line' ? 'true' : 'true' ?>,
            tension: <?= $chart_type === 'line' ? '0.4' : '0' ?>,
            pointBackgroundColor: chartColors.primary,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: <?= $chart_type === 'line' ? '5' : '0' ?>
        }, {
            label: 'Unique Users',
            data: completionData.map(item => item.unique_users),
            backgroundColor: <?= $chart_type === 'line' ? 'chartColors.successLight' : 'chartColors.success' ?>,
            borderColor: chartColors.success,
            borderWidth: 3,
            fill: <?= $chart_type === 'line' ? 'true' : 'true' ?>,
            tension: <?= $chart_type === 'line' ? '0.4' : '0' ?>,
            pointBackgroundColor: chartColors.success,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: <?= $chart_type === 'line' ? '5' : '0' ?>
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    usePointStyle: true,
                    pointStyle: 'circle',
                    font: {
                        size: 13,
                        family: 'Inter, sans-serif'
                    },
                    color: '#64748b',
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: '#1e293b',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#e2e8f0',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true
            }
        },
        scales: {
            x: {
                grid: {
                    color: '#f1f5f9',
                    borderColor: '#e2e8f0'
                },
                ticks: {
                    color: '#64748b',
                    font: {
                        size: 12,
                        family: 'Inter, sans-serif'
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f1f5f9',
                    borderColor: '#e2e8f0'
                },
                ticks: {
                    color: '#64748b',
                    font: {
                        size: 12,
                        family: 'Inter, sans-serif'
                    }
                }
            }
        }
    }
});

// Category performance chart
const categoryData = <?= json_encode($category_performance) ?>;
const ctx2 = document.getElementById('categoryChart').getContext('2d');

const categoryColors = [
    chartColors.primary,
    chartColors.success,
    chartColors.warning,
    chartColors.info
];

new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: categoryData.map(item => item.category_name),
        datasets: [{
            data: categoryData.map(item => item.total_completions),
            backgroundColor: categoryColors,
            borderWidth: 3,
            borderColor: '#ffffff',
            hoverBorderWidth: 4,
            hoverBorderColor: '#ffffff',
            hoverBackgroundColor: categoryColors.map(color => color + 'dd')
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false
        },
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true,
                    pointStyle: 'circle',
                    font: {
                        size: 12,
                        family: 'Inter, sans-serif'
                    },
                    color: '#64748b'
                }
            },
            tooltip: {
                backgroundColor: '#1e293b',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#e2e8f0',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                    }
                }
            }
        },
        cutout: '65%',
        elements: {
            arc: {
                borderWidth: 0
            }
        }
    }
});

function changePeriod(period) {
    const url = new URL(window.location);
    url.searchParams.set('period', period);
    window.location.href = url.toString();
}

function changeChartType(type) {
    const url = new URL(window.location);
    url.searchParams.set('chart', type);
    window.location.href = url.toString();
}
</script>

<?php include 'includes/admin_footer.php'; ?>
