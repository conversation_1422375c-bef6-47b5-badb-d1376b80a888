<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

$setup_messages = [];

try {
    // Create access_logs table for file/directory access tracking
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            username VARCHAR(100),
            ip_address VARCHAR(45) NOT NULL,
            country VARCHAR(100),
            city VARCHAR(100),
            user_agent TEXT,
            request_method VARCHAR(10) NOT NULL,
            request_uri TEXT NOT NULL,
            file_path TEXT,
            directory_path TEXT,
            access_type ENUM('FILE', 'DIRECTORY', 'API', 'PAGE') NOT NULL,
            response_code INT NOT NULL,
            response_size INT,
            referer TEXT,
            execution_time DECIMAL(10,6),
            blocked BOOLEAN DEFAULT FALSE,
            block_reason VARCHAR(255),
            risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
            details JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_ip_address (ip_address),
            INDEX idx_access_type (access_type),
            INDEX idx_response_code (response_code),
            INDEX idx_blocked (blocked),
            INDEX idx_risk_level (risk_level),
            INDEX idx_created_at (created_at),
            INDEX idx_file_path (file_path(255)),
            INDEX idx_directory_path (directory_path(255))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $setup_messages[] = "✅ access_logs table created successfully";

    // Create file_operations_logs table for detailed file operations
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS file_operations_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            username VARCHAR(100),
            ip_address VARCHAR(45) NOT NULL,
            operation_type ENUM('UPLOAD', 'DOWNLOAD', 'DELETE', 'MODIFY', 'COPY', 'MOVE', 'VIEW') NOT NULL,
            file_path TEXT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_size BIGINT,
            file_type VARCHAR(100),
            old_file_path TEXT,
            new_file_path TEXT,
            success BOOLEAN NOT NULL,
            error_message TEXT,
            risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
            details JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_operation_type (operation_type),
            INDEX idx_file_name (file_name),
            INDEX idx_success (success),
            INDEX idx_risk_level (risk_level),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $setup_messages[] = "✅ file_operations_logs table created successfully";

    // Create suspicious_activity_logs table for advanced threat detection
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS suspicious_activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            username VARCHAR(100),
            ip_address VARCHAR(45) NOT NULL,
            activity_type ENUM('BRUTE_FORCE', 'SQL_INJECTION', 'XSS_ATTEMPT', 'DIRECTORY_TRAVERSAL', 'UNAUTHORIZED_ACCESS', 'RATE_LIMIT_EXCEEDED', 'SUSPICIOUS_PATTERN', 'MALICIOUS_FILE', 'BOT_ACTIVITY') NOT NULL,
            severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
            description TEXT NOT NULL,
            request_data JSON,
            pattern_matched VARCHAR(255),
            auto_blocked BOOLEAN DEFAULT FALSE,
            resolved BOOLEAN DEFAULT FALSE,
            resolved_by VARCHAR(100),
            resolved_at TIMESTAMP NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_ip_address (ip_address),
            INDEX idx_activity_type (activity_type),
            INDEX idx_severity (severity),
            INDEX idx_auto_blocked (auto_blocked),
            INDEX idx_resolved (resolved),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $setup_messages[] = "✅ suspicious_activity_logs table created successfully";

    // Create performance_logs table for system performance monitoring
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS performance_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            metric_type ENUM('PAGE_LOAD', 'DATABASE_QUERY', 'FILE_OPERATION', 'API_CALL', 'MEMORY_USAGE', 'CPU_USAGE') NOT NULL,
            metric_name VARCHAR(100) NOT NULL,
            value DECIMAL(15,6) NOT NULL,
            unit VARCHAR(20) NOT NULL,
            threshold_exceeded BOOLEAN DEFAULT FALSE,
            context JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_metric_type (metric_type),
            INDEX idx_metric_name (metric_name),
            INDEX idx_threshold_exceeded (threshold_exceeded),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $setup_messages[] = "✅ performance_logs table created successfully";

    // Create admin_actions_logs table for admin activity tracking
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_actions_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            admin_username VARCHAR(100) NOT NULL,
            action_type ENUM('USER_MANAGEMENT', 'SYSTEM_CONFIG', 'LOG_ACCESS', 'DATA_EXPORT', 'SECURITY_SETTING', 'CHALLENGE_MANAGEMENT', 'CERTIFICATE_MANAGEMENT', 'BADGE_MANAGEMENT') NOT NULL,
            action_description TEXT NOT NULL,
            target_user_id INT,
            target_username VARCHAR(100),
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            success BOOLEAN NOT NULL,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_admin_id (admin_id),
            INDEX idx_action_type (action_type),
            INDEX idx_target_user_id (target_user_id),
            INDEX idx_success (success),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $setup_messages[] = "✅ admin_actions_logs table created successfully";

    // Insert initial log entry
    $pdo->exec("
        INSERT INTO security_logs (event_type, username, user_id, ip_address, country, city, risk_level, details) 
        VALUES ('ENHANCED_LOGGING_SETUP', '{$_SESSION['username']}', {$_SESSION['user_id']}, '{$_SERVER['REMOTE_ADDR']}', 'Unknown', 'Unknown', 'LOW', '{\"action\": \"enhanced_logging_tables_created\", \"admin\": \"{$_SESSION['username']}\", \"timestamp\": \"" . date('Y-m-d H:i:s') . "\"}')
    ");

    $setup_messages[] = "✅ Enhanced logging system setup completed successfully!";

} catch (PDOException $e) {
    $setup_messages[] = "❌ Error setting up enhanced logging: " . $e->getMessage();
}

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Enhanced Logging System Setup
                    </h4>
                </div>
                <div class="card-body">
                    <?php foreach ($setup_messages as $message): ?>
                        <div class="alert <?= strpos($message, '✅') !== false ? 'alert-success' : 'alert-danger' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endforeach; ?>

                    <div class="mt-4">
                        <h5>Enhanced Logging Features Added:</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="fas fa-folder-open text-primary me-2"></i>
                                <strong>Access Logs:</strong> Track all file and directory access attempts
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-file-alt text-info me-2"></i>
                                <strong>File Operations:</strong> Monitor file uploads, downloads, modifications
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <strong>Suspicious Activity:</strong> Advanced threat detection and pattern matching
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-tachometer-alt text-success me-2"></i>
                                <strong>Performance Monitoring:</strong> Track system performance metrics
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-user-shield text-danger me-2"></i>
                                <strong>Admin Actions:</strong> Comprehensive admin activity tracking
                            </li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <a href="logs.php" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i>View Enhanced Logs
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
