<?php
// Debug get_logs.php for audit logs display issue
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Debug get_logs.php - Audit Logs Display Issue</h2>";

try {
    echo "<h3>Step 1: Test Direct Database Query</h3>";
    
    // Test the exact query from get_logs.php
    $query = "
        SELECT id, action_type, table_name, record_id, username, old_values, new_values, ip_address, created_at
        FROM audit_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC 
        LIMIT 50
    ";
    
    echo "<strong>Query:</strong><br>";
    echo "<code>" . htmlspecialchars($query) . "</code><br><br>";
    
    $stmt = $pdo->query($query);
    $audit_logs = $stmt->fetchAll();
    
    echo "<strong>Results:</strong> Found " . count($audit_logs) . " audit log records<br><br>";
    
    if ($audit_logs) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Action</th><th>Table</th><th>Username</th><th>Old Values</th><th>New Values</th><th>Time</th></tr>";
        foreach (array_slice($audit_logs, 0, 5) as $log) {
            echo "<tr>";
            echo "<td>" . $log['id'] . "</td>";
            echo "<td>" . htmlspecialchars($log['action_type']) . "</td>";
            echo "<td>" . htmlspecialchars($log['table_name']) . "</td>";
            echo "<td>" . htmlspecialchars($log['username']) . "</td>";
            echo "<td><small>" . htmlspecialchars(substr($log['old_values'], 0, 50)) . "...</small></td>";
            echo "<td><small>" . htmlspecialchars(substr($log['new_values'], 0, 50)) . "...</small></td>";
            echo "<td>" . $log['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        if (count($audit_logs) > 5) {
            echo "<p><em>Showing first 5 of " . count($audit_logs) . " records</em></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No audit logs found in database</p>";
    }
    
    echo "<h3>Step 2: Test get_logs.php AJAX Call</h3>";
    
    echo "<strong>Testing AJAX endpoint:</strong> <code>get_logs.php?type=audit&limit=50</code><br><br>";
    
    // Simulate the AJAX call
    $_GET['type'] = 'audit';
    $_GET['limit'] = '50';
    
    ob_start();
    include 'get_logs.php';
    $ajax_output = ob_get_clean();
    
    echo "<strong>AJAX Output Length:</strong> " . strlen($ajax_output) . " characters<br>";
    
    if (strlen($ajax_output) > 0) {
        echo "<strong>AJAX Output Preview:</strong><br>";
        echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
        echo "<pre>" . htmlspecialchars(substr($ajax_output, 0, 1000)) . "</pre>";
        if (strlen($ajax_output) > 1000) {
            echo "<p><em>... (truncated, showing first 1000 characters)</em></p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ AJAX call returned empty output</p>";
    }
    
    echo "<h3>Step 3: Test Audit Logs Rendering Function</h3>";
    
    if ($audit_logs) {
        echo "<strong>Testing renderAuditLogs function directly:</strong><br><br>";
        
        // Include the get_logs.php file to get the function
        ob_start();
        
        function renderAuditLogs($logs) {
            if (empty($logs)) {
                echo '<div class="text-center p-4 text-muted">No audit logs found</div>';
                return;
            }
            
            echo '<table class="table table-hover mb-0">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>Action</th>';
            echo '<th>Table</th>';
            echo '<th>Record ID</th>';
            echo '<th>Username</th>';
            echo '<th>IP Address</th>';
            echo '<th>Time</th>';
            echo '<th>Actions</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($logs as $log) {
                echo '<tr>';
                echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['action_type']) . '</span></td>';
                echo '<td>' . htmlspecialchars($log['table_name']) . '</td>';
                echo '<td>' . htmlspecialchars($log['record_id'] ?? 'N/A') . '</td>';
                echo '<td>' . htmlspecialchars($log['username'] ?? 'System') . '</td>';
                echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
                echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
                echo '<td>';
                if (!empty($log['old_values']) || !empty($log['new_values'])) {
                    echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'audit\')">Changes</button>';
                }
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        }
        
        renderAuditLogs(array_slice($audit_logs, 0, 3));
        $render_output = ob_get_clean();
        
        echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        echo $render_output;
        echo "</div>";
    }
    
    echo "<h3>Step 4: Test Live AJAX Call</h3>";
    
    echo "<p><strong>Click the button below to test the actual AJAX call:</strong></p>";
    echo "<button onclick='testAjaxCall()' class='btn btn-primary'>Test AJAX Call</button>";
    echo "<div id='ajaxResult' style='margin-top: 20px; padding: 10px; border: 1px solid #ddd; background: #f9f9f9; display: none;'></div>";
    
    echo "<h3>Step 5: Check for JavaScript Errors</h3>";
    
    echo "<p>Open browser developer tools (F12) and check for JavaScript errors when clicking the Audit Logs tab.</p>";
    echo "<p><strong>Common issues:</strong></p>";
    echo "<ul>";
    echo "<li>JavaScript errors preventing AJAX calls</li>";
    echo "<li>Incorrect URL paths in AJAX requests</li>";
    echo "<li>PHP errors in get_logs.php causing empty responses</li>";
    echo "<li>Missing Bootstrap CSS/JS causing display issues</li>";
    echo "</ul>";
    
    echo "<h3>Step 6: Direct Links for Testing</h3>";
    
    echo "<p><strong>Test these links directly:</strong></p>";
    echo "<ul>";
    echo "<li><a href='get_logs.php?type=audit&limit=10' target='_blank'>get_logs.php?type=audit&limit=10</a></li>";
    echo "<li><a href='get_logs.php?type=security&limit=10' target='_blank'>get_logs.php?type=security&limit=10</a></li>";
    echo "<li><a href='logs.php' target='_blank'>Main Logs Page</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Debug Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<script>";
echo "function testAjaxCall() {";
echo "    const resultDiv = document.getElementById('ajaxResult');";
echo "    resultDiv.style.display = 'block';";
echo "    resultDiv.innerHTML = '<p>Loading...</p>';";
echo "    ";
echo "    fetch('get_logs.php?type=audit&limit=10')";
echo "        .then(response => response.text())";
echo "        .then(data => {";
echo "            resultDiv.innerHTML = '<h4>AJAX Response:</h4><pre>' + data.substring(0, 500) + '</pre>';";
echo "        })";
echo "        .catch(error => {";
echo "            resultDiv.innerHTML = '<h4>AJAX Error:</h4><p style=\"color: red;\">' + error.message + '</p>';";
echo "        });";
echo "}";
echo "</script>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='logs.php' class='btn btn-primary'>← Back to Logs</a> ";
echo "<a href='get_logs.php?type=audit&limit=5' target='_blank' class='btn btn-info'>Test get_logs.php</a>";
echo "</p>";
?>
