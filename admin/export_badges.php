<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get all badges with user and category info
    $sql = "
        SELECT 
            ub.id,
            ub.earned_at,
            u.username,
            u.email,
            cat.name as category_name,
            cat.icon as category_icon
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        JOIN categories cat ON ub.category_id = cat.id
        ORDER BY ub.earned_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $badges = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Set headers for CSV download
    $filename = 'badges_export_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8 (helps with Excel compatibility)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // CSV Headers
    $headers = [
        'Badge ID',
        'Username',
        'Email',
        'Category',
        'Earned Date',
        'Earned Time',
        'Days Since Earned'
    ];
    
    fputcsv($output, $headers);
    
    // Add badge data
    foreach ($badges as $badge) {
        $earned_date = new DateTime($badge['earned_at']);
        $now = new DateTime();
        $days_since = $now->diff($earned_date)->days;
        
        $row = [
            $badge['id'],
            $badge['username'],
            $badge['email'],
            $badge['category_name'],
            $earned_date->format('Y-m-d'),
            $earned_date->format('H:i:s'),
            $days_since
        ];
        
        fputcsv($output, $row);
    }
    
    // Add summary row
    fputcsv($output, []); // Empty row
    fputcsv($output, ['SUMMARY']);
    fputcsv($output, ['Total Badges', count($badges)]);
    
    // Get category breakdown
    $category_counts = [];
    foreach ($badges as $badge) {
        $category = $badge['category_name'];
        $category_counts[$category] = ($category_counts[$category] ?? 0) + 1;
    }
    
    foreach ($category_counts as $category => $count) {
        fputcsv($output, [$category . ' Badges', $count]);
    }
    
    // Get unique recipients
    $unique_recipients = count(array_unique(array_column($badges, 'username')));
    fputcsv($output, ['Unique Recipients', $unique_recipients]);
    
    // Get recent statistics
    $today = date('Y-m-d');
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $month_ago = date('Y-m-d', strtotime('-30 days'));
    
    $earned_today = count(array_filter($badges, function($badge) use ($today) {
        return date('Y-m-d', strtotime($badge['earned_at'])) === $today;
    }));
    
    $earned_this_week = count(array_filter($badges, function($badge) use ($week_ago) {
        return date('Y-m-d', strtotime($badge['earned_at'])) >= $week_ago;
    }));
    
    $earned_this_month = count(array_filter($badges, function($badge) use ($month_ago) {
        return date('Y-m-d', strtotime($badge['earned_at'])) >= $month_ago;
    }));
    
    fputcsv($output, ['Earned Today', $earned_today]);
    fputcsv($output, ['Earned This Week', $earned_this_week]);
    fputcsv($output, ['Earned This Month', $earned_this_month]);
    fputcsv($output, ['Export Date', date('Y-m-d H:i:s')]);
    fputcsv($output, ['Exported By', $_SESSION['username'] ?? 'Admin']);
    
    fclose($output);
    exit;
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Export failed: ' . $e->getMessage()]);
    exit;
}
?>
