<?php
/**
 * Setup Logs Tables
 * Creates necessary database tables for comprehensive logging system
 */

session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Create system_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            log_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL DEFAULT 'INFO',
            category VARCHAR(50) NOT NULL,
            message TEXT NOT NULL,
            details JSON NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_log_level (log_level),
            INDEX idx_category (category),
            INDEX idx_created_at (created_at)
        )
    ");

    // Create security_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type ENUM('LOGIN_SUCCESS', 'LOGIN_FAILED', 'LOGOUT', 'PASSWORD_CHANGE', 'ACCOUNT_LOCKED', 'SUSPICIOUS_ACTIVITY', 'PERMISSION_DENIED', 'DATA_ACCESS') NOT NULL,
            user_id INT NULL,
            username VARCHAR(255) NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            details JSON NULL,
            risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'LOW',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_event_type (event_type),
            INDEX idx_user_id (user_id),
            INDEX idx_risk_level (risk_level),
            INDEX idx_created_at (created_at),
            INDEX idx_ip_address (ip_address)
        )
    ");

    // Create audit_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            action_type ENUM('CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'IMPORT', 'ADMIN_ACTION') NOT NULL,
            table_name VARCHAR(100) NOT NULL,
            record_id INT NULL,
            user_id INT NULL,
            username VARCHAR(255) NULL,
            old_values JSON NULL,
            new_values JSON NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_action_type (action_type),
            INDEX idx_table_name (table_name),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        )
    ");

    // Create application_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS application_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            module VARCHAR(100) NOT NULL,
            action VARCHAR(100) NOT NULL,
            user_id INT NULL,
            session_id VARCHAR(255) NULL,
            request_method VARCHAR(10) NULL,
            request_uri TEXT NULL,
            response_code INT NULL,
            execution_time DECIMAL(10,4) NULL,
            memory_usage INT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_module (module),
            INDEX idx_action (action),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at),
            INDEX idx_response_code (response_code)
        )
    ");

    // Create error_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS error_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            error_type VARCHAR(100) NOT NULL,
            error_message TEXT NOT NULL,
            file_path VARCHAR(500) NULL,
            line_number INT NULL,
            stack_trace TEXT NULL,
            user_id INT NULL,
            session_id VARCHAR(255) NULL,
            request_data JSON NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_error_type (error_type),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        )
    ");

    // Insert sample log data for demonstration
    $sample_logs = [
        // System logs
        "INSERT INTO system_logs (log_level, category, message, details, ip_address) VALUES 
        ('INFO', 'SYSTEM', 'Application started successfully', '{\"version\": \"1.0.0\", \"environment\": \"production\"}', '127.0.0.1'),
        ('WARNING', 'DATABASE', 'Slow query detected', '{\"query_time\": 2.5, \"query\": \"SELECT * FROM users\"}', '127.0.0.1'),
        ('ERROR', 'EMAIL', 'Failed to send notification email', '{\"recipient\": \"<EMAIL>\", \"error\": \"SMTP timeout\"}', '127.0.0.1')",

        // Security logs
        "INSERT INTO security_logs (event_type, user_id, username, ip_address, details, risk_level) VALUES 
        ('LOGIN_SUCCESS', 1, 'admin', '127.0.0.1', '{\"method\": \"password\", \"browser\": \"Chrome\"}', 'LOW'),
        ('LOGIN_FAILED', NULL, 'unknown', '*************', '{\"attempts\": 3, \"reason\": \"invalid_password\"}', 'MEDIUM'),
        ('SUSPICIOUS_ACTIVITY', 2, 'testuser', '********', '{\"activity\": \"multiple_failed_challenges\", \"count\": 10}', 'HIGH')",

        // Audit logs
        "INSERT INTO audit_logs (action_type, table_name, record_id, user_id, username, old_values, new_values, ip_address) VALUES 
        ('UPDATE', 'users', 2, 1, 'admin', '{\"role\": \"student\"}', '{\"role\": \"admin\"}', '127.0.0.1'),
        ('DELETE', 'user_progress', 15, 1, 'admin', '{\"user_id\": 3, \"challenge_id\": 5}', NULL, '127.0.0.1'),
        ('CREATE', 'challenges', 10, 1, 'admin', NULL, '{\"title\": \"New SQL Challenge\", \"category_id\": 1}', '127.0.0.1')",

        // Application logs
        "INSERT INTO application_logs (module, action, user_id, request_method, request_uri, response_code, execution_time, ip_address) VALUES 
        ('dashboard', 'view', 1, 'GET', '/admin/dashboard.php', 200, 0.1250, '127.0.0.1'),
        ('challenges', 'complete', 2, 'POST', '/modules/sql/login_bypass.php', 200, 0.3500, '************'),
        ('certificates', 'generate', 2, 'POST', '/utils/certificate_generator.php', 200, 1.2000, '************')",

        // Error logs
        "INSERT INTO error_logs (error_type, error_message, file_path, line_number, user_id, ip_address) VALUES 
        ('PHP_ERROR', 'Undefined variable: user_data', '/pages/profile.php', 45, 2, '************'),
        ('DATABASE_ERROR', 'Table users does not exist', '/config/db_connect.php', 12, NULL, '127.0.0.1'),
        ('VALIDATION_ERROR', 'Invalid email format provided', '/auth/signup.php', 78, NULL, '********')"
    ];

    foreach ($sample_logs as $query) {
        $pdo->exec($query);
    }

    echo "<h1>✅ Logs Setup Complete!</h1>";
    echo "<p>Successfully created all logging tables and inserted sample data:</p>";
    echo "<ul>";
    echo "<li>✅ <strong>system_logs</strong> - General system events and monitoring</li>";
    echo "<li>✅ <strong>security_logs</strong> - Authentication and security events</li>";
    echo "<li>✅ <strong>audit_logs</strong> - Data changes and admin actions</li>";
    echo "<li>✅ <strong>application_logs</strong> - User activities and performance</li>";
    echo "<li>✅ <strong>error_logs</strong> - Application errors and exceptions</li>";
    echo "</ul>";
    echo "<p><a href='logs.php' class='btn btn-primary'>📊 View Logs Dashboard</a></p>";
    echo "<p><a href='dashboard.php' class='btn btn-secondary'>🏠 Back to Dashboard</a></p>";

} catch (Exception $e) {
    echo "<h1>❌ Setup Error</h1>";
    echo "<p>Error creating log tables: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
