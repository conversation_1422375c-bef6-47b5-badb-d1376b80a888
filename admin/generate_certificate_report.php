<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get certificate statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_certificates,
            COUNT(DISTINCT user_id) as unique_recipients,
            COUNT(CASE WHEN DATE(issued_at) = CURDATE() THEN 1 END) as issued_today,
            COUNT(CASE WHEN DATE(issued_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as issued_this_week,
            COUNT(CASE WHEN DATE(issued_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as issued_this_month
        FROM user_certificates
    ");
    $stats = $stmt->fetch();
    
    // Get category distribution
    $stmt = $pdo->query("
        SELECT 
            cat.name as category_name,
            cat.icon as category_icon,
            COUNT(*) as certificate_count,
            COUNT(DISTINCT uc.user_id) as unique_users
        FROM user_certificates uc
        JOIN categories cat ON uc.category_id = cat.id
        GROUP BY cat.id, cat.name, cat.icon
        ORDER BY certificate_count DESC
    ");
    $category_distribution = $stmt->fetchAll();
    
    // Get recent certificates
    $stmt = $pdo->query("
        SELECT 
            uc.certificate_code,
            uc.issued_at,
            u.username,
            cat.name as category_name
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories cat ON uc.category_id = cat.id
        ORDER BY uc.issued_at DESC
        LIMIT 10
    ");
    $recent_certificates = $stmt->fetchAll();
    
    // Get monthly trends (last 6 months)
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(issued_at, '%Y-%m') as month,
            COUNT(*) as count
        FROM user_certificates
        WHERE issued_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(issued_at, '%Y-%m')
        ORDER BY month
    ");
    $monthly_trends = $stmt->fetchAll();
    
    // Start output buffering
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Certificate Management Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
                margin: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #2563eb;
                padding-bottom: 20px;
            }
            .header h1 {
                color: #2563eb;
                margin: 0;
                font-size: 24px;
            }
            .header p {
                margin: 5px 0;
                color: #666;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 15px;
                margin-bottom: 30px;
            }
            .stat-card {
                text-align: center;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
            }
            .stat-value {
                font-size: 24px;
                font-weight: bold;
                color: #2563eb;
                display: block;
            }
            .stat-label {
                font-size: 11px;
                color: #666;
                margin-top: 5px;
            }
            .section {
                margin-bottom: 30px;
            }
            .section h2 {
                color: #2563eb;
                font-size: 18px;
                margin-bottom: 15px;
                border-bottom: 1px solid #e5e7eb;
                padding-bottom: 5px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
                font-size: 11px;
            }
            th {
                background: #2563eb;
                color: white;
                padding: 8px 6px;
                text-align: left;
                font-weight: bold;
            }
            td {
                padding: 6px;
                border-bottom: 1px solid #e5e7eb;
            }
            tr:nth-child(even) {
                background: #f9fafb;
            }
            .category-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #e5e7eb;
            }
            .progress-bar {
                width: 100px;
                height: 6px;
                background: #e5e7eb;
                border-radius: 3px;
                overflow: hidden;
                margin: 0 10px;
            }
            .progress-fill {
                height: 100%;
                background: #2563eb;
            }
            .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #666;
                border-top: 1px solid #e5e7eb;
                padding-top: 15px;
            }
            @media print {
                body { margin: 0; }
                @page { margin: 1cm; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Certificate Management Report</h1>
            <p>TryMeOut Vulnerability Platform</p>
            <p>Generated on: <?= date('F j, Y \a\t g:i A') ?></p>
            <p>Report by: <?= htmlspecialchars($_SESSION['username'] ?? 'Admin') ?></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['total_certificates']) ?></span>
                <div class="stat-label">Total Certificates</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['unique_recipients']) ?></span>
                <div class="stat-label">Unique Recipients</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['issued_today']) ?></span>
                <div class="stat-label">Issued Today</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['issued_this_week']) ?></span>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-card">
                <span class="stat-value"><?= number_format($stats['issued_this_month']) ?></span>
                <div class="stat-label">This Month</div>
            </div>
        </div>

        <div class="section">
            <h2>Category Distribution</h2>
            <?php 
            $max_count = !empty($category_distribution) ? max(array_column($category_distribution, 'certificate_count')) : 1;
            foreach ($category_distribution as $category): 
            ?>
                <div class="category-item">
                    <div style="flex: 1;">
                        <strong><?= htmlspecialchars($category['category_name']) ?></strong>
                        <br>
                        <small><?= $category['unique_users'] ?> unique users</small>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?= ($category['certificate_count'] / $max_count) * 100 ?>%"></div>
                    </div>
                    <div style="text-align: right;">
                        <strong><?= $category['certificate_count'] ?></strong>
                        <br>
                        <small>certificates</small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="section">
            <h2>Recent Certificates (Last 10)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Certificate Code</th>
                        <th>Username</th>
                        <th>Category</th>
                        <th>Issued Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_certificates as $cert): ?>
                        <tr>
                            <td style="font-family: monospace;"><?= htmlspecialchars($cert['certificate_code']) ?></td>
                            <td><?= htmlspecialchars($cert['username']) ?></td>
                            <td><?= htmlspecialchars($cert['category_name']) ?></td>
                            <td><?= date('M j, Y g:i A', strtotime($cert['issued_at'])) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($monthly_trends)): ?>
        <div class="section">
            <h2>Monthly Trends (Last 6 Months)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Certificates Issued</th>
                        <th>Growth</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $prev_count = 0;
                    foreach ($monthly_trends as $trend): 
                        $growth = $prev_count > 0 ? (($trend['count'] - $prev_count) / $prev_count) * 100 : 0;
                        $growth_class = $growth > 0 ? 'color: green' : ($growth < 0 ? 'color: red' : '');
                    ?>
                        <tr>
                            <td><?= date('F Y', strtotime($trend['month'] . '-01')) ?></td>
                            <td><?= number_format($trend['count']) ?></td>
                            <td style="<?= $growth_class ?>">
                                <?= $prev_count > 0 ? sprintf('%+.1f%%', $growth) : 'N/A' ?>
                            </td>
                        </tr>
                    <?php 
                        $prev_count = $trend['count'];
                    endforeach; 
                    ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>This report contains confidential information about certificate issuance.</p>
            <p>TryMeOut Vulnerability Platform - Certificate Management System</p>
        </div>
    </body>
    </html>
    <?php
    
    $html = ob_get_clean();
    
    // Check if we want to download directly or show in browser
    $download = isset($_GET['download']) && $_GET['download'] === 'true';
    
    if ($download) {
        // Set headers for HTML display that will be printed to PDF
        header('Content-Type: text/html; charset=utf-8');
        
        // Add print functionality
        echo '<script>
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                    setTimeout(function() {
                        window.close();
                    }, 2000);
                }, 1000);
            };
        </script>';
        
        echo $html;
        
        // Add print button
        echo '<div style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    Print Report
                </button>
                <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                    Close
                </button>
              </div>';
    } else {
        // Just display the HTML for preview
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo '<h1>Report Error</h1><p>Failed to generate report: ' . htmlspecialchars($e->getMessage()) . '</p>';
}
?>
