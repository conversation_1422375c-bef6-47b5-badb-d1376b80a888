<?php
session_start();
require '../config/db_connect.php';
require '../config/upload_config.php';
require '../utils/admin_profile_manager.php';

// Set page variables
$page_title = 'Profile Picture Moderation';
$page_subtitle = 'Review and manage user profile pictures';
$current_page = 'profile_moderation';

// Initialize profile manager
$profile_manager = new AdminProfileManager($pdo, $_SESSION['user_id']);

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'remove_single':
                $user_id = (int)$_POST['user_id'];
                $reason = $_POST['reason'] ?? 'Inappropriate content';
                try {
                    $profile_manager->removeUserProfilePicture($user_id, $reason);
                    $_SESSION['success_message'] = "Profile picture removed successfully.";
                } catch (Exception $e) {
                    $_SESSION['error_message'] = "Failed to remove profile picture: " . $e->getMessage();
                }
                break;
                
            case 'bulk_remove':
                $user_ids = $_POST['user_ids'] ?? [];
                $reason = $_POST['bulk_reason'] ?? 'Bulk moderation action';
                if (!empty($user_ids)) {
                    $result = $profile_manager->bulkRemoveProfilePictures($user_ids, $reason);
                    $_SESSION['success_message'] = "Removed {$result['removed_count']} profile pictures.";
                    if (!empty($result['errors'])) {
                        $_SESSION['error_message'] = "Some errors occurred: " . implode(', ', $result['errors']);
                    }
                } else {
                    $_SESSION['error_message'] = "No users selected for bulk action.";
                }
                break;
        }
        header("Location: profile_moderation.php");
        exit;
    }
}

// Pagination
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get users with custom pictures
$users = $profile_manager->getUsersWithCustomPictures($per_page, $offset);
$total_users = $profile_manager->getCustomPictureCount();
$total_pages = ceil($total_users / $per_page);

// Get moderation stats
$stats = $profile_manager->getModerationStats();

// Get recent action history
$recent_actions = $profile_manager->getProfilePictureActionHistory(10);

include 'includes/admin_header.php';
?>

<style>
    :root {
        /* Match Admin Panel Color Scheme */
        --primary-color: #2563eb;
        --primary-dark: #1d4ed8;
        --primary-light: #3b82f6;
        --secondary-color: #64748b;
        --accent-color: #0ea5e9;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --info-color: #0891b2;

        /* Neutral Colors */
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;

        /* Design System */
        --border-radius: 8px;
        --border-radius-lg: 12px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --transition: all 0.2s ease-in-out;
    }

    /* Enhanced Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 32px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 32px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        position: relative;
        z-index: 1;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .stat-card {
        background: white;
        padding: 28px;
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        text-align: center;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

    .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        font-size: 24px;
        color: white;
    }

    .stat-icon.custom {
        background: linear-gradient(135deg, var(--warning-color), #ea580c);
    }

    .stat-icon.default {
        background: linear-gradient(135deg, var(--info-color), var(--accent-color));
    }

    .stat-icon.removed {
        background: linear-gradient(135deg, var(--success-color), #16a34a);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--gray-900);
        margin-bottom: 8px;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.95rem;
        color: var(--gray-600);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Enhanced Bulk Actions */
    .bulk-actions {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        padding: 28px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 32px;
        border: 1px solid var(--gray-200);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;
    }

    .bulk-actions::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--warning-color));
    }

    .bulk-actions h5 {
        color: var(--gray-900);
        font-weight: 600;
        font-size: 1.25rem;
    }

    .bulk-actions .btn {
        font-weight: 500;
        border-radius: var(--border-radius);
        transition: all 0.2s ease-in-out;
        position: relative;
        overflow: hidden;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bulk-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .bulk-actions .btn:active {
        transform: translateY(0);
    }

    .bulk-actions .btn-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        border-color: var(--warning-color);
        color: white;
        font-weight: 600;
    }

    .bulk-actions .btn-warning:hover:not(:disabled) {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        border-color: #d97706;
    }

    .bulk-actions .btn-warning:disabled {
        background: #e5e7eb;
        border-color: #d1d5db;
        color: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .bulk-actions .btn-outline-primary {
        border-width: 2px;
        font-weight: 500;
    }

    .bulk-actions .btn-outline-danger {
        border-width: 2px;
        font-weight: 500;
    }

    .bulk-actions .btn-sm {
        padding: 8px 16px;
        font-size: 0.875rem;
        min-height: 36px;
    }

    .bulk-stats {
        border: 1px solid #e5e7eb;
        background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
    }

    .bulk-stats .stat-item {
        padding: 8px;
    }

    .bulk-stats .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }

    .bulk-stats .stat-label {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 4px;
    }

    .bulk-actions-status {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .btn-loading {
        pointer-events: none;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .bulk-actions {
            padding: 20px;
        }

        .bulk-actions .btn {
            width: 100%;
            margin-bottom: 8px;
        }

        .bulk-stats .stat-number {
            font-size: 1.25rem;
        }

        .d-flex.gap-2 {
            flex-direction: column;
            gap: 8px !important;
        }

        .d-flex.gap-2 .btn {
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .bulk-actions {
            padding: 16px;
        }

        .bulk-stats .row {
            text-align: center;
        }

        .bulk-stats .col-6 {
            margin-bottom: 12px;
        }
    }

    /* Enhanced Moderation Cards */
    .moderation-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 32px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 32px;
        transition: var(--transition);
    }

    .moderation-card:hover {
        box-shadow: var(--shadow-md);
    }

    .moderation-card h3 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    /* Enhanced User Cards */
    .user-card {
        background: white;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        padding: 20px;
        margin-bottom: 20px;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .user-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
        transform: scaleY(0);
        transition: var(--transition);
    }

    .user-card:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .user-card:hover::before {
        transform: scaleY(1);
    }

    .user-card.selected {
        border-color: var(--success-color);
        background: rgba(16, 185, 129, 0.02);
    }

    .user-card.selected::before {
        background: var(--success-color);
        transform: scaleY(1);
    }

    /* Enhanced Profile Preview */
    .profile-preview {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--gray-200);
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
    }

    .user-card:hover .profile-preview {
        border-color: var(--primary-color);
        transform: scale(1.05);
    }

    .user-info h6 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 4px;
    }

    .user-info small {
        color: var(--gray-500);
    }

    /* Enhanced Action Buttons */
    .btn-outline-warning {
        border: 2px solid var(--warning-color);
        color: var(--warning-color);
        background: transparent;
        transition: var(--transition);
    }

    .btn-outline-warning:hover {
        background: var(--warning-color);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    /* Enhanced Form Controls */
    .form-select, .form-control {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        padding: 12px 16px;
        transition: var(--transition);
    }

    .form-select:focus, .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    /* Enhanced Badges */
    .badge {
        padding: 8px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .badge.bg-info {
        background: var(--info-color) !important;
    }

    /* Enhanced Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--gray-500);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 24px;
        color: var(--gray-300);
    }

    .empty-state h5 {
        color: var(--gray-600);
        margin-bottom: 12px;
    }

    /* Enhanced Table */
    .table-responsive {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        background: var(--gray-50);
        border-bottom: 2px solid var(--gray-200);
        color: var(--gray-700);
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 16px;
    }

    .table td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid var(--gray-100);
    }

    .table tbody tr:hover {
        background: var(--gray-50);
    }

    /* Enhanced Pagination */
    .pagination {
        margin-top: 32px;
    }

    .page-link {
        border: 2px solid var(--gray-200);
        color: var(--gray-600);
        padding: 12px 16px;
        margin: 0 4px;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .page-link:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        background: rgba(37, 99, 235, 0.1);
    }

    .page-item.active .page-link {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* Enhanced Checkbox */
    .form-check-input {
        width: 20px;
        height: 20px;
        border: 2px solid var(--gray-300);
        border-radius: 4px;
        transition: var(--transition);
    }

    .form-check-input:checked {
        background: var(--success-color);
        border-color: var(--success-color);
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .user-card {
            padding: 16px;
        }

        .profile-preview {
            width: 60px;
            height: 60px;
        }

        .page-header {
            padding: 24px;
        }

        .page-header h1 {
            font-size: 2rem;
        }
    }
</style>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-images me-3"></i>Profile Picture Moderation</h1>
    <p>Review and manage user profile pictures to maintain community standards</p>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($_SESSION['success_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($_SESSION['error_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Enhanced Moderation Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon custom">
            <i class="fas fa-user-edit"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['custom_pictures']) ?></div>
        <div class="stat-label">Custom Pictures</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon default">
            <i class="fas fa-user-circle"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['default_pictures']) ?></div>
        <div class="stat-label">Default Pictures</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon removed">
            <i class="fas fa-shield-alt"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['recent_removals']) ?></div>
        <div class="stat-label">Removed (30 days)</div>
    </div>
</div>

<!-- Enhanced Bulk Actions -->
<div class="bulk-actions">
    <div class="d-flex align-items-center justify-content-between mb-4">
        <h5 class="mb-0">
            <i class="fas fa-tasks me-2 text-primary"></i>
            Bulk Actions
        </h5>
        <div class="bulk-actions-status" id="bulkStatus" style="display: none;">
            <span class="badge bg-info" id="selectedCount">0 selected</span>
        </div>
    </div>

    <form method="POST" id="bulkForm">
        <input type="hidden" name="action" value="bulk_remove">

        <!-- Selection Controls -->
        <div class="row g-3 mb-4">
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2 align-items-center">
                    <label class="form-label mb-0 me-3 fw-semibold">Selection:</label>
                    <button type="button" class="btn btn-outline-primary btn-sm px-3" onclick="selectAll()" id="selectAllBtn">
                        <i class="fas fa-check-square me-1"></i>Select All
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm px-3" onclick="selectNone()">
                        <i class="fas fa-square me-1"></i>Clear Selection
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm px-3" onclick="selectInverse()">
                        <i class="fas fa-exchange-alt me-1"></i>Invert Selection
                    </button>
                </div>
            </div>
        </div>

        <!-- Action Controls -->
        <div class="row g-3 align-items-end">
            <div class="col-lg-6 col-md-12">
                <label for="bulk_reason" class="form-label fw-semibold">
                    <i class="fas fa-clipboard-list me-1"></i>Reason for removal
                </label>
                <select class="form-select" name="bulk_reason" id="bulk_reason" required>
                    <option value="">Select a reason...</option>
                    <option value="Inappropriate content">🚫 Inappropriate content</option>
                    <option value="Violates community guidelines">📋 Violates community guidelines</option>
                    <option value="Spam or promotional">📢 Spam or promotional</option>
                    <option value="Copyright violation">©️ Copyright violation</option>
                    <option value="Other policy violation">⚠️ Other policy violation</option>
                </select>
            </div>
            <div class="col-lg-6 col-md-12">
                <div class="d-flex flex-column flex-md-row gap-2">
                    <button type="submit" class="btn btn-warning px-4 py-2 flex-fill" onclick="return confirmBulkAction()" id="bulkRemoveBtn" disabled>
                        <span class="btn-content">
                            <i class="fas fa-trash-alt me-2"></i>Remove Selected
                        </span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Processing...
                        </span>
                    </button>
                    <button type="button" class="btn btn-outline-danger px-4 py-2" onclick="showBulkPreview()" id="previewBtn" disabled>
                        <i class="fas fa-eye me-2"></i>Preview
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Users with Custom Pictures -->
<div class="moderation-card">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-images me-2"></i>Users with Custom Profile Pictures</h3>
        <span class="badge bg-info"><?= number_format($total_users) ?> total</span>
    </div>
    
    <?php if (empty($users)): ?>
        <div class="empty-state">
            <i class="fas fa-images"></i>
            <h5>No custom profile pictures found</h5>
            <p>All users are using the default profile picture. This is great for maintaining consistency!</p>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($users as $user): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="user-card">
                        <div class="d-flex align-items-center mb-3">
                            <input type="checkbox" class="form-check-input me-3" name="user_ids[]"
                                   value="<?= $user['id'] ?>" form="bulkForm"
                                   onchange="toggleCardSelection(this)">
                            <img src="../<?= htmlspecialchars($user['profile_picture']) ?>"
                                 alt="<?= htmlspecialchars($user['username']) ?>"
                                 class="profile-preview me-3"
                                 onerror="this.src='../<?= DEFAULT_PROFILE_PICTURE ?>'">
                            <div class="user-info flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($user['username']) ?></h6>
                                <small class="text-muted d-block"><?= htmlspecialchars($user['email']) ?></small>
                                <small class="text-info">
                                    <i class="fas fa-trophy me-1"></i>
                                    <?= $user['total_completions'] ?> completions
                                </small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-column">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Joined <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                </small>
                                <small class="text-warning mt-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Requires Review
                                </small>
                            </div>
                            <button class="btn btn-sm btn-outline-warning"
                                    onclick="removeProfilePicture(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')"
                                    title="Remove Profile Picture">
                                <i class="fas fa-trash-alt me-1"></i>
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Profile moderation pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- Recent Actions -->
<div class="moderation-card">
    <h4><i class="fas fa-history me-2"></i>Recent Moderation Actions</h4>
    <?php if (empty($recent_actions)): ?>
        <p class="text-muted">No recent moderation actions.</p>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Admin</th>
                        <th>Target User</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_actions as $action): ?>
                        <tr>
                            <td><?= date('M j, Y g:i A', strtotime($action['created_at'])) ?></td>
                            <td><?= htmlspecialchars($action['admin_username']) ?></td>
                            <td><?= htmlspecialchars($action['target_username']) ?></td>
                            <td><?= htmlspecialchars($action['details']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Remove Profile Picture Modal -->
<div class="modal fade" id="removeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Profile Picture</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="remove_single">
                    <input type="hidden" name="user_id" id="removeUserId">
                    
                    <p>Are you sure you want to remove the profile picture for <strong id="removeUsername"></strong>?</p>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for removal</label>
                        <select class="form-select" name="reason" required>
                            <option value="Inappropriate content">Inappropriate content</option>
                            <option value="Violates community guidelines">Violates community guidelines</option>
                            <option value="Spam or promotional">Spam or promotional</option>
                            <option value="Copyright violation">Copyright violation</option>
                            <option value="Other policy violation">Other policy violation</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Remove Picture</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function removeProfilePicture(userId, username) {
    document.getElementById('removeUserId').value = userId;
    document.getElementById('removeUsername').textContent = username;
    new bootstrap.Modal(document.getElementById('removeModal')).show();
}

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        toggleCardSelection(cb);
    });

    updateBulkActionState();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    checkboxes.forEach(cb => {
        cb.checked = false;
        toggleCardSelection(cb);
    });
    updateBulkActionState();
}

function selectInverse() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    checkboxes.forEach(cb => {
        cb.checked = !cb.checked;
        toggleCardSelection(cb);
    });
    updateBulkActionState();
}

function toggleCardSelection(checkbox) {
    const card = checkbox.closest('.user-card');
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }

    updateBulkActionState();
}

function updateBulkActionState() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const selectedCheckboxes = document.querySelectorAll('input[name="user_ids[]"]:checked');
    const selectedCount = selectedCheckboxes.length;
    const totalCount = checkboxes.length;

    // Update selection counters
    document.getElementById('selectedUsers').textContent = selectedCount;
    document.getElementById('completionRate').textContent = totalCount > 0 ? Math.round((selectedCount / totalCount) * 100) + '%' : '0%';

    // Estimate processing time (roughly 0.5 seconds per user)
    const estimatedSeconds = selectedCount * 0.5;
    document.getElementById('estimatedTime').textContent = estimatedSeconds < 60 ?
        Math.round(estimatedSeconds) + 's' :
        Math.round(estimatedSeconds / 60) + 'm';

    // Update status badge
    const bulkStatus = document.getElementById('bulkStatus');
    const selectedCountBadge = document.getElementById('selectedCount');

    if (selectedCount > 0) {
        bulkStatus.style.display = 'block';
        selectedCountBadge.textContent = `${selectedCount} selected`;
        selectedCountBadge.className = selectedCount === totalCount ? 'badge bg-success' : 'badge bg-info';
    } else {
        bulkStatus.style.display = 'none';
    }

    // Update button states
    const bulkRemoveBtn = document.getElementById('bulkRemoveBtn');
    const previewBtn = document.getElementById('previewBtn');
    const reasonSelect = document.getElementById('bulk_reason');

    const hasSelection = selectedCount > 0;
    const hasReason = reasonSelect.value !== '';

    bulkRemoveBtn.disabled = !hasSelection || !hasReason;
    previewBtn.disabled = !hasSelection;

    // Update select all button
    updateSelectAllButton();
}

function updateSelectAllButton() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    const someChecked = Array.from(checkboxes).some(cb => cb.checked);

    if (selectAllBtn) {
        if (allChecked && checkboxes.length > 0) {
            selectAllBtn.innerHTML = '<i class="fas fa-square me-1"></i>Deselect All';
            selectAllBtn.className = 'btn btn-outline-danger btn-sm px-3';
        } else {
            selectAllBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>Select All';
            selectAllBtn.className = 'btn btn-outline-primary btn-sm px-3';
        }
    }
}

function showBulkPreview() {
    const selected = document.querySelectorAll('input[name="user_ids[]"]:checked');
    const reason = document.getElementById('bulk_reason').value;

    if (selected.length === 0) {
        showToast('Please select at least one user to preview.', 'warning');
        return;
    }

    const usernames = Array.from(selected).map(cb => {
        const card = cb.closest('.user-card');
        return {
            name: card.querySelector('h6').textContent,
            email: card.querySelector('small').textContent,
            completions: card.querySelector('.text-info').textContent
        };
    });

    // Create preview modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>Bulk Action Preview
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Action:</strong> Remove profile pictures<br>
                        <strong>Reason:</strong> ${reason || 'No reason specified'}<br>
                        <strong>Affected Users:</strong> ${selected.length}
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Completions</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${usernames.map(user => `
                                    <tr>
                                        <td>${user.name}</td>
                                        <td><small class="text-muted">${user.email}</small></td>
                                        <td><small>${user.completions}</small></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function confirmBulkAction() {
    const selected = document.querySelectorAll('input[name="user_ids[]"]:checked');
    const reason = document.getElementById('bulk_reason').value;

    if (selected.length === 0) {
        showToast('Please select at least one user to perform bulk action.', 'warning');
        return false;
    }

    if (!reason) {
        showToast('Please select a reason for removal.', 'warning');
        document.getElementById('bulk_reason').focus();
        return false;
    }

    // Show loading state
    const btn = document.getElementById('bulkRemoveBtn');
    const btnContent = btn.querySelector('.btn-content');
    const btnLoading = btn.querySelector('.btn-loading');

    btnContent.classList.add('d-none');
    btnLoading.classList.remove('d-none');
    btn.disabled = true;

    // Enhanced confirmation dialog
    const usernames = Array.from(selected).map(cb => {
        const card = cb.closest('.user-card');
        return card.querySelector('h6').textContent;
    });

    const message = selected.length === 1 ?
        `Are you sure you want to remove the profile picture for "${usernames[0]}"?\n\nReason: ${reason}` :
        `Are you sure you want to remove profile pictures for ${selected.length} users?\n\nReason: ${reason}\n\nUsers: ${usernames.slice(0, 3).join(', ')}${usernames.length > 3 ? ` and ${usernames.length - 3} more` : ''}`;

    const confirmed = confirm(message);

    if (!confirmed) {
        // Reset button state
        btnContent.classList.remove('d-none');
        btnLoading.classList.add('d-none');
        btn.disabled = false;
    }

    return confirmed;
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';

    const icon = type === 'warning' ? 'exclamation-triangle' :
                 type === 'success' ? 'check-circle' :
                 type === 'danger' ? 'times-circle' : 'info-circle';

    toast.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Update bulk action state on page load
    updateBulkActionState();

    // Add event listener for reason selection
    const reasonSelect = document.getElementById('bulk_reason');
    if (reasonSelect) {
        reasonSelect.addEventListener('change', updateBulkActionState);
    }

    // Add smooth scrolling to pagination
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href') && this.getAttribute('href').includes('?page=')) {
                // Smooth scroll to top of moderation section
                const moderationCard = document.querySelector('.moderation-card');
                if (moderationCard) {
                    moderationCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + A to select all
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            selectAll();
        }

        // Escape to clear selection
        if (e.key === 'Escape') {
            selectNone();
        }
    });

    // Add tooltips to buttons
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include 'includes/admin_footer.php'; ?>
