<?php
// User Activity Logging Summary
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>User Activity Logging Summary</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo ".summary-card { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin: 20px 0; padding: 25px; }";
echo ".feature-list { list-style: none; padding: 0; }";
echo ".feature-list li { padding: 8px 0; border-bottom: 1px solid #eee; }";
echo ".feature-list li:last-child { border-bottom: none; }";
echo ".status-implemented { color: #28a745; font-weight: bold; }";
echo ".status-new { color: #007bff; font-weight: bold; }";
echo "</style>";
echo "</head><body class='container-fluid'>";

echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>📋 User Activity Logging Summary</h1>";
echo "<p class='text-center text-muted'>Comprehensive overview of implemented user activity logging</p>";

echo "<div class='summary-card'>";
echo "<h2>✅ Implemented User Activity Logging</h2>";
echo "<p>The following user operations are now being comprehensively logged in your admin system:</p>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h3>🔐 Authentication & Security</h3>";
echo "<ul class='feature-list'>";
echo "<li><span class='status-implemented'>✅ LOGIN ATTEMPTS</span><br><small>Successful and failed login attempts with user association</small></li>";
echo "<li><span class='status-implemented'>✅ PASSWORD CHANGES</span><br><small>User-initiated password changes via settings</small></li>";
echo "<li><span class='status-new'>🆕 PASSWORD RESETS</span><br><small>Password resets via email verification</small></li>";
echo "<li><span class='status-implemented'>✅ ACCOUNT LOCKOUTS</span><br><small>Brute force protection triggers</small></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h3>👤 Profile Management</h3>";
echo "<ul class='feature-list'>";
echo "<li><span class='status-new'>🆕 USERNAME CHANGES</span><br><small>User-initiated username modifications</small></li>";
echo "<li><span class='status-new'>🆕 PROFILE PICTURE UPLOADS</span><br><small>Profile picture changes with file details</small></li>";
echo "<li><span class='status-implemented'>✅ SETTINGS ACCESS</span><br><small>User accessing settings page</small></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h3>👨‍💼 Admin Operations</h3>";
echo "<ul class='feature-list'>";
echo "<li><span class='status-new'>🆕 ADMIN USER EDITS</span><br><small>Admin modifying user profiles</small></li>";
echo "<li><span class='status-implemented'>✅ ADMIN PROFILE PICTURE REMOVAL</span><br><small>Admin removing user profile pictures</small></li>";
echo "<li><span class='status-implemented'>✅ ADMIN ACTIONS</span><br><small>Various administrative actions</small></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h3>🔍 System Monitoring</h3>";
echo "<ul class='feature-list'>";
echo "<li><span class='status-implemented'>✅ SECURITY EVENTS</span><br><small>High-risk activities and anomalies</small></li>";
echo "<li><span class='status-implemented'>✅ SUSPICIOUS ACTIVITIES</span><br><small>Unusual login patterns and behaviors</small></li>";
echo "<li><span class='status-implemented'>✅ SYSTEM EVENTS</span><br><small>Application errors and system events</small></li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='summary-card'>";
echo "<h2>📊 Logging Details</h2>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h3>🗃️ Audit Logs (Data Changes)</h3>";
echo "<p>Tracks all data modifications with:</p>";
echo "<ul>";
echo "<li><strong>Action:</strong> CREATE, UPDATE, DELETE</li>";
echo "<li><strong>Table:</strong> Which database table was affected</li>";
echo "<li><strong>Record ID:</strong> Specific record that was changed</li>";
echo "<li><strong>Username:</strong> Who made the change</li>";
echo "<li><strong>Old Values:</strong> Previous data values</li>";
echo "<li><strong>New Values:</strong> Updated data values</li>";
echo "<li><strong>Timestamp:</strong> When the change occurred</li>";
echo "<li><strong>IP Address:</strong> Source IP of the change</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h3>🛡️ Security Logs (User Activities)</h3>";
echo "<p>Tracks security-relevant events with:</p>";
echo "<ul>";
echo "<li><strong>Event Type:</strong> Type of security event</li>";
echo "<li><strong>Username:</strong> User involved in the event</li>";
echo "<li><strong>User ID:</strong> Database user identifier</li>";
echo "<li><strong>Risk Level:</strong> LOW, MEDIUM, HIGH</li>";
echo "<li><strong>Details:</strong> JSON data with event specifics</li>";
echo "<li><strong>IP Address:</strong> Source IP address</li>";
echo "<li><strong>User Agent:</strong> Browser/client information</li>";
echo "<li><strong>Session ID:</strong> User session identifier</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='summary-card'>";
echo "<h2>🎯 Specific User Operations Logged</h2>";

$operations = [
    'Username Change' => [
        'trigger' => 'User updates username in settings',
        'audit_log' => 'UPDATE on users table with old/new username',
        'security_log' => 'USERNAME_CHANGED event with change details',
        'risk_level' => 'LOW'
    ],
    'Password Change' => [
        'trigger' => 'User changes password in settings',
        'audit_log' => 'UPDATE on users table (password hash redacted)',
        'security_log' => 'PASSWORD_CHANGED event with method and strength',
        'risk_level' => 'MEDIUM'
    ],
    'Profile Picture Upload' => [
        'trigger' => 'User uploads new profile picture',
        'audit_log' => 'UPDATE on users table with old/new file paths',
        'security_log' => 'PROFILE_PICTURE_CHANGED with file details',
        'risk_level' => 'LOW'
    ],
    'Password Reset' => [
        'trigger' => 'User resets password via email',
        'audit_log' => 'UPDATE on users table (password hash redacted)',
        'security_log' => 'PASSWORD_RESET event with reset method',
        'risk_level' => 'HIGH'
    ],
    'Admin User Edit' => [
        'trigger' => 'Admin modifies user profile',
        'audit_log' => 'UPDATE on users table for each changed field',
        'security_log' => 'ADMIN_USER_EDIT with admin and target details',
        'risk_level' => 'MEDIUM'
    ]
];

echo "<div class='table-responsive'>";
echo "<table class='table table-striped'>";
echo "<tr><th>Operation</th><th>Trigger</th><th>Audit Log</th><th>Security Log</th><th>Risk Level</th></tr>";

foreach ($operations as $operation => $details) {
    $risk_color = $details['risk_level'] === 'HIGH' ? 'danger' : ($details['risk_level'] === 'MEDIUM' ? 'warning' : 'success');
    echo "<tr>";
    echo "<td><strong>$operation</strong></td>";
    echo "<td><small>{$details['trigger']}</small></td>";
    echo "<td><small>{$details['audit_log']}</small></td>";
    echo "<td><small>{$details['security_log']}</small></td>";
    echo "<td><span class='badge bg-$risk_color'>{$details['risk_level']}</span></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";
echo "</div>";

echo "<div class='summary-card'>";
echo "<h2>🖥️ Admin Dashboard Access</h2>";
echo "<p>View all logged user activities in your admin dashboard:</p>";

echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<div class='card border-primary'>";
echo "<div class='card-body text-center'>";
echo "<h5 class='card-title'>📋 Audit Logs</h5>";
echo "<p class='card-text'>View all data changes and modifications</p>";
echo "<a href='logs.php?tab=audit' class='btn btn-primary'>View Audit Logs</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card border-warning'>";
echo "<div class='card-body text-center'>";
echo "<h5 class='card-title'>🛡️ Security Logs</h5>";
echo "<p class='card-text'>Monitor security events and user activities</p>";
echo "<a href='logs.php?tab=security' class='btn btn-warning'>View Security Logs</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card border-success'>";
echo "<div class='card-body text-center'>";
echo "<h5 class='card-title'>🔐 Login Attempts</h5>";
echo "<p class='card-text'>Track authentication attempts and failures</p>";
echo "<a href='logs.php?tab=login' class='btn btn-success'>View Login Logs</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='summary-card'>";
echo "<h2>🧪 Testing & Verification</h2>";
echo "<p>Use these tools to test and verify the logging functionality:</p>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h4>🔬 Testing Tools</h4>";
echo "<ul>";
echo "<li><a href='test_user_activity_logging.php' class='btn btn-sm btn-info'>Test User Activity Logging</a></li>";
echo "<li><a href='../auth/final_login_verification.php' class='btn btn-sm btn-info'>Test Login Logging</a></li>";
echo "<li><a href='../pages/settings.php' class='btn btn-sm btn-secondary'>User Settings Page</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h4>📊 Monitoring Tools</h4>";
echo "<ul>";
echo "<li><a href='logs.php' class='btn btn-sm btn-primary'>Main Admin Logs</a></li>";
echo "<li><a href='dashboard.php' class='btn btn-sm btn-secondary'>Admin Dashboard</a></li>";
echo "<li><a href='get_logs.php?type=audit&limit=10' class='btn btn-sm btn-outline-primary'>AJAX Audit Test</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

// Get recent activity statistics
try {
    echo "<div class='summary-card'>";
    echo "<h2>📈 Recent Activity Statistics</h2>";
    
    $stats = [];
    
    // Audit logs count
    $stmt = $pdo->query("SELECT COUNT(*) FROM audit_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['audit_24h'] = $stmt->fetchColumn();
    
    // Security logs count
    $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['security_24h'] = $stmt->fetchColumn();
    
    // Login attempts count
    $stmt = $pdo->query("SELECT COUNT(*) FROM login_attempts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['login_24h'] = $stmt->fetchColumn();
    
    echo "<div class='row text-center'>";
    echo "<div class='col-md-4'>";
    echo "<h3 class='text-primary'>{$stats['audit_24h']}</h3>";
    echo "<p>Audit Logs (24h)</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h3 class='text-warning'>{$stats['security_24h']}</h3>";
    echo "<p>Security Events (24h)</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h3 class='text-success'>{$stats['login_24h']}</h3>";
    echo "<p>Login Attempts (24h)</p>";
    echo "</div>";
    echo "</div>";
    
    $total_activity = $stats['audit_24h'] + $stats['security_24h'] + $stats['login_24h'];
    
    if ($total_activity > 0) {
        echo "<div class='alert alert-success text-center'>";
        echo "<h4>✅ User Activity Logging is Active!</h4>";
        echo "<p>Total logged activities in last 24 hours: <strong>$total_activity</strong></p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info text-center'>";
        echo "<h4>ℹ️ No Recent Activity</h4>";
        echo "<p>No user activities logged in the last 24 hours. This is normal for new installations.</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-warning'>Unable to fetch activity statistics: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4 mb-4'>";
echo "<a href='logs.php' class='btn btn-primary btn-lg me-3'>📊 Open Admin Logs</a>";
echo "<a href='test_user_activity_logging.php' class='btn btn-info btn-lg me-3'>🧪 Test Logging</a>";
echo "<a href='dashboard.php' class='btn btn-secondary btn-lg'>🏠 Admin Dashboard</a>";
echo "</div>";

echo "</div></body></html>";
?>
