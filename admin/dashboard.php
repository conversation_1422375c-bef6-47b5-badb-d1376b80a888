<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'Dashboard';
$page_subtitle = 'Overview of your vulnerability platform';

// Get dashboard statistics
$stats = [];

// Total users
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
$stats['total_users'] = $stmt->fetchColumn();

// Active users (users with at least 1 completed challenge)
$stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) as active FROM user_progress WHERE status = 'completed'");
$stats['active_users'] = $stmt->fetchColumn();

// New users today
$stmt = $pdo->query("SELECT COUNT(*) as new_today FROM users WHERE DATE(created_at) = CURDATE()");
$stats['new_users_today'] = $stmt->fetchColumn();

// Total challenges completed
$stmt = $pdo->query("SELECT COUNT(*) as total FROM user_progress WHERE status = 'completed'");
$stats['total_completions'] = $stmt->fetchColumn();

// Challenges completed today
$stmt = $pdo->query("SELECT COUNT(*) as today FROM user_progress WHERE status = 'completed' AND DATE(completed_at) = CURDATE()");
$stats['completions_today'] = $stmt->fetchColumn();

// Total certificates issued
$stmt = $pdo->query("SELECT COUNT(*) as total FROM user_certificates");
$stats['total_certificates'] = $stmt->fetchColumn();

// Certificates issued today
$stmt = $pdo->query("SELECT COUNT(*) as today FROM user_certificates WHERE DATE(issued_at) = CURDATE()");
$stats['certificates_today'] = $stmt->fetchColumn();

// Total badges awarded
$stmt = $pdo->query("SELECT COUNT(*) as total FROM user_badges");
$stats['total_badges'] = $stmt->fetchColumn();

// Brute force protection statistics
try {
    require '../config/brute_force_protection.php';
    $bruteForceProtection = new BruteForceProtection($pdo);
    $security_stats = $bruteForceProtection->getLockoutStats();
    $stats['locked_accounts'] = $security_stats['current_locked'] ?? 0;
    $stats['recent_attempts'] = $security_stats['recent_attempts'] ?? 0;
} catch (Exception $e) {
    $stats['locked_accounts'] = 0;
    $stats['recent_attempts'] = 0;
}

// Category completion stats
$stmt = $pdo->query("
    SELECT
        c.category_id,
        cat.name as category_name,
        COUNT(DISTINCT up.user_id) as users_completed,
        COUNT(*) as total_completions
    FROM user_progress up
    JOIN challenges c ON up.challenge_id = c.id
    JOIN categories cat ON c.category_id = cat.id
    WHERE up.status = 'completed'
    GROUP BY c.category_id, cat.name
    ORDER BY total_completions DESC
");
$category_stats = $stmt->fetchAll();

// Recent activities
$stmt = $pdo->query("
    SELECT
        u.username,
        ch.title as challenge_title,
        cat.name as category_name,
        up.completed_at
    FROM user_progress up
    JOIN users u ON up.user_id = u.id
    JOIN challenges ch ON up.challenge_id = ch.id
    JOIN categories cat ON ch.category_id = cat.id
    WHERE up.status = 'completed'
    ORDER BY up.completed_at DESC
    LIMIT 10
");
$recent_activities = $stmt->fetchAll();

// System health metrics (simulated)
$system_health = [
    'cpu_usage' => rand(15, 35),
    'memory_usage' => rand(45, 75),
    'disk_usage' => rand(25, 60),
    'database_status' => 'optimal'
];

// Log admin dashboard access
try {
    if (file_exists('../utils/logger.php')) {
        require_once '../utils/logger.php';
        $logger = getLogger();
        $logger->logApplication('admin_dashboard', 'view', 200, microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']);
        $logger->logSecurity('DATA_ACCESS', $_SESSION['user_id'], $_SESSION['username'], [
            'page' => 'admin_dashboard',
            'action' => 'view_statistics'
        ], 'LOW');
    }
} catch (Exception $e) {
    // Silently handle logging errors
}

include 'includes/admin_header.php';
?>

<style>
    /* PROFESSIONAL STAT CARDS */
    .stat-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--gray-300);
    }

    .stat-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
    }

    .stat-number {
        font-size: 32px;
        font-weight: 800;
        color: var(--gray-900);
        margin: 8px 0;
        line-height: 1;
    }

    .stat-label {
        font-size: 14px;
        color: var(--gray-500);
        font-weight: 500;
        margin: 0;
    }

    .stat-change {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        margin-top: 12px;
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 500;
    }

    .stat-change.positive {
        background: rgba(5, 150, 105, 0.1);
        color: var(--success-color);
    }

    .stat-change.negative {
        background: rgba(220, 38, 38, 0.1);
        color: var(--danger-color);
    }

    /* Professional Security Cards */
    .security-stat-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .security-stat-card:hover {
        border-color: #d1d5db;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
    }

    .security-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6);
    }

    .security-stat-card.danger::before {
        background: linear-gradient(90deg, #ef4444, #f97316);
    }

    .security-stat-card.warning::before {
        background: linear-gradient(90deg, #f59e0b, #eab308);
    }

    .security-stat-card.success::before {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .security-stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .security-stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .security-stat-icon.danger {
        background: linear-gradient(135deg, #ef4444, #f97316);
    }

    .security-stat-icon.warning {
        background: linear-gradient(135deg, #f59e0b, #eab308);
    }

    .security-stat-icon.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .security-stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #111827;
        margin: 8px 0 4px 0;
        line-height: 1;
    }

    .security-stat-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
        margin: 0 0 12px 0;
    }

    .security-stat-status {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .security-stat-status.secure {
        background: #ecfdf5;
        color: #065f46;
    }

    .security-stat-status.alert {
        background: #fef2f2;
        color: #991b1b;
    }

    .security-stat-status.warning {
        background: #fffbeb;
        color: #92400e;
    }

    .security-manage-btn {
        background: transparent;
        border: 1px solid #e5e7eb;
        color: #6b7280;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .security-manage-btn:hover {
        border-color: #6366f1;
        color: #6366f1;
        background: #f8fafc;
    }

    .chart-container {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
    }

    .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--gray-200);
    }

    .chart-title {
        font-size: 18px;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
    }

    .chart-wrapper {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .chart-wrapper canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
    }

    .activity-item {
        padding: 16px;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
    }

    .activity-item:hover {
        background: var(--gray-50);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-meta {
        font-size: 12px;
        color: var(--gray-500);
    }

    .system-metric {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .system-metric:last-child {
        border-bottom: none;
    }

    .metric-label {
        font-weight: 500;
        color: var(--gray-700);
    }

    .metric-value {
        font-weight: 600;
        color: var(--gray-900);
    }

    .progress-modern {
        height: 8px;
        background: var(--gray-200);
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-bar-modern {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-icon" style="background: var(--primary-color);">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-number"><?= number_format($stats['total_users']) ?></div>
            <p class="stat-label">Total Users</p>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +<?= $stats['new_users_today'] ?> today
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-icon" style="background: var(--success-color);">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
            <div class="stat-number"><?= number_format($stats['active_users']) ?></div>
            <p class="stat-label">Active Users</p>
            <div class="stat-change positive">
                <i class="fas fa-percentage"></i>
                <?= $stats['total_users'] > 0 ? round(($stats['active_users']/$stats['total_users'])*100, 1) : 0 ?>% engagement
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-icon" style="background: var(--warning-color);">
                    <i class="fas fa-trophy"></i>
                </div>
            </div>
            <div class="stat-number"><?= number_format($stats['total_completions']) ?></div>
            <p class="stat-label">Total Completions</p>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +<?= $stats['completions_today'] ?> today
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-icon" style="background: var(--info-color);">
                    <i class="fas fa-certificate"></i>
                </div>
            </div>
            <div class="stat-number"><?= number_format($stats['total_certificates']) ?></div>
            <p class="stat-label">Certificates Issued</p>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +<?= $stats['certificates_today'] ?> today
            </div>
        </div>
    </div>
</div>

<!-- Security Statistics Row -->
<div class="row g-4 mb-4">
    <div class="col-lg-6">
        <div class="security-stat-card <?= $stats['locked_accounts'] > 0 ? 'danger' : 'success' ?>">
            <div class="security-stat-header">
                <div class="security-stat-icon <?= $stats['locked_accounts'] > 0 ? 'danger' : 'success' ?>">
                    <i class="fas fa-<?= $stats['locked_accounts'] > 0 ? 'lock' : 'shield-check' ?>"></i>
                </div>
                <a href="brute_force_management.php" class="security-manage-btn">
                    <i class="fas fa-cog"></i> Manage
                </a>
            </div>
            <div class="security-stat-number"><?= number_format($stats['locked_accounts']) ?></div>
            <p class="security-stat-label">Locked Accounts</p>
            <div class="security-stat-status <?= $stats['locked_accounts'] > 0 ? 'alert' : 'secure' ?>">
                <i class="fas fa-<?= $stats['locked_accounts'] > 0 ? 'exclamation-triangle' : 'check-circle' ?>"></i>
                <?= $stats['locked_accounts'] > 0 ? 'Security Alert' : 'All Clear' ?>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="security-stat-card <?= $stats['recent_attempts'] > 10 ? 'danger' : ($stats['recent_attempts'] > 5 ? 'warning' : 'success') ?>">
            <div class="security-stat-header">
                <div class="security-stat-icon <?= $stats['recent_attempts'] > 10 ? 'danger' : ($stats['recent_attempts'] > 5 ? 'warning' : 'success') ?>">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <a href="brute_force_management.php" class="security-manage-btn">
                    <i class="fas fa-eye"></i> Details
                </a>
            </div>
            <div class="security-stat-number"><?= number_format($stats['recent_attempts']) ?></div>
            <p class="security-stat-label">Failed Attempts (1h)</p>
            <div class="security-stat-status <?= $stats['recent_attempts'] > 10 ? 'alert' : ($stats['recent_attempts'] > 5 ? 'warning' : 'secure') ?>">
                <i class="fas fa-<?= $stats['recent_attempts'] > 10 ? 'arrow-up' : 'arrow-down' ?>"></i>
                <?= $stats['recent_attempts'] > 10 ? 'High Activity' : ($stats['recent_attempts'] > 5 ? 'Moderate' : 'Normal') ?>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Row -->
<div class="row g-4 mb-4">
    <!-- Category Performance Chart -->
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">Category Performance</h3>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary active" onclick="switchChartData('completions')">Completions</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="switchChartData('users')">Users</button>
                </div>
            </div>
            <div class="chart-wrapper">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="col-lg-4">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">System Health</h3>
                <span class="badge bg-success">All Systems Operational</span>
            </div>
            <div class="system-metrics">
                <div class="system-metric">
                    <span class="metric-label">CPU Usage</span>
                    <span class="metric-value"><?= $system_health['cpu_usage'] ?>%</span>
                </div>
                <div class="progress-modern mb-3">
                    <div class="progress-bar-modern" style="width: <?= $system_health['cpu_usage'] ?>%; background: var(--primary-color);"></div>
                </div>

                <div class="system-metric">
                    <span class="metric-label">Memory Usage</span>
                    <span class="metric-value"><?= $system_health['memory_usage'] ?>%</span>
                </div>
                <div class="progress-modern mb-3">
                    <div class="progress-bar-modern" style="width: <?= $system_health['memory_usage'] ?>%; background: var(--warning-color);"></div>
                </div>

                <div class="system-metric">
                    <span class="metric-label">Disk Usage</span>
                    <span class="metric-value"><?= $system_health['disk_usage'] ?>%</span>
                </div>
                <div class="progress-modern mb-3">
                    <div class="progress-bar-modern" style="width: <?= $system_health['disk_usage'] ?>%; background: var(--success-color);"></div>
                </div>

                <div class="system-metric">
                    <span class="metric-label">Database</span>
                    <span class="badge bg-success">Optimal</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-lg-12">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">Quick Actions</h3>
                <span class="badge bg-info">Admin Tools</span>
            </div>
            <div class="row g-3">
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="users.php" class="btn btn-outline-primary w-100">
                        <i class="fas fa-users d-block mb-2"></i>
                        <small>Users</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="badges.php" class="btn btn-outline-success w-100">
                        <i class="fas fa-medal d-block mb-2"></i>
                        <small>Badges</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="certificates.php" class="btn btn-outline-warning w-100">
                        <i class="fas fa-certificate d-block mb-2"></i>
                        <small>Certificates</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="challenges.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-code d-block mb-2"></i>
                        <small>Challenges</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="brute_force_management.php" class="btn btn-outline-danger w-100">
                        <i class="fas fa-shield-alt d-block mb-2"></i>
                        <small>Security</small>
                        <?php if ($stats['locked_accounts'] > 0): ?>
                            <span class="badge bg-danger position-absolute top-0 start-100 translate-middle"><?= $stats['locked_accounts'] ?></span>
                        <?php endif; ?>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="logs.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-clipboard-list d-block mb-2"></i>
                        <small>System Logs</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="profile_moderation.php" class="btn btn-outline-warning w-100">
                        <i class="fas fa-images d-block mb-2"></i>
                        <small>Profile Moderation</small>
                    </a>
                </div>
            </div>

            <!-- Second Row for Additional Actions -->
            <div class="row g-3 mt-2">
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="analytics.php" class="btn btn-outline-dark w-100">
                        <i class="fas fa-chart-bar d-block mb-2"></i>
                        <small>Analytics</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="settings.php" class="btn btn-outline-dark w-100">
                        <i class="fas fa-cog d-block mb-2"></i>
                        <small>Settings</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="../pages/dashboard.php" class="btn btn-outline-dark w-100">
                        <i class="fas fa-external-link-alt d-block mb-2"></i>
                        <small>User View</small>
                    </a>
                </div>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="../auth/test_brute_force.php" class="btn btn-outline-dark w-100" target="_blank">
                        <i class="fas fa-vial d-block mb-2"></i>
                        <small>Test Security</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="chart-container">
    <div class="chart-header">
        <h3 class="chart-title">Recent Activities</h3>
        <a href="analytics.php" class="btn btn-sm btn-outline-primary">View All</a>
    </div>
    <div class="recent-activities">
        <?php if (empty($recent_activities)): ?>
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>No recent activities</p>
            </div>
        <?php else: ?>
            <?php foreach ($recent_activities as $activity): ?>
                <div class="activity-item">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <strong><?= htmlspecialchars($activity['username']) ?></strong>
                            completed
                            <span class="text-primary"><?= htmlspecialchars($activity['challenge_title']) ?></span>
                            in <?= htmlspecialchars($activity['category_name']) ?>
                        </div>
                        <div class="activity-meta">
                            <?= date('M j, Y g:i A', strtotime($activity['completed_at'])) ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// Category performance chart
const categoryData = <?= json_encode($category_stats) ?>;
const ctx = document.getElementById('categoryChart').getContext('2d');

// Professional color palette
const chartColors = [
    '#2563eb', // Primary blue
    '#059669', // Success green
    '#d97706', // Warning orange
    '#0891b2'  // Info cyan
];

// Initialize chart with completions data
let categoryChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: categoryData.map(item => item.category_name),
        datasets: [{
            data: categoryData.map(item => item.total_completions),
            backgroundColor: chartColors,
            borderWidth: 3,
            borderColor: '#ffffff',
            hoverBorderWidth: 4,
            hoverBorderColor: '#ffffff',
            hoverBackgroundColor: chartColors.map(color => color + 'dd')
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false
        },
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true,
                    pointStyle: 'circle',
                    font: {
                        size: 13,
                        family: 'Inter, sans-serif'
                    },
                    color: '#64748b'
                }
            },
            tooltip: {
                backgroundColor: '#1e293b',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#e2e8f0',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        const currentMode = document.querySelector('.btn-group .btn.active').textContent;
                        return `${context.label}: ${context.parsed} ${currentMode.toLowerCase()} (${percentage}%)`;
                    }
                }
            }
        },
        cutout: '65%',
        elements: {
            arc: {
                borderWidth: 0
            }
        }
    }
});

// Function to switch between completions and users data
function switchChartData(mode) {
    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Update chart data
    let newData;
    if (mode === 'users') {
        newData = categoryData.map(item => item.users_completed);
    } else {
        newData = categoryData.map(item => item.total_completions);
    }

    // Animate the data change
    categoryChart.data.datasets[0].data = newData;
    categoryChart.update('active');
}
</script>

<?php include 'includes/admin_footer.php'; ?>
