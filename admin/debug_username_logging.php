<?php
// Debug Username Change Logging
session_start();
require '../config/db_connect.php';
require '../config/logger.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Debug Username Change Logging</h2>";

try {
    $logger = getLogger();
    
    // Get current user for testing
    $user_id = $_SESSION['user_id'];
    $current_username = $_SESSION['username'];
    
    echo "<h3>Current User: " . htmlspecialchars($current_username) . " (ID: $user_id)</h3>";
    
    // Check if audit_logs table exists and structure
    echo "<h4>Step 1: Check Database Tables</h4>";
    
    try {
        $stmt = $pdo->query("DESCRIBE audit_logs");
        $columns = $stmt->fetchAll();
        echo "✅ audit_logs table exists with columns:<br>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    } catch (Exception $e) {
        echo "❌ audit_logs table issue: " . $e->getMessage() . "<br>";
    }
    
    try {
        $stmt = $pdo->query("DESCRIBE security_logs");
        $columns = $stmt->fetchAll();
        echo "✅ security_logs table exists<br>";
    } catch (Exception $e) {
        echo "❌ security_logs table issue: " . $e->getMessage() . "<br>";
    }
    
    // Test direct logging
    echo "<h4>Step 2: Test Direct Logging</h4>";
    
    $test_old_username = $current_username;
    $test_new_username = $current_username . "_test";
    
    echo "Testing username change: '$test_old_username' → '$test_new_username'<br>";
    
    // Test audit logging
    echo "<strong>Testing audit logging...</strong><br>";
    try {
        $logger->logAuditEvent('UPDATE', 'users', $user_id, $test_old_username, ['username' => $test_old_username], ['username' => $test_new_username]);
        echo "✅ Audit event logged<br>";
    } catch (Exception $e) {
        echo "❌ Audit logging error: " . $e->getMessage() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // Test user profile change logging
    echo "<strong>Testing user profile change logging...</strong><br>";
    try {
        $logger->logUserProfileChange($user_id, $test_old_username, 'username', $test_old_username, $test_new_username);
        echo "✅ User profile change logged<br>";
    } catch (Exception $e) {
        echo "❌ User profile change error: " . $e->getMessage() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // Test security event logging
    echo "<strong>Testing security event logging...</strong><br>";
    try {
        $logger->logSecurityEvent('USERNAME_CHANGED', $test_new_username, $user_id, 'LOW', [
            'old_username' => $test_old_username,
            'new_username' => $test_new_username,
            'changed_by' => 'user'
        ]);
        echo "✅ Security event logged<br>";
    } catch (Exception $e) {
        echo "❌ Security event error: " . $e->getMessage() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // Check if records were inserted
    echo "<h4>Step 3: Verify Records in Database</h4>";
    
    echo "<strong>Checking audit_logs...</strong><br>";
    try {
        $stmt = $pdo->prepare("
            SELECT id, action, table_name, record_id, username, old_values, new_values, created_at 
            FROM audit_logs 
            WHERE record_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        $audit_records = $stmt->fetchAll();
        
        if ($audit_records) {
            echo "✅ Found " . count($audit_records) . " audit records:<br>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Action</th><th>Table</th><th>Username</th><th>Old Values</th><th>New Values</th><th>Time</th></tr>";
            foreach ($audit_records as $record) {
                echo "<tr>";
                echo "<td>" . $record['id'] . "</td>";
                echo "<td>" . htmlspecialchars($record['action']) . "</td>";
                echo "<td>" . htmlspecialchars($record['table_name']) . "</td>";
                echo "<td>" . htmlspecialchars($record['username']) . "</td>";
                echo "<td>" . htmlspecialchars($record['old_values']) . "</td>";
                echo "<td>" . htmlspecialchars($record['new_values']) . "</td>";
                echo "<td>" . $record['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "❌ No audit records found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Audit query error: " . $e->getMessage() . "<br>";
    }
    
    echo "<strong>Checking security_logs...</strong><br>";
    try {
        $stmt = $pdo->prepare("
            SELECT id, event_type, username, user_id, risk_level, details, created_at 
            FROM security_logs 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        $security_records = $stmt->fetchAll();
        
        if ($security_records) {
            echo "✅ Found " . count($security_records) . " security records:<br>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Event Type</th><th>Username</th><th>Risk Level</th><th>Details</th><th>Time</th></tr>";
            foreach ($security_records as $record) {
                echo "<tr>";
                echo "<td>" . $record['id'] . "</td>";
                echo "<td>" . htmlspecialchars($record['event_type']) . "</td>";
                echo "<td>" . htmlspecialchars($record['username']) . "</td>";
                echo "<td>" . htmlspecialchars($record['risk_level']) . "</td>";
                echo "<td>" . htmlspecialchars(substr($record['details'], 0, 100)) . "...</td>";
                echo "<td>" . $record['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "❌ No security records found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Security query error: " . $e->getMessage() . "<br>";
    }
    
    // Test admin dashboard queries
    echo "<h4>Step 4: Test Admin Dashboard Queries</h4>";
    
    echo "<strong>Testing audit logs query...</strong><br>";
    try {
        $query = "
            SELECT id, action, table_name, record_id, username, old_values, new_values, ip_address, created_at
            FROM audit_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY created_at DESC 
            LIMIT 10
        ";
        $stmt = $pdo->query($query);
        $admin_audit = $stmt->fetchAll();
        echo "Admin audit query returned " . count($admin_audit) . " records<br>";
        
        if ($admin_audit) {
            echo "<strong>Recent audit logs (what admin dashboard shows):</strong><br>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Action</th><th>Table</th><th>Username</th><th>Changes</th><th>Time</th></tr>";
            foreach (array_slice($admin_audit, 0, 5) as $log) {
                echo "<tr>";
                echo "<td><span style='background: blue; color: white; padding: 2px 6px; border-radius: 3px;'>" . htmlspecialchars($log['action']) . "</span></td>";
                echo "<td>" . htmlspecialchars($log['table_name']) . "</td>";
                echo "<td>" . htmlspecialchars($log['username']) . "</td>";
                echo "<td><small>" . htmlspecialchars(substr($log['old_values'] . ' → ' . $log['new_values'], 0, 50)) . "...</small></td>";
                echo "<td>" . date('M j, g:i A', strtotime($log['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        }
    } catch (Exception $e) {
        echo "❌ Admin audit query error: " . $e->getMessage() . "<br>";
    }
    
    echo "<strong>Testing security logs query...</strong><br>";
    try {
        $query = "
            SELECT id, event_type, username, user_id, risk_level, details, created_at 
            FROM security_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY created_at DESC 
            LIMIT 10
        ";
        $stmt = $pdo->query($query);
        $admin_security = $stmt->fetchAll();
        echo "Admin security query returned " . count($admin_security) . " records<br>";
        
        if ($admin_security) {
            echo "<strong>Recent security logs (what admin dashboard shows):</strong><br>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Event Type</th><th>Username</th><th>Risk Level</th><th>Details</th><th>Time</th></tr>";
            foreach (array_slice($admin_security, 0, 5) as $log) {
                $risk_color = $log['risk_level'] === 'HIGH' ? 'red' : ($log['risk_level'] === 'MEDIUM' ? 'orange' : 'green');
                echo "<tr>";
                echo "<td>" . htmlspecialchars($log['event_type']) . "</td>";
                echo "<td>" . htmlspecialchars($log['username']) . "</td>";
                echo "<td><span style='background: $risk_color; color: white; padding: 2px 6px; border-radius: 3px;'>" . htmlspecialchars($log['risk_level']) . "</span></td>";
                echo "<td><small>" . htmlspecialchars(substr($log['details'], 0, 50)) . "...</small></td>";
                echo "<td>" . date('M j, g:i A', strtotime($log['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        }
    } catch (Exception $e) {
        echo "❌ Admin security query error: " . $e->getMessage() . "<br>";
    }
    
    // Check settings.php integration
    echo "<h4>Step 5: Check Settings.php Integration</h4>";
    
    echo "<strong>Checking if logger is included in settings.php...</strong><br>";
    $settings_content = file_get_contents('../pages/settings.php');
    if (strpos($settings_content, 'require \'../config/logger.php\'') !== false) {
        echo "✅ Logger is included in settings.php<br>";
    } else {
        echo "❌ Logger is NOT included in settings.php<br>";
    }
    
    if (strpos($settings_content, 'logUserProfileChange') !== false) {
        echo "✅ logUserProfileChange is called in settings.php<br>";
    } else {
        echo "❌ logUserProfileChange is NOT called in settings.php<br>";
    }
    
    if (strpos($settings_content, 'USERNAME_CHANGED') !== false) {
        echo "✅ USERNAME_CHANGED security event is logged in settings.php<br>";
    } else {
        echo "❌ USERNAME_CHANGED security event is NOT logged in settings.php<br>";
    }
    
    // Cleanup test records
    echo "<h4>Step 6: Cleanup</h4>";
    try {
        $pdo->exec("DELETE FROM audit_logs WHERE username LIKE '%_test' OR old_values LIKE '%_test%' OR new_values LIKE '%_test%'");
        $pdo->exec("DELETE FROM security_logs WHERE username LIKE '%_test' OR details LIKE '%_test%'");
        echo "✅ Test records cleaned up<br>";
    } catch (Exception $e) {
        echo "⚠️ Cleanup error: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Debug Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffc107;'>";
echo "<h4>🔍 Next Steps</h4>";
echo "<p>Based on the results above:</p>";
echo "<ol>";
echo "<li>If tables exist but no records are found, there's a logging integration issue</li>";
echo "<li>If logger methods fail, there's a code issue</li>";
echo "<li>If records exist but admin dashboard doesn't show them, there's a query issue</li>";
echo "<li>If settings.php doesn't include logging calls, that's the problem</li>";
echo "</ol>";
echo "</div>";

echo "<p style='margin-top: 20px;'>";
echo "<a href='../pages/settings.php'>← Test Settings Page</a> | ";
echo "<a href='logs.php'>Admin Logs</a> | ";
echo "<a href='test_user_activity_logging.php'>Activity Test</a>";
echo "</p>";
?>
