<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get all users with their progress data
    $sql = "
        SELECT 
            u.id,
            u.username,
            u.email,
            u.role,
            u.created_at,
            u.is_verified,
            COUNT(DISTINCT up.id) as total_completions,
            COUNT(DISTINCT ub.id) as total_badges,
            COUNT(DISTINCT uc.id) as total_certificates,
            MAX(up.completed_at) as last_activity
        FROM users u
        LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        LEFT JOIN user_badges ub ON u.id = ub.user_id
        LEFT JOIN user_certificates uc ON u.id = uc.user_id
        GROUP BY u.id, u.username, u.email, u.role, u.created_at, u.is_verified
        ORDER BY u.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Set headers for CSV download
    $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8 (helps with Excel compatibility)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // CSV Headers
    $headers = [
        'ID',
        'Username',
        'Email',
        'Role',
        'Verified',
        'Registration Date',
        'Registration Time',
        'Total Completions',
        'Total Badges',
        'Total Certificates',
        'Last Activity Date',
        'Last Activity Time',
        'Account Status'
    ];
    
    fputcsv($output, $headers);
    
    // Add user data
    foreach ($users as $user) {
        $registration_date = new DateTime($user['created_at']);
        $last_activity_date = $user['last_activity'] ? new DateTime($user['last_activity']) : null;
        
        $row = [
            $user['id'],
            $user['username'],
            $user['email'],
            ucfirst($user['role']),
            $user['is_verified'] ? 'Yes' : 'No',
            $registration_date->format('Y-m-d'),
            $registration_date->format('H:i:s'),
            $user['total_completions'],
            $user['total_badges'],
            $user['total_certificates'],
            $last_activity_date ? $last_activity_date->format('Y-m-d') : 'No Activity',
            $last_activity_date ? $last_activity_date->format('H:i:s') : '',
            ($user['total_completions'] > 0 || $user['total_badges'] > 0) ? 'Active' : 'Inactive'
        ];
        
        fputcsv($output, $row);
    }
    
    // Add summary row
    fputcsv($output, []); // Empty row
    fputcsv($output, ['SUMMARY']);
    fputcsv($output, ['Total Users', count($users)]);
    fputcsv($output, ['Admin Users', count(array_filter($users, function($u) { return $u['role'] === 'admin'; }))]);
    fputcsv($output, ['Student Users', count(array_filter($users, function($u) { return $u['role'] === 'student'; }))]);
    fputcsv($output, ['Verified Users', count(array_filter($users, function($u) { return $u['is_verified']; }))]);
    fputcsv($output, ['Active Users', count(array_filter($users, function($u) { return $u['total_completions'] > 0; }))]);
    fputcsv($output, ['Export Date', date('Y-m-d H:i:s')]);
    fputcsv($output, ['Exported By', $_SESSION['username'] ?? 'Admin']);
    
    fclose($output);
    exit;
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Export failed: ' . $e->getMessage()]);
    exit;
}
?>
