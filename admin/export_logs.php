<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

$type = $_GET['type'] ?? 'system';
$date_range = $_GET['date_range'] ?? 'month';
$log_level = $_GET['log_level'] ?? '';
$user_filter = $_GET['user_filter'] ?? '';
$ip_filter = $_GET['ip_filter'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';
$export_format = $_GET['export'] ?? 'csv';

// Build date condition
$date_condition = '';
switch ($date_range) {
    case 'today':
        $date_condition = "AND DATE(created_at) = CURDATE()";
        break;
    case 'week':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        break;
    case 'month':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
    case 'all':
        $date_condition = ""; // No date restriction
        break;
    case 'custom':
        if (!empty($start_date) && !empty($end_date)) {
            $date_condition = "AND DATE(created_at) BETWEEN " . $pdo->quote($start_date) . " AND " . $pdo->quote($end_date);
        } elseif (!empty($start_date)) {
            $date_condition = "AND DATE(created_at) >= " . $pdo->quote($start_date);
        } elseif (!empty($end_date)) {
            $date_condition = "AND DATE(created_at) <= " . $pdo->quote($end_date);
        }
        break;
    default:
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
}

// Build additional conditions
$additional_conditions = '';

// Log level filter (works for different log types)
if (!empty($log_level)) {
    switch ($type) {
        case 'system':
            $additional_conditions .= " AND log_level = " . $pdo->quote($log_level);
            break;
        case 'security':
        case 'access':
        case 'file_operations':
        case 'suspicious':
            $additional_conditions .= " AND risk_level = " . $pdo->quote($log_level);
            break;
        case 'anomaly':
            $additional_conditions .= " AND severity = " . $pdo->quote($log_level);
            break;
    }
}

// User filter
if (!empty($user_filter)) {
    switch ($type) {
        case 'admin_actions':
            $additional_conditions .= " AND (admin_username LIKE " . $pdo->quote('%' . $user_filter . '%') .
                                     " OR target_username LIKE " . $pdo->quote('%' . $user_filter . '%') . ")";
            break;
        case 'login':
            $additional_conditions .= " AND email LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
        default:
            $additional_conditions .= " AND username LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
    }
}

// IP address filter
if (!empty($ip_filter)) {
    $additional_conditions .= " AND ip_address LIKE " . $pdo->quote('%' . $ip_filter . '%');
}

// Set headers for CSV download
$filename = $type . '_logs_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Excel compatibility)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

try {
    // Check if table exists before querying
    $table_exists = false;
    $query = '';
    $headers = [];

    switch ($type) {
        case 'system':
            $table_check = $pdo->query("SHOW TABLES LIKE 'system_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT log_level, category, message, details, ip_address, user_agent, created_at
                    FROM system_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Log Level', 'Category', 'Message', 'Details', 'IP Address', 'User Agent', 'Created At'];
            }
            break;

        case 'security':
            $table_check = $pdo->query("SHOW TABLES LIKE 'security_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT event_type, username, ip_address, country, city, user_agent, session_id, details, risk_level, created_at
                    FROM security_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Event Type', 'Username', 'IP Address', 'Country', 'City', 'User Agent', 'Session ID', 'Details', 'Risk Level', 'Created At'];
            }
            break;

        case 'audit':
            $table_check = $pdo->query("SHOW TABLES LIKE 'audit_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT action_type, table_name, record_id, username, old_values, new_values, ip_address, user_agent, created_at
                    FROM audit_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Action Type', 'Table Name', 'Record ID', 'Username', 'Old Values', 'New Values', 'IP Address', 'User Agent', 'Created At'];
            }
            break;

        case 'login':
            $table_check = $pdo->query("SHOW TABLES LIKE 'login_attempts'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT email, user_id, ip_address, country, city, user_agent, success, failure_reason, session_id, two_factor_used, remember_me, created_at
                    FROM login_attempts
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Email', 'User ID', 'IP Address', 'Country', 'City', 'User Agent', 'Success', 'Failure Reason', 'Session ID', '2FA Used', 'Remember Me', 'Created At'];
            }
            break;

        case 'anomaly':
            $table_check = $pdo->query("SHOW TABLES LIKE 'anomaly_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT anomaly_type, user_id, ip_address, severity, description, threshold_value, actual_value, data, resolved, resolved_by, resolved_at, created_at
                    FROM anomaly_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Anomaly Type', 'User ID', 'IP Address', 'Severity', 'Description', 'Threshold Value', 'Actual Value', 'Data', 'Resolved', 'Resolved By', 'Resolved At', 'Created At'];
            }
            break;

        case 'access':
            $table_check = $pdo->query("SHOW TABLES LIKE 'access_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT username, ip_address, request_method, request_uri, file_path, directory_path, access_type, response_code, response_size, referer, execution_time, blocked, block_reason, risk_level, details, created_at
                    FROM access_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Username', 'IP Address', 'Request Method', 'Request URI', 'File Path', 'Directory Path', 'Access Type', 'Response Code', 'Response Size', 'Referer', 'Execution Time', 'Blocked', 'Block Reason', 'Risk Level', 'Details', 'Created At'];
            }
            break;

        case 'file_operations':
            $table_check = $pdo->query("SHOW TABLES LIKE 'file_operations_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT username, ip_address, operation_type, file_path, file_name, file_size, file_type, old_file_path, new_file_path, success, error_message, risk_level, details, created_at
                    FROM file_operations_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Username', 'IP Address', 'Operation Type', 'File Path', 'File Name', 'File Size', 'File Type', 'Old File Path', 'New File Path', 'Success', 'Error Message', 'Risk Level', 'Details', 'Created At'];
            }
            break;

        case 'suspicious':
            $table_check = $pdo->query("SHOW TABLES LIKE 'suspicious_activity_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT username, ip_address, activity_type, severity, description, request_data, pattern_matched, auto_blocked, resolved, resolved_by, resolved_at, created_at
                    FROM suspicious_activity_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Username', 'IP Address', 'Activity Type', 'Severity', 'Description', 'Request Data', 'Pattern Matched', 'Auto Blocked', 'Resolved', 'Resolved By', 'Resolved At', 'Created At'];
            }
            break;

        case 'admin_actions':
            $table_check = $pdo->query("SHOW TABLES LIKE 'admin_actions_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT admin_username, action_type, action_description, target_user_id, target_username, old_values, new_values, ip_address, user_agent, success, error_message, created_at
                    FROM admin_actions_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Admin Username', 'Action Type', 'Action Description', 'Target User ID', 'Target Username', 'Old Values', 'New Values', 'IP Address', 'User Agent', 'Success', 'Error Message', 'Created At'];
            }
            break;

        case 'error':
            $table_check = $pdo->query("SHOW TABLES LIKE 'error_logs'");
            if ($table_check->rowCount() > 0) {
                $table_exists = true;
                $query = "
                    SELECT error_type, error_message, file_path, line_number, stack_trace, user_id, session_id, request_data, ip_address, user_agent, created_at
                    FROM error_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                ";
                $headers = ['Error Type', 'Error Message', 'File Path', 'Line Number', 'Stack Trace', 'User ID', 'Session ID', 'Request Data', 'IP Address', 'User Agent', 'Created At'];
            }
            break;

        default:
            fputcsv($output, ['Error: Invalid log type']);
            fclose($output);
            exit;
    }

    if (!$table_exists) {
        fputcsv($output, ['Error: Log table does not exist']);
        fputcsv($output, ['Log Type', $type]);
        fputcsv($output, ['Message', 'The requested log table has not been created yet.']);
        fclose($output);
        exit;
    }

    // Write headers
    fputcsv($output, $headers);

    // Execute query and write data
    $stmt = $pdo->prepare($query);
    $stmt->execute();

    $row_count = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // Process each field
        $csv_row = [];
        foreach ($row as $key => $value) {
            if (is_null($value)) {
                $csv_row[] = '';
            } elseif (in_array($key, ['details', 'old_values', 'new_values', 'stack_trace', 'request_data', 'data']) && !empty($value)) {
                // Format JSON fields for CSV
                $json_data = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $csv_row[] = json_encode($json_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                } else {
                    $csv_row[] = $value;
                }
            } elseif (in_array($key, ['success', 'blocked', 'resolved', 'auto_blocked', 'two_factor_used', 'remember_me'])) {
                // Format boolean fields
                $csv_row[] = $value ? 'Yes' : 'No';
            } elseif ($key === 'file_size' && is_numeric($value)) {
                // Format file sizes
                $csv_row[] = formatBytes($value);
            } elseif (in_array($key, ['execution_time']) && is_numeric($value)) {
                // Format execution time
                $csv_row[] = number_format($value, 4) . 's';
            } elseif (in_array($key, ['response_size']) && is_numeric($value)) {
                // Format response size
                $csv_row[] = formatBytes($value);
            } else {
                // Escape and clean the value
                $csv_row[] = cleanCsvValue($value);
            }
        }

        fputcsv($output, $csv_row);
        $row_count++;

        // Prevent memory issues with large exports
        if ($row_count % 1000 === 0) {
            flush();
        }
    }

    // Add summary information
    fputcsv($output, []); // Empty row
    fputcsv($output, ['EXPORT SUMMARY']);
    fputcsv($output, ['Log Type', ucfirst($type) . ' Logs']);
    fputcsv($output, ['Date Range', ucfirst($date_range)]);
    if ($date_range === 'custom') {
        if (!empty($start_date)) fputcsv($output, ['Start Date', $start_date]);
        if (!empty($end_date)) fputcsv($output, ['End Date', $end_date]);
    }
    fputcsv($output, ['Total Records', $row_count]);
    fputcsv($output, ['Export Date', date('Y-m-d H:i:s')]);
    fputcsv($output, ['Exported By', $_SESSION['username'] ?? 'Admin']);

    // Add filter information
    if (!empty($log_level)) {
        fputcsv($output, ['Log Level Filter', $log_level]);
    }
    if (!empty($user_filter)) {
        fputcsv($output, ['User Filter', $user_filter]);
    }
    if (!empty($ip_filter)) {
        fputcsv($output, ['IP Filter', $ip_filter]);
    }

    fclose($output);

} catch (PDOException $e) {
    // Clear any output and send error
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Log the error
    error_log("Export error for user " . ($_SESSION['username'] ?? 'unknown') . ": " . $e->getMessage());

    header('Content-Type: text/plain');
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Database Error: Unable to export logs. Please try again or contact support.';
} catch (Exception $e) {
    // Clear any output and send error
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Log the error
    error_log("Export error for user " . ($_SESSION['username'] ?? 'unknown') . ": " . $e->getMessage());

    header('Content-Type: text/plain');
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Export Error: ' . $e->getMessage();
}

/**
 * Format bytes into human readable format
 */
function formatBytes($bytes, $precision = 2) {
    if (!is_numeric($bytes) || $bytes < 0) {
        return 'N/A';
    }

    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Clean CSV values to prevent injection and formatting issues
 */
function cleanCsvValue($value) {
    if (is_null($value)) {
        return '';
    }

    // Convert to string
    $value = (string) $value;

    // Remove or escape potentially dangerous characters
    $value = str_replace(["\r\n", "\r", "\n"], ' ', $value);

    // Trim whitespace
    $value = trim($value);

    // Limit length to prevent extremely large cells
    if (strlen($value) > 32767) { // Excel cell limit
        $value = substr($value, 0, 32764) . '...';
    }

    return $value;
}
?>
