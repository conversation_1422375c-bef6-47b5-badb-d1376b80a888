<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

try {
    // Get all certificates with user and category info
    $sql = "
        SELECT
            uc.id,
            uc.certificate_code,
            uc.issued_at,
            u.username,
            u.email,
            cat.name as category_name,
            cat.icon as category_icon
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories cat ON uc.category_id = cat.id
        ORDER BY uc.issued_at DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $certificates = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Set headers for CSV download
    $filename = 'certificates_export_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');

    // Create output stream
    $output = fopen('php://output', 'w');

    // Add BOM for UTF-8 (helps with Excel compatibility)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // CSV Headers
    $headers = [
        'Certificate ID',
        'Certificate Code',
        'Username',
        'Email',
        'Category',
        'Issue Date',
        'Issue Time',
        'Verification URL'
    ];

    fputcsv($output, $headers);

    // Add certificate data
    foreach ($certificates as $cert) {
        $issue_date = new DateTime($cert['issued_at']);
        $verification_url = 'http://' . $_SERVER['HTTP_HOST'] . '/vuln-platform-beta/utils/verify_certificate.php?code=' . urlencode($cert['certificate_code']);

        $row = [
            $cert['id'],
            $cert['certificate_code'],
            $cert['username'],
            $cert['email'],
            $cert['category_name'],
            $issue_date->format('Y-m-d'),
            $issue_date->format('H:i:s'),
            $verification_url
        ];

        fputcsv($output, $row);
    }

    // Add summary row
    fputcsv($output, []); // Empty row
    fputcsv($output, ['SUMMARY']);
    fputcsv($output, ['Total Certificates', count($certificates)]);

    // Get category breakdown
    $category_counts = [];
    foreach ($certificates as $cert) {
        $category = $cert['category_name'];
        $category_counts[$category] = ($category_counts[$category] ?? 0) + 1;
    }

    foreach ($category_counts as $category => $count) {
        fputcsv($output, [$category . ' Certificates', $count]);
    }

    // Get unique recipients
    $unique_recipients = count(array_unique(array_column($certificates, 'username')));
    fputcsv($output, ['Unique Recipients', $unique_recipients]);

    fputcsv($output, ['Export Date', date('Y-m-d H:i:s')]);
    fputcsv($output, ['Exported By', $_SESSION['username'] ?? 'Admin']);

    fclose($output);
    exit;

} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Export failed: ' . $e->getMessage()]);
    exit;
}
?>
