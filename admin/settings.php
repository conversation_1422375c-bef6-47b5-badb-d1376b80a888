<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'System Settings';
$page_subtitle = 'Configure platform settings and preferences';

// Handle settings updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_general':
                // Handle general settings update
                $success_message = "General settings updated successfully!";
                break;
                
            case 'update_security':
                // Handle security settings update
                $success_message = "Security settings updated successfully!";
                break;
                
            case 'update_email':
                // Handle email settings update
                $success_message = "Email settings updated successfully!";
                break;
                
            case 'backup_database':
                // Handle database backup
                $success_message = "Database backup initiated successfully!";
                break;
        }
    }
}

// Get system information
$system_info = [
    'php_version' => phpversion(),
    'mysql_version' => $pdo->query("SELECT VERSION()")->fetchColumn(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

// Get database statistics
$db_stats = [];
$tables = ['users', 'challenges', 'user_progress', 'user_certificates', 'user_badges', 'categories'];
foreach ($tables as $table) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
    $db_stats[$table] = $stmt->fetchColumn();
}

include 'includes/admin_header.php';
?>

<style>
    .settings-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
    }

    .settings-section {
        margin-bottom: 32px;
    }

    .settings-section:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--gray-200);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 6px;
    }

    .form-control, .form-select {
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        padding: 10px 12px;
        font-size: 14px;
        transition: var(--transition);
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: var(--gray-50);
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-200);
    }

    .info-label {
        font-weight: 500;
        color: var(--gray-700);
    }

    .info-value {
        font-weight: 600;
        color: var(--gray-900);
        font-family: 'Courier New', monospace;
        font-size: 13px;
    }

    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-online {
        background: rgba(5, 150, 105, 0.1);
        color: var(--success-color);
    }

    .status-warning {
        background: rgba(217, 119, 6, 0.1);
        color: var(--warning-color);
    }

    .status-error {
        background: rgba(220, 38, 38, 0.1);
        color: var(--danger-color);
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .btn-professional {
        padding: 10px 20px;
        border-radius: var(--border-radius);
        font-size: 14px;
        font-weight: 500;
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700);
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-professional:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-900);
    }

    .btn-professional.btn-primary {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-professional.btn-primary:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .btn-professional.btn-danger {
        background: var(--danger-color);
        border-color: var(--danger-color);
        color: white;
    }

    .btn-professional.btn-danger:hover {
        background: #b91c1c;
        border-color: #b91c1c;
    }

    .btn-professional.btn-warning {
        background: var(--warning-color);
        border-color: var(--warning-color);
        color: white;
    }

    .btn-professional.btn-warning:hover {
        background: #b45309;
        border-color: #b45309;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--gray-300);
        transition: var(--transition);
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: var(--transition);
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: var(--primary-color);
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }
</style>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= $success_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row g-4">
    <!-- Settings Forms -->
    <div class="col-lg-8">
        <!-- General Settings -->
        <div class="settings-card">
            <div class="settings-section">
                <h2 class="section-title">
                    <i class="fas fa-cog me-2"></i>
                    General Settings
                </h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_general">
                    
                    <div class="form-group">
                        <label class="form-label">Platform Name</label>
                        <input type="text" class="form-control" value="TryMeOut" name="platform_name">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Platform Description</label>
                        <textarea class="form-control" rows="3" name="platform_description">A comprehensive vulnerability web application platform for security students learning</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Default Language</label>
                                <select class="form-select" name="default_language">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Timezone</label>
                                <select class="form-select" name="timezone">
                                    <option value="UTC" selected>UTC</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Los_Angeles">Pacific Time</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label d-flex align-items-center justify-content-between">
                            Enable User Registration
                            <label class="switch">
                                <input type="checkbox" name="enable_registration" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label d-flex align-items-center justify-content-between">
                            Require Email Verification
                            <label class="switch">
                                <input type="checkbox" name="require_verification" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn-professional btn-primary">
                        <i class="fas fa-save"></i>
                        Save General Settings
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div class="settings-card">
            <div class="settings-section">
                <h2 class="section-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security Settings
                </h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_security">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Session Timeout (minutes)</label>
                                <input type="number" class="form-control" value="30" name="session_timeout" min="5" max="1440">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Max Login Attempts</label>
                                <input type="number" class="form-control" value="5" name="max_login_attempts" min="3" max="10">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Password Requirements</label>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center justify-content-between">
                                    Minimum 8 characters
                                    <label class="switch">
                                        <input type="checkbox" name="password_min_length" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center justify-content-between">
                                    Require uppercase
                                    <label class="switch">
                                        <input type="checkbox" name="password_uppercase" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center justify-content-between">
                                    Require numbers
                                    <label class="switch">
                                        <input type="checkbox" name="password_numbers" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center justify-content-between">
                                    Require special characters
                                    <label class="switch">
                                        <input type="checkbox" name="password_special" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-professional btn-primary">
                        <i class="fas fa-save"></i>
                        Save Security Settings
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Email Settings -->
        <div class="settings-card">
            <div class="settings-section">
                <h2 class="section-title">
                    <i class="fas fa-envelope me-2"></i>
                    Email Settings
                </h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_email">
                    
                    <div class="form-group">
                        <label class="form-label">SMTP Host</label>
                        <input type="text" class="form-control" placeholder="smtp.gmail.com" name="smtp_host">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" value="587" name="smtp_port">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Encryption</label>
                                <select class="form-select" name="smtp_encryption">
                                    <option value="tls" selected>TLS</option>
                                    <option value="ssl">SSL</option>
                                    <option value="none">None</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">SMTP Username</label>
                                <input type="email" class="form-control" placeholder="<EMAIL>" name="smtp_username">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">SMTP Password</label>
                                <input type="password" class="form-control" placeholder="••••••••" name="smtp_password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button type="submit" class="btn-professional btn-primary">
                            <i class="fas fa-save"></i>
                            Save Email Settings
                        </button>
                        <button type="button" class="btn-professional" onclick="testEmail()">
                            <i class="fas fa-paper-plane"></i>
                            Test Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-lg-4">
        <!-- System Status -->
        <div class="settings-card">
            <h2 class="section-title">
                <i class="fas fa-server me-2"></i>
                System Status
            </h2>
            
            <div class="mb-3">
                <div class="status-indicator status-online">
                    <i class="fas fa-circle"></i>
                    All Systems Operational
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">PHP Version</span>
                    <span class="info-value"><?= $system_info['php_version'] ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">MySQL Version</span>
                    <span class="info-value"><?= $system_info['mysql_version'] ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Memory Limit</span>
                    <span class="info-value"><?= $system_info['memory_limit'] ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Upload Limit</span>
                    <span class="info-value"><?= $system_info['upload_max_filesize'] ?></span>
                </div>
            </div>
        </div>
        
        <!-- Database Statistics -->
        <div class="settings-card">
            <h2 class="section-title">
                <i class="fas fa-database me-2"></i>
                Database Statistics
            </h2>
            
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Users</span>
                    <span class="info-value"><?= number_format($db_stats['users']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Challenges</span>
                    <span class="info-value"><?= number_format($db_stats['challenges']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Progress Records</span>
                    <span class="info-value"><?= number_format($db_stats['user_progress']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Certificates</span>
                    <span class="info-value"><?= number_format($db_stats['user_certificates']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Badges</span>
                    <span class="info-value"><?= number_format($db_stats['user_badges']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Categories</span>
                    <span class="info-value"><?= number_format($db_stats['categories']) ?></span>
                </div>
            </div>
        </div>
        
        <!-- System Actions -->
        <div class="settings-card">
            <h2 class="section-title">
                <i class="fas fa-tools me-2"></i>
                System Actions
            </h2>
            
            <div class="d-grid gap-2">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="backup_database">
                    <button type="submit" class="btn-professional btn-warning w-100">
                        <i class="fas fa-download"></i>
                        Backup Database
                    </button>
                </form>
                
                <button class="btn-professional w-100" onclick="clearCache()">
                    <i class="fas fa-broom"></i>
                    Clear Cache
                </button>
                
                <button class="btn-professional w-100" onclick="optimizeDatabase()">
                    <i class="fas fa-database"></i>
                    Optimize Database
                </button>
                
                <button class="btn-professional btn-danger w-100" onclick="systemMaintenance()">
                    <i class="fas fa-exclamation-triangle"></i>
                    Maintenance Mode
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function testEmail() {
    showNotification('Sending test email...', 'info');
    // Simulate email test
    setTimeout(() => {
        showNotification('Test email sent successfully!', 'success');
    }, 2000);
}

function clearCache() {
    showNotification('Cache cleared successfully!', 'success');
}

function optimizeDatabase() {
    showNotification('Database optimization completed!', 'success');
}

function systemMaintenance() {
    if (confirm('Are you sure you want to enable maintenance mode? This will make the platform unavailable to users.')) {
        showNotification('Maintenance mode feature coming soon!', 'info');
    }
}
</script>

<?php include 'includes/admin_footer.php'; ?>
