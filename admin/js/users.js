// Users page JavaScript functionality

// Search functionality with debouncing
let searchTimeout;
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchValue = this.value.trim();
                const currentSearch = new URLSearchParams(window.location.search).get('search') || '';
                if (searchValue !== currentSearch) {
                    window.location.href = '?search=' + encodeURIComponent(searchValue);
                }
            }, 500);
        });
    }
});

// Show user details in modal
function showUserDetails(userId, username, email, role, createdAt, isVerified, profilePicture, totalCompletions, totalBadges, totalCertificates, lastActivity) {
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    const content = document.getElementById('userDetailsContent');
    
    // Determine profile picture path
    const profilePicPath = profilePicture && profilePicture !== 'assets/images/default.jpg' 
        ? '../' + profilePicture 
        : '../assets/images/default.jpg';
    
    const hasCustomPicture = profilePicture && profilePicture !== 'assets/images/default.jpg';
    
    // Format last activity
    const lastActivityFormatted = lastActivity ?
        new Date(lastActivity).toLocaleDateString() + ' at ' + new Date(lastActivity).toLocaleTimeString() :
        'No activity recorded';

    content.innerHTML =
        '<div class="row">' +
            '<div class="col-md-4 text-center">' +
                '<div class="position-relative d-inline-block mb-3">' +
                    '<img src="' + profilePicPath + '"' +
                         ' alt="' + username + '"' +
                         ' class="user-avatar-large rounded-circle border"' +
                         ' style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #e9ecef !important;"' +
                         ' onerror="this.src=\'../assets/images/default.jpg\'">' +
                    (hasCustomPicture ? '<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning" title="Custom Profile Picture"><i class="fas fa-image" style="font-size: 10px;"></i></span>' : '') +
                '</div>' +
                '<h5 class="mb-2">' + username + '</h5>' +
                '<span class="badge badge-professional ' + (role === 'admin' ? 'badge-danger' : 'badge-info') + ' mb-2">' +
                    '<i class="fas fa-' + (role === 'admin' ? 'shield-alt' : 'user-graduate') + ' me-1"></i>' +
                    role.toUpperCase() +
                '</span>' +
                '<div class="mt-2">' +
                    (isVerified ?
                        '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Verified</span>' :
                        '<span class="badge bg-warning"><i class="fas fa-clock me-1"></i>Pending</span>'
                    ) +
                '</div>' +
            '</div>' +
            '<div class="col-md-8">' +
                '<div class="row mb-4">' +
                    '<div class="col-12">' +
                        '<h6 class="text-muted mb-3"><i class="fas fa-info-circle me-2"></i>Account Information</h6>' +
                        '<table class="table table-borderless table-sm">' +
                            '<tr>' +
                                '<td style="width: 30%;"><strong>User ID:</strong></td>' +
                                '<td>#' + userId + '</td>' +
                            '</tr>' +
                            '<tr>' +
                                '<td><strong>Email:</strong></td>' +
                                '<td>' + email + '</td>' +
                            '</tr>' +
                            '<tr>' +
                                '<td><strong>Joined:</strong></td>' +
                                '<td>' + new Date(createdAt).toLocaleDateString() + ' at ' + new Date(createdAt).toLocaleTimeString() + '</td>' +
                            '</tr>' +
                            '<tr>' +
                                '<td><strong>Last Activity:</strong></td>' +
                                '<td>' + lastActivityFormatted + '</td>' +
                            '</tr>' +
                            '<tr>' +
                                '<td><strong>Profile Picture:</strong></td>' +
                                '<td>' + (hasCustomPicture ?
                                    '<span class="text-warning"><i class="fas fa-image me-1"></i>Custom Picture</span>' :
                                    '<span class="text-muted"><i class="fas fa-user-circle me-1"></i>Default Picture</span>'
                                ) + '</td>' +
                            '</tr>' +
                        '</table>' +
                    '</div>' +
                '</div>' +
                '<div class="row">' +
                    '<div class="col-12">' +
                        '<h6 class="text-muted mb-3"><i class="fas fa-chart-bar me-2"></i>Progress Statistics</h6>' +
                        '<div class="row text-center">' +
                            '<div class="col-4">' +
                                '<div class="card border-0 bg-light">' +
                                    '<div class="card-body py-2">' +
                                        '<div class="h4 mb-0 text-primary">' + totalCompletions + '</div>' +
                                        '<small class="text-muted">Challenges</small>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                            '<div class="col-4">' +
                                '<div class="card border-0 bg-light">' +
                                    '<div class="card-body py-2">' +
                                        '<div class="h4 mb-0 text-warning">' + totalBadges + '</div>' +
                                        '<small class="text-muted">Badges</small>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                            '<div class="col-4">' +
                                '<div class="card border-0 bg-light">' +
                                    '<div class="card-body py-2">' +
                                        '<div class="h4 mb-0 text-success">' + totalCertificates + '</div>' +
                                        '<small class="text-muted">Certificates</small>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>';

    modal.show();
}

// Edit user function
function editUser(userId, username, email, role) {
    // Populate edit form
    document.getElementById('edit_user_id').value = userId;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_email').value = email;
    document.getElementById('edit_role').value = role;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
    modal.show();
}

// Change role function
function changeRole(userId, currentRole) {
    // Populate role change form
    document.getElementById('role_user_id').value = userId;
    document.getElementById('new_role').value = currentRole === 'admin' ? 'student' : 'admin';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('changeRoleModal'));
    modal.show();
}

// Print user details function
function printUserDetails() {
    const content = document.getElementById('userDetailsContent').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(
        '<!DOCTYPE html>' +
        '<html>' +
        '<head>' +
            '<title>User Details - TryMeOut Admin</title>' +
            '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">' +
            '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">' +
            '<style>' +
                'body { font-family: Arial, sans-serif; }' +
                '.badge-professional { padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500; }' +
                '.badge-danger { background: rgba(220, 38, 38, 0.1); color: #dc2626; }' +
                '.badge-info { background: rgba(8, 145, 178, 0.1); color: #0891b2; }' +
                '@media print {' +
                    '.no-print { display: none; }' +
                    'body { -webkit-print-color-adjust: exact; }' +
                '}' +
            '</style>' +
        '</head>' +
        '<body class="p-4">' +
            '<div class="text-center mb-4">' +
                '<h2>TryMeOut - User Details Report</h2>' +
                '<p class="text-muted">Generated on ' + new Date().toLocaleDateString() + ' at ' + new Date().toLocaleTimeString() + '</p>' +
            '</div>' +
            content +
            '<script>' +
                'window.onload = function() {' +
                    'window.print();' +
                    'window.onafterprint = function() {' +
                        'window.close();' +
                    '}' +
                '}' +
            '</script>' +
        '</body>' +
        '</html>'
    );

    printWindow.document.close();
}

// Export users function
function exportUsers(format = 'csv') {
    // Show loading notification
    const formatName = format.toUpperCase();
    showNotification('Preparing ' + formatName + ' export...', 'info');

    // Determine the export URL and file extension
    let exportUrl, fileExtension, successMessage;

    if (format === 'pdf') {
        exportUrl = 'export_users_pdf.php?download=true';
        fileExtension = 'pdf';
        successMessage = 'PDF export opened in new tab. The print dialog will appear automatically to save as PDF.';

        // For PDF, open in new tab for print dialog
        const newWindow = window.open(exportUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        if (newWindow) {
            setTimeout(() => {
                showNotification(successMessage, 'success');
            }, 1000);
        } else {
            showNotification('Please allow popups to export PDF. Check your browser\'s popup blocker.', 'warning');
        }
    } else {
        // CSV export
        exportUrl = 'export_users.php';
        fileExtension = 'csv';
        successMessage = 'CSV export started! Check your downloads folder.';

        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = 'users_export_' + new Date().toISOString().split('T')[0] + '.' + fileExtension;
        link.style.display = 'none';

        // Add to DOM, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            showNotification(successMessage, 'success');
        }, 1000);
    }
}

// Notification function (if not already defined)
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
