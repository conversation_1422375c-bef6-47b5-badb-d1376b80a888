<?php
session_start();
require '../config/db_connect.php';
require '../config/logger.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $anomaly_id = $input['anomaly_id'] ?? null;
    
    if (!$anomaly_id) {
        echo json_encode(['success' => false, 'message' => 'Anomaly ID required']);
        exit;
    }
    
    try {
        // Update anomaly status to resolved
        $stmt = $pdo->prepare("UPDATE anomaly_logs SET resolved = TRUE WHERE id = ?");
        $stmt->execute([$anomaly_id]);
        
        if ($stmt->rowCount() > 0) {
            // Log the resolution action
            $logger = getLogger();
            $logger->logSecurityEvent('ANOMALY_RESOLVED', $_SESSION['username'], $_SESSION['user_id'], 'LOW', [
                'anomaly_id' => $anomaly_id,
                'resolved_by' => $_SESSION['username']
            ]);
            
            echo json_encode(['success' => true, 'message' => 'Anomaly resolved successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Anomaly not found']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
