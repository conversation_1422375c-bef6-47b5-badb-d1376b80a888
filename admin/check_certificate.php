<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Validate input
if (!isset($_POST['user_id']) || !isset($_POST['category_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

$user_id = (int)$_POST['user_id'];
$category_id = (int)$_POST['category_id'];

if ($user_id <= 0 || $category_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid parameters']);
    exit;
}

try {
    // Check if certificate already exists
    $stmt = $pdo->prepare("
        SELECT id, certificate_code, issued_at 
        FROM user_certificates 
        WHERE user_id = ? AND category_id = ?
    ");
    $stmt->execute([$user_id, $category_id]);
    $existing_cert = $stmt->fetch();

    // Get user progress for this category
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_challenges,
            COUNT(CASE WHEN up.status = 'completed' THEN 1 END) as completed_challenges,
            COUNT(CASE WHEN up.status = 'completed' AND up.completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_completions
        FROM challenges c
        LEFT JOIN user_progress up ON c.id = up.challenge_id AND up.user_id = ?
        WHERE c.category_id = ?
    ");
    $stmt->execute([$user_id, $category_id]);
    $progress = $stmt->fetch();

    // Get category name for context
    $stmt = $pdo->prepare("SELECT name FROM categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();

    // Get user name for context
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    $response = [
        'exists' => (bool)$existing_cert,
        'progress' => [
            'completed' => (int)$progress['completed_challenges'],
            'total' => (int)$progress['total_challenges'],
            'recent' => (int)$progress['recent_completions']
        ],
        'category_name' => $category['name'] ?? 'Unknown',
        'username' => $user['username'] ?? 'Unknown'
    ];

    if ($existing_cert) {
        $response['existing_certificate'] = [
            'id' => $existing_cert['id'],
            'code' => $existing_cert['certificate_code'],
            'issued_at' => $existing_cert['issued_at']
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);

} catch (PDOException $e) {
    error_log("Database error in check_certificate.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("Error in check_certificate.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'An error occurred']);
}
?>
