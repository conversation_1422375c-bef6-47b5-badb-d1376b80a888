<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

// Force download headers
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="certificates_export_' . date('Y-m-d_H-i-s') . '.csv"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Start output
echo "Certificate ID,Certificate Code,Username,Email,Category,Issue Date,Issue Time\n";

try {
    // Get certificates
    $sql = "
        SELECT 
            uc.id,
            uc.certificate_code,
            uc.issued_at,
            u.username,
            u.email,
            cat.name as category_name
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories cat ON uc.category_id = cat.id
        ORDER BY uc.issued_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    while ($cert = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $issue_date = new DateTime($cert['issued_at']);
        
        // Escape CSV values
        $row = [
            $cert['id'],
            '"' . str_replace('"', '""', $cert['certificate_code']) . '"',
            '"' . str_replace('"', '""', $cert['username']) . '"',
            '"' . str_replace('"', '""', $cert['email']) . '"',
            '"' . str_replace('"', '""', $cert['category_name']) . '"',
            $issue_date->format('Y-m-d'),
            $issue_date->format('H:i:s')
        ];
        
        echo implode(',', $row) . "\n";
    }
    
    // Add summary
    echo "\n";
    echo "SUMMARY\n";
    echo "Export Date," . date('Y-m-d H:i:s') . "\n";
    echo "Exported By," . ($_SESSION['username'] ?? 'Admin') . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

exit;
?>
