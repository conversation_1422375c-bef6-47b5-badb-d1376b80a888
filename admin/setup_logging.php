<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

$setup_complete = false;
$setup_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_logging'])) {
    try {
        // Fix users table - add last_login column if missing
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("DESCRIBE users");
            $existing_columns = [];
            while ($row = $stmt->fetch()) {
                $existing_columns[] = $row['Field'];
            }

            if (!in_array('last_login', $existing_columns)) {
                try {
                    $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL");
                    $setup_messages[] = "✅ Added missing 'last_login' column to users table";
                } catch (PDOException $e) {
                    $setup_messages[] = "⚠️ Could not add last_login column: " . $e->getMessage();
                }
            } else {
                $setup_messages[] = "✅ Column 'last_login' already exists in users table";
            }
        }

        // Check if security_logs table exists and update it
        $stmt = $pdo->query("SHOW TABLES LIKE 'security_logs'");
        if ($stmt->rowCount() > 0) {
            // Table exists, check and add missing columns
            $columns_to_add = [
                'country' => 'VARCHAR(100)',
                'city' => 'VARCHAR(100)',
                'user_agent' => 'TEXT',
                'session_id' => 'VARCHAR(255)',
                'risk_level' => "ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW'",
                'details' => 'JSON'
            ];

            foreach ($columns_to_add as $column => $definition) {
                try {
                    $pdo->exec("ALTER TABLE security_logs ADD COLUMN $column $definition");
                    $setup_messages[] = "✅ Added column '$column' to security_logs table";
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                        $setup_messages[] = "✅ Column '$column' already exists in security_logs";
                    } else {
                        $setup_messages[] = "⚠️ Could not add column '$column': " . $e->getMessage();
                    }
                }
            }

            // Add indexes if they don't exist
            $indexes_to_add = [
                'idx_risk_level' => 'risk_level',
                'idx_event_type' => 'event_type',
                'idx_ip_address' => 'ip_address',
                'idx_created_at' => 'created_at'
            ];

            foreach ($indexes_to_add as $index_name => $column) {
                try {
                    $pdo->exec("ALTER TABLE security_logs ADD INDEX $index_name ($column)");
                    $setup_messages[] = "✅ Added index '$index_name' to security_logs table";
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                        $setup_messages[] = "✅ Index '$index_name' already exists in security_logs";
                    }
                }
            }

        } else {
            // Create new security_logs table
            $pdo->exec("
                CREATE TABLE security_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_type VARCHAR(50) NOT NULL,
                    username VARCHAR(100),
                    user_id INT,
                    ip_address VARCHAR(45) NOT NULL,
                    country VARCHAR(100),
                    city VARCHAR(100),
                    user_agent TEXT,
                    session_id VARCHAR(255),
                    risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
                    details JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_event_type (event_type),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_created_at (created_at),
                    INDEX idx_risk_level (risk_level)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            $setup_messages[] = "✅ security_logs table created successfully";
        }

        // Create login_attempts table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                country VARCHAR(100),
                city VARCHAR(100),
                user_agent TEXT,
                success BOOLEAN DEFAULT FALSE,
                failure_reason VARCHAR(255),
                session_duration INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_ip_address (ip_address),
                INDEX idx_created_at (created_at),
                INDEX idx_success (success)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setup_messages[] = "✅ login_attempts table created successfully";

        // Create anomaly_logs table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS anomaly_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                anomaly_type VARCHAR(50) NOT NULL,
                user_id INT,
                ip_address VARCHAR(45),
                severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                description TEXT,
                data JSON,
                resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_anomaly_type (anomaly_type),
                INDEX idx_severity (severity),
                INDEX idx_resolved (resolved),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setup_messages[] = "✅ anomaly_logs table created successfully";

        // Check and create other tables if needed
        $tables_to_check = [
            'system_logs' => "
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    category VARCHAR(50) NOT NULL,
                    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                    message TEXT NOT NULL,
                    ip_address VARCHAR(45),
                    details JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_category (category),
                    INDEX idx_log_level (log_level),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            'audit_logs' => "
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    action VARCHAR(100) NOT NULL,
                    username VARCHAR(100),
                    user_id INT,
                    resource_type VARCHAR(50),
                    resource_id INT,
                    old_values JSON,
                    new_values JSON,
                    result ENUM('SUCCESS', 'FAILURE') DEFAULT 'SUCCESS',
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_action (action),
                    INDEX idx_username (username),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            'application_logs' => "
                CREATE TABLE IF NOT EXISTS application_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    component VARCHAR(50) NOT NULL,
                    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                    message TEXT NOT NULL,
                    username VARCHAR(100),
                    user_id INT,
                    context JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_component (component),
                    INDEX idx_log_level (log_level),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            'error_logs' => "
                CREATE TABLE IF NOT EXISTS error_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    error_type VARCHAR(50) NOT NULL,
                    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                    error_message TEXT NOT NULL,
                    file_path VARCHAR(255),
                    line_number INT,
                    stack_trace TEXT,
                    user_id INT,
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_error_type (error_type),
                    INDEX idx_severity (severity),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];

        foreach ($tables_to_check as $table_name => $create_sql) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table_name'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($create_sql);
                $setup_messages[] = "✅ $table_name table created successfully";
            } else {
                $setup_messages[] = "✅ $table_name table already exists";
            }
        }

        // Insert sample data
        $pdo->exec("
            INSERT IGNORE INTO security_logs (event_type, username, user_id, ip_address, country, city, risk_level, details) 
            VALUES ('LOGGING_SYSTEM_SETUP', '{$_SESSION['username']}', {$_SESSION['user_id']}, '127.0.0.1', 'Local', 'Localhost', 'LOW', '{\"action\": \"enhanced_logging_setup\", \"admin\": \"{$_SESSION['username']}\"}')
        ");

        $pdo->exec("
            INSERT IGNORE INTO system_logs (category, log_level, message, ip_address) 
            VALUES ('SECURITY', 'INFO', 'Enhanced logging system initialized by admin: {$_SESSION['username']}', '127.0.0.1')
        ");

        $setup_messages[] = "✅ Sample data inserted successfully";
        $setup_complete = true;

    } catch (PDOException $e) {
        $setup_messages[] = "❌ Error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Enhanced Logging System | Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .setup-container { max-width: 800px; margin: 50px auto; }
        .setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .setup-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px 15px 0 0; }
        .message-item { padding: 10px; margin: 5px 0; border-radius: 5px; font-family: monospace; }
        .message-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn-setup { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 15px 30px; font-weight: 600; }
        .btn-setup:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4); }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-card">
                <div class="setup-header text-center">
                    <h1><i class="fas fa-database me-3"></i>Enhanced Logging System Setup</h1>
                    <p class="mb-0">Initialize the comprehensive security logging system</p>
                </div>
                
                <div class="card-body p-4">
                    <?php if (!$setup_complete): ?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Setup Required</h5>
                            <p>The enhanced logging system requires additional database tables to be created. This setup will create:</p>
                            <ul>
                                <li><strong>security_logs</strong> - Enhanced security events with geolocation</li>
                                <li><strong>login_attempts</strong> - Detailed login tracking with IP/location</li>
                                <li><strong>anomaly_logs</strong> - Automated anomaly detection and tracking</li>
                                <li><strong>system_logs</strong> - System events and activities</li>
                                <li><strong>audit_logs</strong> - Administrative actions and changes</li>
                                <li><strong>application_logs</strong> - Application-specific events</li>
                                <li><strong>error_logs</strong> - Error tracking and debugging</li>
                            </ul>
                            <p class="mb-0"><strong>Note:</strong> This is a one-time setup and is safe to run multiple times.</p>
                        </div>
                        
                        <form method="POST" class="text-center">
                            <button type="submit" name="setup_logging" class="btn btn-success btn-setup btn-lg">
                                <i class="fas fa-rocket me-2"></i>Setup Enhanced Logging System
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>Setup Complete!</h5>
                            <p class="mb-0">The enhanced logging system has been successfully initialized.</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="logs.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-chart-line me-2"></i>View Logs Dashboard
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($setup_messages)): ?>
                        <div class="mt-4">
                            <h5>Setup Messages:</h5>
                            <div class="setup-messages">
                                <?php foreach ($setup_messages as $message): ?>
                                    <div class="message-item <?= strpos($message, '✅') !== false ? 'message-success' : 'message-error' ?>">
                                        <?= htmlspecialchars($message) ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
