<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

$format = $_GET['format'] ?? 'csv';

// Create timestamp for filename
$timestamp = date('Y-m-d_H-i-s');

try {
    // Get user data with challenge statistics
    $stmt = $pdo->query("
        SELECT
            u.id,
            u.username,
            u.email,
            u.role,
            u.created_at,
            COUNT(DISTINCT up.challenge_id) as completed_challenges,
            MAX(up.completed_at) as last_activity,
            COUNT(DISTINCT ub.badge_id) as badges_earned
        FROM users u
        LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        LEFT JOIN user_badges ub ON u.id = ub.user_id
        GROUP BY u.id, u.username, u.email, u.role, u.created_at
        ORDER BY u.id ASC
    ");
    $users = $stmt->fetchAll();

    if (empty($users)) {
        header('HTTP/1.1 404 Not Found');
        exit('No users found');
    }

    // Set headers based on format
    switch ($format) {
        case 'csv':
            header('Content-Type: text/csv; charset=utf-8');
            header("Content-Disposition: attachment; filename=\"users_export_{$timestamp}.csv\"");
            header('Cache-Control: max-age=0');
            exportCSV($users);
            break;

        case 'excel':
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header("Content-Disposition: attachment; filename=\"users_export_{$timestamp}.xls\"");
            header('Cache-Control: max-age=0');
            exportExcel($users);
            break;

        case 'pdf':
            header('Content-Type: text/html; charset=utf-8');
            header("Content-Disposition: attachment; filename=\"users_export_{$timestamp}.html\"");
            header('Cache-Control: max-age=0');
            exportPDF($users);
            break;

        case 'word':
            header('Content-Type: application/msword; charset=utf-8');
            header("Content-Disposition: attachment; filename=\"users_export_{$timestamp}.doc\"");
            header('Cache-Control: max-age=0');
            exportWord($users);
            break;

        default:
            header('HTTP/1.1 400 Bad Request');
            exit('Invalid format. Supported formats: csv, excel, pdf, word');
    }

} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    exit('Export failed: ' . $e->getMessage());
}

function exportCSV($users) {
    $output = fopen('php://output', 'w');

    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Headers
    fputcsv($output, [
        'ID',
        'Username',
        'Email',
        'Role',
        'Completed Challenges',
        'Badges Earned',
        'Registration Date',
        'Last Activity',
        'Status'
    ]);

    // Data rows
    foreach ($users as $user) {
        fputcsv($output, [
            $user['id'],
            $user['username'],
            $user['email'],
            ucfirst($user['role']),
            $user['completed_challenges'],
            $user['badges_earned'],
            date('Y-m-d H:i:s', strtotime($user['created_at'])),
            $user['last_activity'] ? date('Y-m-d H:i:s', strtotime($user['last_activity'])) : 'Never',
            $user['completed_challenges'] > 0 ? 'Active' : 'Inactive'
        ]);
    }

    fclose($output);
}

function exportExcel($users) {
    // Simple Excel XML format (compatible with Excel 2003+)
    echo '<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>User Export</Title>
  <Created>' . date('Y-m-d\TH:i:s\Z') . '</Created>
 </DocumentProperties>
 <Worksheet ss:Name="Users">
  <Table>
   <Row>
    <Cell><Data ss:Type="String">ID</Data></Cell>
    <Cell><Data ss:Type="String">Username</Data></Cell>
    <Cell><Data ss:Type="String">Email</Data></Cell>
    <Cell><Data ss:Type="String">Role</Data></Cell>
    <Cell><Data ss:Type="String">Completed Challenges</Data></Cell>
    <Cell><Data ss:Type="String">Badges Earned</Data></Cell>
    <Cell><Data ss:Type="String">Registration Date</Data></Cell>
    <Cell><Data ss:Type="String">Last Activity</Data></Cell>
    <Cell><Data ss:Type="String">Status</Data></Cell>
   </Row>';

    foreach ($users as $user) {
        echo '<Row>
         <Cell><Data ss:Type="Number">' . $user['id'] . '</Data></Cell>
         <Cell><Data ss:Type="String">' . htmlspecialchars($user['username']) . '</Data></Cell>
         <Cell><Data ss:Type="String">' . htmlspecialchars($user['email']) . '</Data></Cell>
         <Cell><Data ss:Type="String">' . ucfirst($user['role']) . '</Data></Cell>
         <Cell><Data ss:Type="Number">' . $user['completed_challenges'] . '</Data></Cell>
         <Cell><Data ss:Type="Number">' . $user['badges_earned'] . '</Data></Cell>
         <Cell><Data ss:Type="String">' . date('Y-m-d H:i:s', strtotime($user['created_at'])) . '</Data></Cell>
         <Cell><Data ss:Type="String">' . ($user['last_activity'] ? date('Y-m-d H:i:s', strtotime($user['last_activity'])) : 'Never') . '</Data></Cell>
         <Cell><Data ss:Type="String">' . ($user['completed_challenges'] > 0 ? 'Active' : 'Inactive') . '</Data></Cell>
        </Row>';
    }

    echo '  </Table>
 </Worksheet>
</Workbook>';
}

function exportPDF($users) {
    // Simple HTML to PDF conversion (basic implementation)
    // In production, you'd use a library like TCPDF or DOMPDF

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>User Export</title>
    <style>
        body { font-family: Arial, sans-serif; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .header { text-align: center; margin-bottom: 20px; }
        .footer { margin-top: 20px; text-align: center; font-size: 10px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VulnPlatform - User Export Report</h1>
        <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>
        <p>Total Users: ' . count($users) . '</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Challenges</th>
                <th>Badges</th>
                <th>Registered</th>
                <th>Last Activity</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($users as $user) {
        $html .= '<tr>
            <td>' . $user['id'] . '</td>
            <td>' . htmlspecialchars($user['username']) . '</td>
            <td>' . htmlspecialchars($user['email']) . '</td>
            <td>' . ucfirst($user['role']) . '</td>
            <td>' . $user['completed_challenges'] . '</td>
            <td>' . $user['badges_earned'] . '</td>
            <td>' . date('Y-m-d', strtotime($user['created_at'])) . '</td>
            <td>' . ($user['last_activity'] ? date('Y-m-d', strtotime($user['last_activity'])) : 'Never') . '</td>
            <td>' . ($user['completed_challenges'] > 0 ? 'Active' : 'Inactive') . '</td>
        </tr>';
    }

    $html .= '</tbody>
    </table>

    <div class="footer">
        <p>VulnPlatform Admin Dashboard - Confidential Report</p>
    </div>
</body>
</html>';

    // For a basic implementation, we'll output HTML
    // In production, use a proper PDF library
    echo $html;
}

function exportWord($users) {
    // Simple Word document using HTML format
    echo '<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office"
      xmlns:w="urn:schemas-microsoft-com:office:word"
      xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <title>User Export</title>
    <style>
        body { font-family: Arial, sans-serif; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #000; padding: 8px; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .header { text-align: center; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VulnPlatform - User Export Report</h1>
        <p><strong>Generated:</strong> ' . date('F j, Y \a\t g:i A') . '</p>
        <p><strong>Total Users:</strong> ' . count($users) . '</p>
    </div>

    <table>
        <tr>
            <th>ID</th>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Completed Challenges</th>
            <th>Badges Earned</th>
            <th>Registration Date</th>
            <th>Last Activity</th>
            <th>Status</th>
        </tr>';

    foreach ($users as $user) {
        echo '<tr>
            <td>' . $user['id'] . '</td>
            <td>' . htmlspecialchars($user['username']) . '</td>
            <td>' . htmlspecialchars($user['email']) . '</td>
            <td>' . ucfirst($user['role']) . '</td>
            <td>' . $user['completed_challenges'] . '</td>
            <td>' . $user['badges_earned'] . '</td>
            <td>' . date('M j, Y', strtotime($user['created_at'])) . '</td>
            <td>' . ($user['last_activity'] ? date('M j, Y', strtotime($user['last_activity'])) : 'Never') . '</td>
            <td>' . ($user['completed_challenges'] > 0 ? 'Active' : 'Inactive') . '</td>
        </tr>';
    }

    echo '</table>

    <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
        <p>VulnPlatform Admin Dashboard - Confidential Report</p>
        <p>This document contains sensitive user information and should be handled accordingly.</p>
    </div>
</body>
</html>';
}
?>
