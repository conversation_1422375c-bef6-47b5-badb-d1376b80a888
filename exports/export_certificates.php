<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

// Get format parameter
$format = isset($_GET['format']) ? $_GET['format'] : 'csv';
$timestamp = isset($_GET['timestamp']) ? $_GET['timestamp'] : date('Y-m-d_H-i-s');

try {
    // Get certificate data with user and category information
    $stmt = $pdo->query("
        SELECT
            uc.certificate_code,
            u.username,
            u.email,
            c.name as category_name,
            uc.issued_at,
            uc.is_revoked,
            uc.revoked_at,
            uc.revoked_by
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories c ON uc.category_id = c.id
        ORDER BY uc.issued_at DESC
    ");
    $certificates = $stmt->fetchAll();

    if (empty($certificates)) {
        header('HTTP/1.1 404 Not Found');
        exit('No certificates found');
    }

    // Set appropriate headers based on format
    switch ($format) {
        case 'csv':
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="certificates_export_' . $timestamp . '.csv"');
            exportCSV($certificates);
            break;
        
        case 'excel':
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header('Content-Disposition: attachment; filename="certificates_export_' . $timestamp . '.xls"');
            exportExcel($certificates);
            break;
        
        case 'pdf':
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="certificates_export_' . $timestamp . '.pdf"');
            exportPDF($certificates);
            break;
        
        default:
            header('HTTP/1.1 400 Bad Request');
            exit('Invalid format specified');
    }

} catch (PDOException $e) {
    error_log("Error exporting certificates: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    exit('Database error occurred');
} catch (Exception $e) {
    error_log("Error exporting certificates: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    exit('An error occurred while exporting certificates');
}

function exportCSV($certificates) {
    $output = fopen('php://output', 'w');

    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Headers
    fputcsv($output, [
        'Certificate Code',
        'Username',
        'Email',
        'Category',
        'Issued Date',
        'Status',
        'Revoked Date',
        'Revoked By'
    ]);

    // Data rows
    foreach ($certificates as $cert) {
        fputcsv($output, [
            $cert['certificate_code'],
            $cert['username'],
            $cert['email'],
            $cert['category_name'],
            date('Y-m-d H:i:s', strtotime($cert['issued_at'])),
            $cert['is_revoked'] ? 'Revoked' : 'Active',
            $cert['revoked_at'] ? date('Y-m-d H:i:s', strtotime($cert['revoked_at'])) : '',
            $cert['revoked_by'] ?: ''
        ]);
    }

    fclose($output);
}

function exportExcel($certificates) {
    // Simple HTML table format that Excel can read
    echo '<table border="1">
        <thead>
            <tr>
                <th>Certificate Code</th>
                <th>Username</th>
                <th>Email</th>
                <th>Category</th>
                <th>Issued Date</th>
                <th>Status</th>
                <th>Revoked Date</th>
                <th>Revoked By</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($certificates as $cert) {
        echo '<tr>
            <td>' . htmlspecialchars($cert['certificate_code']) . '</td>
            <td>' . htmlspecialchars($cert['username']) . '</td>
            <td>' . htmlspecialchars($cert['email']) . '</td>
            <td>' . htmlspecialchars($cert['category_name']) . '</td>
            <td>' . date('Y-m-d H:i:s', strtotime($cert['issued_at'])) . '</td>
            <td>' . ($cert['is_revoked'] ? 'Revoked' : 'Active') . '</td>
            <td>' . ($cert['revoked_at'] ? date('Y-m-d H:i:s', strtotime($cert['revoked_at'])) : '') . '</td>
            <td>' . htmlspecialchars($cert['revoked_by'] ?: '') . '</td>
        </tr>';
    }

    echo '</tbody></table>';
}

function exportPDF($certificates) {
    // Simple HTML to PDF conversion (basic implementation)
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Certificates Export</title>
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            h1 { color: #333; text-align: center; }
            .header { text-align: center; margin-bottom: 30px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>TryMeOut - Certificates Export</h1>
            <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>
            <p>Total Certificates: ' . count($certificates) . '</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Certificate Code</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Category</th>
                    <th>Issued Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($certificates as $cert) {
        $html .= '<tr>
            <td>' . htmlspecialchars($cert['certificate_code']) . '</td>
            <td>' . htmlspecialchars($cert['username']) . '</td>
            <td>' . htmlspecialchars($cert['email']) . '</td>
            <td>' . htmlspecialchars($cert['category_name']) . '</td>
            <td>' . date('Y-m-d', strtotime($cert['issued_at'])) . '</td>
            <td>' . ($cert['is_revoked'] ? 'Revoked' : 'Active') . '</td>
        </tr>';
    }

    $html .= '</tbody></table></body></html>';
    
    // For basic PDF generation, we'll output HTML that can be printed to PDF
    echo $html;
}
?>
