<?php
/**
 * MCP Database Connection Configuration
 * This file provides database connection settings for MCP integration
 */

// Database Configuration for MCP
$mcp_db_config = [
    'host' => 'localhost',
    'port' => '3306',
    'database' => 'vuln_platform_beta',  // Your database name
    'username' => 'root',           // Your MySQL username
    'password' => '',               // Your MySQL password (usually empty for XAMPP)
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];

/**
 * Create PDO connection for MCP
 */
function createMCPConnection() {
    global $mcp_db_config;
    
    try {
        $dsn = "mysql:host={$mcp_db_config['host']};port={$mcp_db_config['port']};dbname={$mcp_db_config['database']};charset={$mcp_db_config['charset']}";
        
        $pdo = new PDO(
            $dsn,
            $mcp_db_config['username'],
            $mcp_db_config['password'],
            $mcp_db_config['options']
        );
        
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Test database connection
 */
function testMCPConnection() {
    try {
        $pdo = createMCPConnection();
        $stmt = $pdo->query("SELECT 1");
        return [
            'status' => 'success',
            'message' => 'Database connection successful',
            'server_info' => $pdo->getAttribute(PDO::ATTR_SERVER_INFO)
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Get database schema information for MCP
 */
function getMCPDatabaseSchema() {
    try {
        $pdo = createMCPConnection();
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $schema = [];
        
        foreach ($tables as $table) {
            // Get table structure
            $stmt = $pdo->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll();
            
            // Get table row count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $stmt->fetch()['count'];
            
            $schema[$table] = [
                'columns' => $columns,
                'row_count' => $count
            ];
        }
        
        return $schema;
    } catch (Exception $e) {
        throw new Exception("Failed to get database schema: " . $e->getMessage());
    }
}

/**
 * Execute safe queries for MCP
 */
function executeMCPQuery($query, $params = []) {
    try {
        $pdo = createMCPConnection();
        
        // Security: Only allow SELECT queries for safety
        $query = trim($query);
        if (!preg_match('/^SELECT\s+/i', $query)) {
            throw new Exception("Only SELECT queries are allowed for security reasons");
        }
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        
        return [
            'status' => 'success',
            'data' => $stmt->fetchAll(),
            'row_count' => $stmt->rowCount()
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }
}

// Export configuration for use in other files
return $mcp_db_config;
?>
