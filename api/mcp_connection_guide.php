<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>MCP phpMyAdmin Connection Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: 'Segoe UI', sans-serif; min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px; }
        .guide-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 25px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(255,255,255,0.2); }
        .method-card { background: #f8f9fa; padding: 20px; border-radius: 15px; border-left: 4px solid #28a745; margin: 15px 0; }
        .code-example { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 10px 0; overflow-x: auto; }
        .step-box { background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #2196f3; }
        .warning-box { background: #fff3cd; color: #856404; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
        .success-box { background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="text-white display-4 mb-3">🔗 MCP ↔ phpMyAdmin Connection Guide</h1>
            <p class="text-white lead">Complete Guide to Connect Model Context Protocol with Your Database</p>
        </div>

        <!-- Overview -->
        <div class="guide-card">
            <h2><i class="fas fa-info-circle text-primary"></i> Connection Overview</h2>

            <div class="success-box">
                <h6><i class="fas fa-check-circle text-success"></i> What You'll Achieve</h6>
                <p>Connect MCP (Model Context Protocol) directly to your phpMyAdmin database for real-time data access, queries, and analysis.</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5>🎯 Connection Benefits:</h5>
                    <ul>
                        <li>Real-time database access for MCP</li>
                        <li>Direct SQL query execution</li>
                        <li>Secure API endpoints</li>
                        <li>Database schema exploration</li>
                        <li>Analytics and reporting</li>
                        <li>Data export capabilities</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🛡️ Security Features:</h5>
                    <ul>
                        <li>Read-only access by default</li>
                        <li>API key authentication</li>
                        <li>SQL injection protection</li>
                        <li>Query validation</li>
                        <li>Error handling</li>
                        <li>Connection limits</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Method 1: Direct Database Connection -->
        <div class="guide-card">
            <h2><i class="fas fa-database text-info"></i> Method 1: Direct Database Connection</h2>

            <div class="method-card">
                <h5><i class="fas fa-plug text-success"></i> Direct PDO Connection</h5>
                <p>Connect MCP directly to your MySQL database using PDO</p>
            </div>

            <div class="step-box">
                <h6>Step 1: Database Configuration</h6>
                <p>Use the provided configuration file to set up your database connection:</p>
            </div>

            <div class="code-example">
// File: mcp_database_config.php<br>
$mcp_db_config = [<br>
&nbsp;&nbsp;&nbsp;&nbsp;'host' => 'localhost',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'port' => '3306',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'database' => 'vuln_platform',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'username' => 'root',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'password' => '',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'charset' => 'utf8mb4'<br>
];
            </div>

            <div class="step-box">
                <h6>Step 2: Create Connection Function</h6>
                <p>Use the createMCPConnection() function to establish database connection:</p>
            </div>

            <div class="code-example">
// Create connection<br>
$pdo = createMCPConnection();<br><br>

// Execute query<br>
$result = executeMCPQuery("SELECT * FROM users LIMIT 10");<br><br>

// Process results<br>
if ($result['status'] === 'success') {<br>
&nbsp;&nbsp;&nbsp;&nbsp;foreach ($result['data'] as $row) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;// Process data<br>
&nbsp;&nbsp;&nbsp;&nbsp;}<br>
}
            </div>
        </div>

        <!-- Method 2: API Endpoint -->
        <div class="guide-card">
            <h2><i class="fas fa-api text-warning"></i> Method 2: RESTful API Endpoint</h2>

            <div class="method-card">
                <h5><i class="fas fa-cloud text-primary"></i> API-Based Access</h5>
                <p>Access your database through secure RESTful API endpoints</p>
            </div>

            <div class="step-box">
                <h6>API Endpoints Available:</h6>
                <ul>
                    <li><code>/mcp_api_endpoint.php?endpoint=test</code> - Test connection</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=schema</code> - Get database schema</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=users</code> - Get users data</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=challenges</code> - Get challenges</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=certificates</code> - Get certificates</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=badges</code> - Get badges</li>
                    <li><code>/mcp_api_endpoint.php?endpoint=analytics</code> - Get analytics</li>
                </ul>
            </div>

            <div class="step-box">
                <h6>API Usage Example:</h6>
            </div>

            <div class="code-example">
// GET request with API key<br>
curl -H "Authorization: mcp_trymeout_2024" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;"http://localhost/vuln-platform-beta/mcp_api_endpoint.php?endpoint=users&limit=10"<br><br>

// POST request for custom query<br>
curl -X POST \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-H "Authorization: mcp_trymeout_2024" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-H "Content-Type: application/json" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-d '{"query":"SELECT * FROM users WHERE active = 1"}' \<br>
&nbsp;&nbsp;&nbsp;&nbsp;"http://localhost/vuln-platform-beta/mcp_api_endpoint.php?endpoint=query"
            </div>
        </div>

        <!-- Method 3: Database Interface -->
        <div class="guide-card">
            <h2><i class="fas fa-desktop text-success"></i> Method 3: Web Interface</h2>

            <div class="method-card">
                <h5><i class="fas fa-browser text-info"></i> Browser-Based Interface</h5>
                <p>Use the web interface to test connections and execute queries</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h6>🌐 Interface Features:</h6>
                    <ul>
                        <li>Connection status monitoring</li>
                        <li>Database schema visualization</li>
                        <li>Query execution interface</li>
                        <li>Result visualization</li>
                        <li>Error handling and debugging</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🔧 Access Methods:</h6>
                    <ul>
                        <li>Direct browser access</li>
                        <li>Embedded in admin panel</li>
                        <li>API testing interface</li>
                        <li>Schema exploration tool</li>
                        <li>Query builder interface</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Configuration Steps -->
        <div class="guide-card">
            <h2><i class="fas fa-cog text-secondary"></i> Configuration Steps</h2>

            <div class="step-box">
                <h6>Step 1: Verify Database Connection</h6>
                <p>Ensure your phpMyAdmin database is accessible:</p>
                <ul>
                    <li>XAMPP/WAMP is running</li>
                    <li>MySQL service is active</li>
                    <li>Database 'vuln_platform' exists</li>
                    <li>Correct credentials are configured</li>
                </ul>
            </div>

            <div class="step-box">
                <h6>Step 2: Update Configuration</h6>
                <p>Modify the database configuration in <code>mcp_database_config.php</code>:</p>
                <ul>
                    <li>Set correct host (usually 'localhost')</li>
                    <li>Set correct port (usually '3306')</li>
                    <li>Set your database name</li>
                    <li>Set your MySQL username and password</li>
                </ul>
            </div>

            <div class="step-box">
                <h6>Step 3: Test Connection</h6>
                <p>Use the interface to test your connection:</p>
                <ul>
                    <li>Access the MCP Database Interface</li>
                    <li>Check connection status</li>
                    <li>Verify schema loading</li>
                    <li>Test sample queries</li>
                </ul>
            </div>

            <div class="step-box">
                <h6>Step 4: Configure API Access</h6>
                <p>Set up API endpoints for MCP access:</p>
                <ul>
                    <li>Configure API key authentication</li>
                    <li>Test API endpoints</li>
                    <li>Verify JSON responses</li>
                    <li>Set up error handling</li>
                </ul>
            </div>
        </div>

        <!-- Security Considerations -->
        <div class="guide-card">
            <h2><i class="fas fa-shield-alt text-danger"></i> Security Considerations</h2>

            <div class="warning-box">
                <h6><i class="fas fa-exclamation-triangle text-warning"></i> Important Security Notes</h6>
                <p>Follow these security best practices when connecting MCP to your database:</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h6>🔒 Database Security:</h6>
                    <ul>
                        <li>Use read-only database user for MCP</li>
                        <li>Limit database permissions</li>
                        <li>Enable query logging</li>
                        <li>Use prepared statements</li>
                        <li>Validate all inputs</li>
                        <li>Implement connection limits</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🛡️ API Security:</h6>
                    <ul>
                        <li>Use strong API keys</li>
                        <li>Implement rate limiting</li>
                        <li>Enable HTTPS in production</li>
                        <li>Log all API requests</li>
                        <li>Validate request origins</li>
                        <li>Handle errors securely</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="guide-card">
            <h2><i class="fas fa-wrench text-warning"></i> Troubleshooting</h2>

            <div class="row">
                <div class="col-md-6">
                    <h6>🔧 Common Issues:</h6>
                    <ul>
                        <li><strong>Connection Failed:</strong> Check MySQL service</li>
                        <li><strong>Access Denied:</strong> Verify credentials</li>
                        <li><strong>Database Not Found:</strong> Check database name</li>
                        <li><strong>Port Issues:</strong> Verify MySQL port</li>
                        <li><strong>Permission Errors:</strong> Check user privileges</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🛠️ Solutions:</h6>
                    <ul>
                        <li>Use the test connection feature</li>
                        <li>Check phpMyAdmin access</li>
                        <li>Verify XAMPP/WAMP status</li>
                        <li>Review error logs</li>
                        <li>Test with simple queries first</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Example Usage -->
        <div class="guide-card">
            <h2><i class="fas fa-code text-info"></i> Example MCP Usage</h2>

            <div class="step-box">
                <h6>Python Example - Using the API:</h6>
            </div>

            <div class="code-example">
import requests<br>
import json<br><br>

# API configuration<br>
BASE_URL = "http://localhost/vuln-platform-beta/mcp_api_endpoint.php"<br>
API_KEY = "mcp_trymeout_2024"<br><br>

# Test connection<br>
response = requests.get(f"{BASE_URL}?endpoint=test&api_key={API_KEY}")<br>
print("Connection test:", response.json())<br><br>

# Get users data<br>
response = requests.get(f"{BASE_URL}?endpoint=users&limit=5&api_key={API_KEY}")<br>
users_data = response.json()<br>
print("Users:", users_data['result']['data'])<br><br>

# Execute custom query<br>
query_data = {<br>
&nbsp;&nbsp;&nbsp;&nbsp;"query": "SELECT COUNT(*) as total FROM users"<br>
}<br>
response = requests.post(<br>
&nbsp;&nbsp;&nbsp;&nbsp;f"{BASE_URL}?endpoint=query&api_key={API_KEY}",<br>
&nbsp;&nbsp;&nbsp;&nbsp;json=query_data<br>
)<br>
print("Query result:", response.json())
            </div>

            <div class="step-box">
                <h6>JavaScript Example - Using Fetch API:</h6>
            </div>

            <div class="code-example">
// MCP Database Client<br>
class MCPClient {<br>
&nbsp;&nbsp;&nbsp;&nbsp;constructor(baseUrl, apiKey) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.baseUrl = baseUrl;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.apiKey = apiKey;<br>
&nbsp;&nbsp;&nbsp;&nbsp;}<br><br>

&nbsp;&nbsp;&nbsp;&nbsp;async get(endpoint, params = {}) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const url = new URL(`${this.baseUrl}?endpoint=${endpoint}&api_key=${this.apiKey}`);<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const response = await fetch(url);<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return await response.json();<br>
&nbsp;&nbsp;&nbsp;&nbsp;}<br><br>

&nbsp;&nbsp;&nbsp;&nbsp;async query(sql) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const response = await fetch(`${this.baseUrl}?endpoint=query&api_key=${this.apiKey}`, {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;method: 'POST',<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;headers: { 'Content-Type': 'application/json' },<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;body: JSON.stringify({ query: sql })<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;});<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return await response.json();<br>
&nbsp;&nbsp;&nbsp;&nbsp;}<br>
}<br><br>

// Usage<br>
const mcp = new MCPClient('http://localhost/vuln-platform-beta/mcp_api_endpoint.php', 'mcp_trymeout_2024');<br>
const users = await mcp.get('users', { limit: 10 });<br>
console.log(users);
            </div>
        </div>

        <!-- Quick Access Links -->
        <div class="text-center">
            <a href="mcp_database_interface.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-database"></i> MCP Database Interface
            </a>
            <a href="mcp_api_endpoint.php?endpoint=test&api_key=mcp_trymeout_2024" class="btn btn-outline-light btn-lg me-3" target="_blank">
                <i class="fas fa-api"></i> Test API Endpoint
            </a>
            <a href="http://localhost/phpmyadmin" class="btn btn-outline-light btn-lg me-3" target="_blank">
                <i class="fas fa-external-link-alt"></i> phpMyAdmin
            </a>
            <a href="enhanced_admin_dashboard.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
            </a>
        </div>
    </div>
</body>
</html>
