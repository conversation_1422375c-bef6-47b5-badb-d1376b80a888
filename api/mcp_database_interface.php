<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>MCP Database Interface - TryMeOut Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: 'Segoe UI', sans-serif; min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px; }
        .interface-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 25px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(255,255,255,0.2); }
        .connection-status { padding: 15px; border-radius: 10px; margin: 15px 0; }
        .status-success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .status-error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .query-box { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 15px 0; }
        .table-info { background: #e3f2fd; padding: 10px; border-radius: 8px; margin: 5px 0; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="text-white display-4 mb-3">🔗 MCP Database Interface</h1>
            <p class="text-white lead">Connect MCP with phpMyAdmin Database</p>
        </div>

        <?php
        require_once 'mcp_database_config.php';
        
        // Test connection
        $connection_test = testMCPConnection();
        ?>

        <!-- Connection Status -->
        <div class="interface-card">
            <h2><i class="fas fa-database text-primary"></i> Database Connection Status</h2>
            
            <div class="connection-status <?= $connection_test['status'] === 'success' ? 'status-success' : 'status-error' ?>">
                <h6>
                    <i class="fas fa-<?= $connection_test['status'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                    Connection <?= ucfirst($connection_test['status']) ?>
                </h6>
                <p><?= htmlspecialchars($connection_test['message']) ?></p>
                <?php if (isset($connection_test['server_info'])): ?>
                    <small>Server Info: <?= htmlspecialchars($connection_test['server_info']) ?></small>
                <?php endif; ?>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5>📊 Connection Details:</h5>
                    <ul>
                        <li><strong>Host:</strong> <?= htmlspecialchars($mcp_db_config['host']) ?></li>
                        <li><strong>Port:</strong> <?= htmlspecialchars($mcp_db_config['port']) ?></li>
                        <li><strong>Database:</strong> <?= htmlspecialchars($mcp_db_config['database']) ?></li>
                        <li><strong>Username:</strong> <?= htmlspecialchars($mcp_db_config['username']) ?></li>
                        <li><strong>Charset:</strong> <?= htmlspecialchars($mcp_db_config['charset']) ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🔧 Configuration:</h5>
                    <ul>
                        <li><strong>Error Mode:</strong> Exception</li>
                        <li><strong>Fetch Mode:</strong> Associative</li>
                        <li><strong>Prepared Statements:</strong> Enabled</li>
                        <li><strong>Security:</strong> Read-only queries</li>
                        <li><strong>Connection Type:</strong> PDO MySQL</li>
                    </ul>
                </div>
            </div>
        </div>

        <?php if ($connection_test['status'] === 'success'): ?>
        <!-- Database Schema -->
        <div class="interface-card">
            <h2><i class="fas fa-table text-info"></i> Database Schema</h2>
            
            <?php
            try {
                $schema = getMCPDatabaseSchema();
            ?>
            
            <div class="row">
                <?php foreach ($schema as $table_name => $table_info): ?>
                <div class="col-md-6 mb-3">
                    <div class="table-info">
                        <h6><i class="fas fa-table text-primary"></i> <?= htmlspecialchars($table_name) ?></h6>
                        <p><strong>Rows:</strong> <?= number_format($table_info['row_count']) ?></p>
                        <p><strong>Columns:</strong> <?= count($table_info['columns']) ?></p>
                        
                        <details>
                            <summary>View Columns</summary>
                            <ul class="mt-2">
                                <?php foreach ($table_info['columns'] as $column): ?>
                                <li>
                                    <strong><?= htmlspecialchars($column['Field']) ?></strong>
                                    (<?= htmlspecialchars($column['Type']) ?>)
                                    <?= $column['Key'] === 'PRI' ? '<span class="badge bg-warning">PK</span>' : '' ?>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </details>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">Error loading schema: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <!-- Query Interface -->
        <div class="interface-card">
            <h2><i class="fas fa-search text-success"></i> Query Interface</h2>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="query" class="form-label">SQL Query (SELECT only for security):</label>
                    <textarea class="form-control" id="query" name="query" rows="4" placeholder="SELECT * FROM users LIMIT 10"><?= isset($_POST['query']) ? htmlspecialchars($_POST['query']) : '' ?></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play"></i> Execute Query
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearQuery()">
                    <i class="fas fa-eraser"></i> Clear
                </button>
            </form>

            <?php
            if (isset($_POST['query']) && !empty($_POST['query'])) {
                $result = executeMCPQuery($_POST['query']);
                
                if ($result['status'] === 'success') {
                    echo '<div class="alert alert-success mt-3">Query executed successfully. Rows returned: ' . $result['row_count'] . '</div>';
                    
                    if (!empty($result['data'])) {
                        echo '<div class="table-responsive mt-3">';
                        echo '<table class="table table-striped table-hover">';
                        
                        // Table header
                        echo '<thead class="table-dark"><tr>';
                        foreach (array_keys($result['data'][0]) as $column) {
                            echo '<th>' . htmlspecialchars($column) . '</th>';
                        }
                        echo '</tr></thead>';
                        
                        // Table body
                        echo '<tbody>';
                        foreach ($result['data'] as $row) {
                            echo '<tr>';
                            foreach ($row as $value) {
                                echo '<td>' . htmlspecialchars($value ?? 'NULL') . '</td>';
                            }
                            echo '</tr>';
                        }
                        echo '</tbody></table></div>';
                    }
                } else {
                    echo '<div class="alert alert-danger mt-3">Error: ' . htmlspecialchars($result['message']) . '</div>';
                }
            }
            ?>
        </div>
        <?php endif; ?>

        <!-- MCP Integration Guide -->
        <div class="interface-card">
            <h2><i class="fas fa-book text-warning"></i> MCP Integration Guide</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>🔗 Connection Methods:</h5>
                    <ul>
                        <li><strong>Direct PDO:</strong> Use the configuration file</li>
                        <li><strong>API Endpoint:</strong> Create REST API for MCP</li>
                        <li><strong>Socket Connection:</strong> Real-time data streaming</li>
                        <li><strong>File Export:</strong> Export data for MCP processing</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🛡️ Security Features:</h5>
                    <ul>
                        <li><strong>Read-only Access:</strong> Only SELECT queries allowed</li>
                        <li><strong>Prepared Statements:</strong> SQL injection protection</li>
                        <li><strong>Error Handling:</strong> Secure error messages</li>
                        <li><strong>Connection Limits:</strong> Resource management</li>
                    </ul>
                </div>
            </div>

            <h5 class="mt-4">📝 Example MCP Usage:</h5>
            <div class="code-block">
// Include the configuration<br>
require_once 'mcp_database_config.php';<br><br>

// Create connection<br>
$pdo = createMCPConnection();<br><br>

// Execute query<br>
$result = executeMCPQuery("SELECT * FROM users WHERE active = 1");<br><br>

// Process results<br>
if ($result['status'] === 'success') {<br>
&nbsp;&nbsp;&nbsp;&nbsp;foreach ($result['data'] as $user) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;// Process user data<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo $user['username'];<br>
&nbsp;&nbsp;&nbsp;&nbsp;}<br>
}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center">
            <a href="enhanced_admin_dashboard.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
            </a>
            <a href="http://localhost/phpmyadmin" class="btn btn-outline-light btn-lg me-3" target="_blank">
                <i class="fas fa-database"></i> phpMyAdmin
            </a>
            <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-user"></i> User Dashboard
            </a>
        </div>
    </div>

    <script>
        function clearQuery() {
            document.getElementById('query').value = '';
        }
    </script>
</body>
</html>
