<?php
/**
 * MCP API Endpoint for Database Access
 * Provides RESTful API for MCP to interact with phpMyAdmin database
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'mcp_database_config.php';

/**
 * Send JSON response
 */
function sendResponse($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

/**
 * Validate API key (basic security)
 */
function validateApiKey() {
    $headers = getallheaders();
    $api_key = $headers['Authorization'] ?? $_GET['api_key'] ?? null;
    
    // Simple API key validation (you should use a more secure method)
    $valid_api_key = 'mcp_trymeout_2024';
    
    if ($api_key !== $valid_api_key) {
        sendResponse([
            'error' => 'Invalid API key',
            'message' => 'Please provide a valid API key'
        ], 401);
    }
}

/**
 * Get request data
 */
function getRequestData() {
    $input = file_get_contents('php://input');
    return json_decode($input, true) ?: [];
}

// Validate API key for all requests
validateApiKey();

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = $_GET['endpoint'] ?? '';

try {
    switch ($endpoint) {
        case 'test':
            // Test database connection
            $result = testMCPConnection();
            sendResponse([
                'endpoint' => 'test',
                'timestamp' => date('Y-m-d H:i:s'),
                'connection' => $result
            ]);
            break;

        case 'schema':
            // Get database schema
            $schema = getMCPDatabaseSchema();
            sendResponse([
                'endpoint' => 'schema',
                'timestamp' => date('Y-m-d H:i:s'),
                'tables' => array_keys($schema),
                'schema' => $schema
            ]);
            break;

        case 'query':
            // Execute database query
            if ($method !== 'POST') {
                sendResponse(['error' => 'Only POST method allowed for queries'], 405);
            }
            
            $data = getRequestData();
            $query = $data['query'] ?? '';
            $params = $data['params'] ?? [];
            
            if (empty($query)) {
                sendResponse(['error' => 'Query parameter is required'], 400);
            }
            
            $result = executeMCPQuery($query, $params);
            sendResponse([
                'endpoint' => 'query',
                'timestamp' => date('Y-m-d H:i:s'),
                'query' => $query,
                'result' => $result
            ]);
            break;

        case 'users':
            // Get users data
            $limit = $_GET['limit'] ?? 50;
            $offset = $_GET['offset'] ?? 0;
            
            $result = executeMCPQuery(
                "SELECT id, username, email, role, created_at, 
                        (SELECT COUNT(*) FROM user_progress WHERE user_id = users.id AND status = 'completed') as completed_challenges
                 FROM users 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?",
                [$limit, $offset]
            );
            
            sendResponse([
                'endpoint' => 'users',
                'timestamp' => date('Y-m-d H:i:s'),
                'limit' => $limit,
                'offset' => $offset,
                'result' => $result
            ]);
            break;

        case 'challenges':
            // Get challenges data
            $category_id = $_GET['category_id'] ?? null;
            
            $query = "SELECT c.*, cat.name as category_name,
                             COUNT(up.id) as completion_count
                      FROM challenges c
                      JOIN categories cat ON c.category_id = cat.id
                      LEFT JOIN user_progress up ON c.id = up.challenge_id AND up.status = 'completed'";
            
            $params = [];
            if ($category_id) {
                $query .= " WHERE c.category_id = ?";
                $params[] = $category_id;
            }
            
            $query .= " GROUP BY c.id ORDER BY completion_count DESC";
            
            $result = executeMCPQuery($query, $params);
            
            sendResponse([
                'endpoint' => 'challenges',
                'timestamp' => date('Y-m-d H:i:s'),
                'category_filter' => $category_id,
                'result' => $result
            ]);
            break;

        case 'certificates':
            // Get certificates data
            $user_id = $_GET['user_id'] ?? null;
            
            $query = "SELECT uc.*, u.username, u.email, c.name as category_name
                      FROM user_certificates uc
                      JOIN users u ON uc.user_id = u.id
                      JOIN categories c ON uc.category_id = c.id";
            
            $params = [];
            if ($user_id) {
                $query .= " WHERE uc.user_id = ?";
                $params[] = $user_id;
            }
            
            $query .= " ORDER BY uc.issued_at DESC";
            
            $result = executeMCPQuery($query, $params);
            
            sendResponse([
                'endpoint' => 'certificates',
                'timestamp' => date('Y-m-d H:i:s'),
                'user_filter' => $user_id,
                'result' => $result
            ]);
            break;

        case 'badges':
            // Get badges data
            $user_id = $_GET['user_id'] ?? null;
            
            $query = "SELECT ub.*, u.username, u.email, c.name as category_name
                      FROM user_badges ub
                      JOIN users u ON ub.user_id = u.id
                      JOIN categories c ON ub.category_id = c.id";
            
            $params = [];
            if ($user_id) {
                $query .= " WHERE ub.user_id = ?";
                $params[] = $user_id;
            }
            
            $query .= " ORDER BY ub.earned_at DESC";
            
            $result = executeMCPQuery($query, $params);
            
            sendResponse([
                'endpoint' => 'badges',
                'timestamp' => date('Y-m-d H:i:s'),
                'user_filter' => $user_id,
                'result' => $result
            ]);
            break;

        case 'analytics':
            // Get analytics data
            $days = $_GET['days'] ?? 30;
            
            // Get daily completions
            $completions_result = executeMCPQuery(
                "SELECT DATE(completed_at) as date, COUNT(*) as completions
                 FROM user_progress 
                 WHERE status = 'completed' AND completed_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                 GROUP BY DATE(completed_at)
                 ORDER BY date DESC",
                [$days]
            );
            
            // Get user statistics
            $users_result = executeMCPQuery(
                "SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) THEN 1 END) as new_users,
                    COUNT(CASE WHEN (SELECT COUNT(*) FROM user_progress WHERE user_id = users.id AND status = 'completed') > 0 THEN 1 END) as active_users
                 FROM users",
                [$days]
            );
            
            sendResponse([
                'endpoint' => 'analytics',
                'timestamp' => date('Y-m-d H:i:s'),
                'period_days' => $days,
                'daily_completions' => $completions_result,
                'user_statistics' => $users_result
            ]);
            break;

        case 'categories':
            // Get categories data
            $result = executeMCPQuery(
                "SELECT c.*, 
                        COUNT(DISTINCT ch.id) as challenge_count,
                        COUNT(DISTINCT ub.id) as badge_count,
                        COUNT(DISTINCT uc.id) as certificate_count
                 FROM categories c
                 LEFT JOIN challenges ch ON c.id = ch.category_id
                 LEFT JOIN user_badges ub ON c.id = ub.category_id
                 LEFT JOIN user_certificates uc ON c.id = uc.category_id
                 GROUP BY c.id
                 ORDER BY c.name"
            );
            
            sendResponse([
                'endpoint' => 'categories',
                'timestamp' => date('Y-m-d H:i:s'),
                'result' => $result
            ]);
            break;

        default:
            // Invalid endpoint
            sendResponse([
                'error' => 'Invalid endpoint',
                'available_endpoints' => [
                    'test' => 'Test database connection',
                    'schema' => 'Get database schema',
                    'query' => 'Execute custom query (POST)',
                    'users' => 'Get users data',
                    'challenges' => 'Get challenges data',
                    'certificates' => 'Get certificates data',
                    'badges' => 'Get badges data',
                    'analytics' => 'Get analytics data',
                    'categories' => 'Get categories data'
                ],
                'usage' => 'Add ?endpoint=<endpoint_name>&api_key=mcp_trymeout_2024'
            ], 404);
            break;
    }

} catch (Exception $e) {
    sendResponse([
        'error' => 'Server error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], 500);
}
?>
