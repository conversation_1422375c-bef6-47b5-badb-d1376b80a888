<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 18;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Handle AJAX request for attempt increment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['increment_attempt'])) {
    $_SESSION[$session_key]++;
    echo json_encode(['status' => 'success', 'attempts' => $_SESSION[$session_key]]);
    exit;
}

// Handle AJAX request for challenge completion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['complete_challenge'])) {
    $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
    $stmt->execute([$_SESSION['user_id'], $challenge_id]);

    $_SESSION[$session_key] = 0;
    echo json_encode(['status' => 'success', 'message' => 'Challenge completed!']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Advanced DOM Manipulation | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right, #c9d6ff, #e2e2e2);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: #ffffff;
            padding: 40px 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 700px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .app-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input, textarea {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #dynamic-content {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            min-height: 100px;
            border: 2px dashed #6c757d;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
        .url-display {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>🌐 DynamicApp URL Processor</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🌐 Scenario:</strong> You're testing DynamicApp which processes URL fragments and updates page content dynamically using JavaScript. This client-side processing might be vulnerable to DOM-based XSS.
        <br><br>
        <strong>🎯 Objective:</strong> Manipulate URL fragments to inject JavaScript that executes through DOM manipulation.
    </div>

    <div class="app-section">
        <h3>URL Fragment Processor</h3>
        <p>This application processes URL fragments and displays dynamic content based on the hash value.</p>

        <div class="url-display">
            <strong>Current URL:</strong> <span id="current-url"></span>
        </div>

        <label for="hash-input">Enter URL Fragment (after #):</label>
        <input type="text" id="hash-input" placeholder="e.g., page=home&user=admin">
        <button onclick="updateHash()">Update Fragment</button>
        <button onclick="processFragment()">Process Fragment</button>
    </div>

    <div class="app-section">
        <h3>Dynamic Content Area</h3>
        <div id="dynamic-content">
            <p>Content will appear here based on URL fragment processing...</p>
        </div>
    </div>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Exploit DOM-based XSS through URL fragment manipulation. The application processes URL fragments and dynamically updates the DOM.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #ffc107; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box" style="display: none;">
            ✅ <strong>Working Solutions:</strong> Try these DOM XSS techniques in the URL fragment (after #):<br><br>
            • <code>&lt;img src=x onerror=alert(1)&gt;</code><br>
            • <code>&lt;script&gt;alert(1)&lt;/script&gt;</code><br>
            • <code>&lt;svg onload=alert(1)&gt;</code><br>
            • <code>&lt;iframe src=javascript:alert(1)&gt;</code><br><br>
            Add these payloads after the # in the URL to exploit DOM-based XSS.
        </div>
    <?php endif; ?>
    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    let attemptCount = 0;

    function updateURL() {
        document.getElementById('current-url').textContent = window.location.href;
    }

    function updateHash() {
        const input = document.getElementById('hash-input').value;
        if (input) {
            window.location.hash = input;
            updateURL();
        }
    }

    function processFragment() {
        const hash = window.location.hash.substring(1); // Remove the #
        const contentDiv = document.getElementById('dynamic-content');

        if (hash) {
            // Increment attempt count
            attemptCount++;

            // Send attempt to server
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'increment_attempt=1'
            });

            // Vulnerable DOM manipulation - directly inserting user input
            contentDiv.innerHTML = '<h4>Processing Fragment:</h4><p>' + decodeURIComponent(hash) + '</p>';

            // Check if XSS was executed by looking for script execution
            setTimeout(() => {
                checkForXSS();
            }, 100);
        } else {
            contentDiv.innerHTML = '<p>No fragment to process. Please enter a URL fragment.</p>';
        }
    }

    function checkForXSS() {
        // This function will be called if XSS is successful
        // In a real scenario, this would be triggered by the XSS payload
    }

    // Override alert function to detect XSS
    const originalAlert = window.alert;
    window.alert = function(message) {
        // XSS detected!
        completeChallenge();
        originalAlert('🎉 Challenge completed! You successfully executed DOM-based XSS!');
    };

    function completeChallenge() {
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'complete_challenge=1'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                setTimeout(() => {
                    window.location.href = 'file_upload_xss.php';
                }, 2000);
            }
        });
    }

    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }

    // Initialize
    updateURL();

    // Process fragment on page load if present
    if (window.location.hash) {
        document.getElementById('hash-input').value = window.location.hash.substring(1);
        processFragment();
    }

    // Listen for hash changes
    window.addEventListener('hashchange', function() {
        updateURL();
        processFragment();
    });
</script>

</body>
</html>
