<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {

    exit;
}

$challenge_id = 7;
$next_challenge_url = 'image_on_error.php'; // Set the next challenge page here
$session_key = 'fail_count_chal_' . $challenge_id;

if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$message = '';
$output = '';
$completed = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = $_POST['xss_input'] ?? '';

    // Basic XSS pattern detection
    if (preg_match('/<script\s*>.*alert\s*\(.*\).*<\/script\s*>/i', $input)) {
        $output = $input;
        $completed = true;

        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        $_SESSION[$session_key] = 0;
    } else {
        $_SESSION[$session_key]++;
        $message = "❌ Incorrect payload. Try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS Challenge 1 - Basic Alert | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right, #c9d6ff, #e2e2e2);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .login-container {
            background: #ffffff;
            padding: 40px 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 460px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #222;
            font-size: 28px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            text-align: left;
            margin-bottom: 6px;
            font-size: 15px;
            color: #555;
        }

        input {
            padding: 12px 14px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 15px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        input:focus {
            border-color: #007bff;
            box-shadow: 0 0 6px rgba(0, 123, 255, 0.3);
            outline: none;
        }

        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:focus {
            outline: 2px dashed #0056b3;
            outline-offset: 2px;
        }

        .error-message {
            color: #e74c3c;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .success-message {
            color: green;
            margin-top: 15px;
            font-size: 15px;
            font-weight: bold;
        }

        .tip-text {
            margin-top: 15px;
            text-align: left;
            font-size: 14px;
            color: #333;
            background: #f9f9f9;
            padding: 10px 14px;
            border-left: 3px solid #007bff;
            border-radius: 4px;
        }

        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #f1f3f5;
            padding: 15px;
            border-left: 4px solid #007bff;
            border-radius: 6px;
            font-size: 14px;
        }

        button.secondary {
            background-color: #6c757d;
        }

        button.secondary:hover {
            background-color: #5a6268;
        }

        button.hint-toggle {
            background-color: #8ea6b6;
        }

        button.hint-toggle:hover {
            background-color: #708da2;
        }

        .output-box {
            margin-top: 25px;
            text-align: left;
            font-size: 14px;
            background: #fff8e1;
            padding: 10px;
            border: 1px solid #ffc107;
            border-radius: 6px;
        }
    </style>
</head>
<body>
<div class="login-container">
    <h1>💬 FeedbackPro Comment System</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>💬 Scenario:</strong> You're testing FeedbackPro's comment system. The application allows users to submit feedback comments but doesn't properly sanitize user input before displaying it on the page.
        <br><br>
        <strong>🎯 Objective:</strong> Inject JavaScript code that executes when the page loads to demonstrate XSS vulnerability.
    </div>

    <?php if ($message): ?>
        <p class="error-message"><?= htmlspecialchars($message) ?></p>
    <?php endif; ?>

    <?php if ($completed): ?>
        <p class="success-message">🎉 Congratulations! You've completed this challenge. Proceeding to next level...</p>
        <script>
            setTimeout(() => {
                window.location.href = "<?= $next_challenge_url ?>";
            }, 2500);
        </script>
    <?php endif; ?>

    <form method="POST">
        <label for="xss_input">Enter some text</label>
        <input type="text" name="xss_input" id="xss_input" required autofocus>
        <button type="submit">Submit</button>
    </form>

    <p class="tip-text">
        🕵️‍♂️ <strong>Tip:</strong> Try injecting something the browser will interpret and execute. Your goal is to make a simple <code>alert()</code> pop up. The classic <code>&lt;script&gt;</code> tag is a good place to start. Keep it simple and test how your input reflects on the page.
    </p>

    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="output-box">
            <strong>Output:</strong><br>
            <?= $output ?>
        </div>
    <?php endif; ?>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" class="hint-toggle" style="margin-top: 20px;">Stuck? Here's the solution</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these JavaScript payloads:<br><br>
            • <code>&lt;script&gt;alert("XSS")&lt;/script&gt;</code><br>
            • <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code><br>
            • <code>&lt;script&gt;alert(1)&lt;/script&gt;</code><br>
            • <code>&lt;script&gt;alert(document.domain)&lt;/script&gt;</code>
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="secondary" style="margin-top: 10px;">Back to Dashboard</button>
</div>

<script>
function toggleHint() {
    const hintBox = document.getElementById('hint-box');
    hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
}
function confirmBack() {
    if (confirm("Return to dashboard?")) {
        window.location.href = '../../pages/dashboard.php';
    }
}
</script>
</body>
</html>
