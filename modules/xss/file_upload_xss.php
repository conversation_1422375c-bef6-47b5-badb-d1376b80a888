<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 19;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$uploaded_files = [];

// Create secure upload directory if it doesn't exist
$upload_dir = __DIR__ . '/safe_uploads/challenge_19/';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
    // Create .htaccess to prevent direct execution
    file_put_contents($upload_dir . '.htaccess', "Options -ExecCGI\nAddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\nOptions -Indexes");
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['upload_file'])) {
    $file = $_FILES['upload_file'];
    $original_filename = $file['name'];
    $tmp_name = $file['tmp_name'];
    $file_size = $file['size'];

    // Strict file validation for security
    $allowed_extensions = ['txt']; // Only allow .txt files for safety
    $file_extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));

    // Additional security checks
    $max_file_size = 1024 * 1024; // 1MB limit

    if ($file_size > $max_file_size) {
        $_SESSION[$session_key]++;
        $message = "File too large. Maximum size: 1MB";
    } elseif (!in_array($file_extension, $allowed_extensions)) {
        $_SESSION[$session_key]++;
        $message = "Invalid file type. Only .txt files are allowed for security reasons.";
    } else {
        // Generate safe filename to prevent directory traversal
        $safe_filename = 'upload_' . $_SESSION['user_id'] . '_' . time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', basename($original_filename));
        $upload_path = $upload_dir . $safe_filename;

        if (move_uploaded_file($tmp_name, $upload_path)) {
            $file_content = file_get_contents($upload_path);

            // Limit file content size for safety
            if (strlen($file_content) > 10000) {
                unlink($upload_path); // Delete oversized file
                $_SESSION[$session_key]++;
                $message = "File content too large. Maximum 10KB allowed.";
            } else {
                // Check for XSS in uploaded file content
                if (strpos(strtolower($file_content), '<script>') !== false ||
                    strpos(strtolower($file_content), 'javascript:') !== false ||
                    strpos(strtolower($file_content), 'onload=') !== false ||
                    strpos(strtolower($file_content), 'onerror=') !== false ||
                    strpos(strtolower($file_content), 'alert(') !== false ||
                    strpos(strtolower($file_content), '<img') !== false ||
                    strpos(strtolower($file_content), '<svg') !== false) {

                    // Mark challenge as completed
                    $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
                    $stmt->execute([$_SESSION['user_id'], $challenge_id]);

                    // Immediately remove the uploaded file for security
                    if (file_exists($upload_path)) {
                        unlink($upload_path);
                    }

                    $_SESSION[$session_key] = 0;
                    $message = "🎉 Challenge completed! You successfully uploaded a .txt file containing XSS payload! File has been securely removed.";

                    // Clear any stored filename since file is deleted
                    unset($_SESSION['uploaded_file']);

                    // Redirect to next challenge
                    echo "<script>
                        setTimeout(() => {
                            window.location.href = '../../pages/dashboard.php';
                        }, 3000);
                    </script>";
                } else {
                    $_SESSION[$session_key]++;
                    $message = "File uploaded successfully, but no XSS payload detected in the .txt content.";
                    $_SESSION['uploaded_file'] = $safe_filename;
                }
            }
        } else {
            $message = "Failed to upload file. Please try again.";
        }
    }
}

// Cleanup old files (older than 1 hour) to prevent storage abuse
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    $current_time = time();

    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && $file !== '.htaccess') {
            $file_path = $upload_dir . $file;
            if (is_file($file_path)) {
                $file_age = $current_time - filemtime($file_path);
                // Remove files older than 1 hour (3600 seconds)
                if ($file_age > 3600) {
                    unlink($file_path);
                }
            }
        }
    }
}

// Get list of uploaded files (only for current user for privacy)
$user_uploaded_files = [];
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    $user_prefix = 'upload_' . $_SESSION['user_id'] . '_';

    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && $file !== '.htaccess' &&
            strpos($file, $user_prefix) === 0 &&
            is_file($upload_dir . $file)) {
            $user_uploaded_files[] = $file;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>FileShare Upload Portal | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .upload-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .upload-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
            border-left: 4px solid #ffc107;
        }
        .upload-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input[type="file"] {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
        }
        .file-item {
            padding: 8px;
            margin: 5px 0;
            background: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
        }
        .file-item:hover {
            background: #dee2e6;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
        #file-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>

<div class="upload-container">
    📁 FileShare Upload Portal</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>📁 Scenario:</strong> You're testing FileShare's file upload functionality. The system accepts various file types and displays their content, which might allow XSS through malicious files.
        <br><br>
        <strong>🎯 Objective:</strong> Upload a TXT file containing JavaScript that executes when the file is viewed.
    </div>

    <div class="upload-info">
        <strong>🔒 Security Restrictions:</strong><br>
        • Allowed file types: <strong>TXT only</strong> (for security)<br>
        • Maximum file size: 1MB<br>
        • Maximum content size: 10KB<br>
        • Files stored in secure directory<br>
        • Direct execution prevented<br>
        • Only your files are visible to you
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Invalid') !== false || strpos($message, 'Failed') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <div class="upload-section">
        <h3>Upload File</h3>
        <form method="POST" enctype="multipart/form-data">
            <input type="file" name="upload_file" accept=".txt" required>
            <button type="submit">Upload TXT File</button>
        </form>
        <small class="text-muted">Only .txt files are accepted for security reasons</small>
    </div>

    <?php if (!empty($user_uploaded_files)): ?>
        <div class="file-list">
            <h3>Your Uploaded Files</h3>
            <?php foreach ($user_uploaded_files as $file): ?>
                <div class="file-item" onclick="viewFile('<?= htmlspecialchars($file) ?>')">
                    📄 <?= htmlspecialchars($file) ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="file-list">
            <p class="text-muted">No files uploaded yet. Upload a .txt file to get started!</p>
        </div>
    <?php endif; ?>

    <div id="file-preview" style="display: none;">
        <h4>File Preview:</h4>
        <div id="file-content"></div>
    </div>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #bbdefb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Upload a .txt file containing XSS payload. Even though it's a text file, the content can still contain malicious scripts that execute when viewed.<br>
        <small><strong>Note:</strong> Files are automatically removed after successful completion or after 1 hour for security.</small>
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #007bff; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            💡 <strong>Hint:</strong> Create a .txt file with XSS payload content:<br><br>
            <strong>TXT file content examples:</strong><br>
            <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code><br>
            <code>&lt;img src=x onerror=alert('XSS')&gt;</code><br>
            <code>&lt;svg onload=alert('XSS')&gt;&lt;/svg&gt;</code><br><br>
            Save this content to a .txt file and upload it. The system will detect XSS patterns in the text content!
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    // HTML escape function for safe display
    function htmlEscape(str) {
        return str.replace(/&/g, '&amp;')
                  .replace(/</g, '&lt;')
                  .replace(/>/g, '&gt;')
                  .replace(/"/g, '&quot;')
                  .replace(/'/g, '&#39;');
    }

    function viewFile(filename) {
        const preview = document.getElementById('file-preview');
        const content = document.getElementById('file-content');

        // Fetch file content from secure directory
        fetch('safe_uploads/challenge_19/' + filename)
            .then(response => {
                if (!response.ok) {
                    throw new Error('File not found or access denied');
                }
                return response.text();
            })
            .then(data => {
                content.innerHTML = '<pre>' + htmlEscape(data) + '</pre>';
                preview.style.display = 'block';

                // Check for XSS patterns in the .txt file content
                if (data.includes('<script>') || data.includes('javascript:') ||
                    data.includes('onload=') || data.includes('onerror=') ||
                    data.includes('alert(') || data.includes('<img') || data.includes('<svg')) {

                    // This simulates the XSS execution for educational purposes
                    setTimeout(() => {
                        if (data.includes('alert(')) {
                            // Extract and execute alert for demonstration
                            const alertMatch = data.match(/alert\([^)]*\)/);
                            if (alertMatch) {
                                eval(alertMatch[0]);
                            }
                        }
                    }, 500);
                }
            })
            .catch(error => {
                content.innerHTML = 'Error loading file: ' + error.message;
                preview.style.display = 'block';
            });
    }

    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
