<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 17;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$search_term = "";
$filtered_input = "";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $search_term = $_POST['search'] ?? '';

    // Basic XSS filter (intentionally bypassable)
    $filtered_input = $search_term;
    $filtered_input = str_ireplace('<script>', '', $filtered_input);
    $filtered_input = str_ireplace('</script>', '', $filtered_input);
    $filtered_input = str_ireplace('javascript:', '', $filtered_input);
    $filtered_input = str_ireplace('alert', '', $filtered_input);
    $filtered_input = str_ireplace('onload', '', $filtered_input);
    $filtered_input = str_ireplace('onerror', '', $filtered_input);

    // Check for successful filter bypass
    if ($search_term !== $filtered_input &&
        (strpos(strtolower($search_term), 'script') !== false ||
         strpos(strtolower($search_term), 'javascript') !== false ||
         strpos(strtolower($search_term), 'alert') !== false ||
         strpos(strtolower($search_term), 'onload') !== false ||
         strpos(strtolower($search_term), 'onerror') !== false)) {

        // Check if they successfully bypassed the filter
        if (strpos(strtolower($filtered_input), 'script') !== false ||
            strpos(strtolower($filtered_input), 'javascript') !== false ||
            strpos(strtolower($filtered_input), 'alert') !== false ||
            strpos(strtolower($filtered_input), 'onload') !== false ||
            strpos(strtolower($filtered_input), 'onerror') !== false) {

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $message = "🎉 Challenge completed! You successfully bypassed the XSS filter!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'dom_advanced.php';
                }, 3000);
            </script>";


            // Redirect to next XSS challenge
            
        } else {
            $_SESSION[$session_key]++;
            $message = "Filter blocked your input. Try a different approach.";
        }
    } else {
        $_SESSION[$session_key]++;
        $message = "No XSS payload detected. Try injecting malicious code.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Secure Search Engine | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right, #c9d6ff, #e2e2e2);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .search-container {
            background: #ffffff;
            padding: 40px 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .security-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
            border-left: 4px solid #00b894;
        }
        label {
            text-align: left;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input[type="text"] {
            padding: 12px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .filter-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
        }
        .original-input {
            color: #dc3545;
            font-family: monospace;
        }
        .filtered-input {
            color: #28a745;
            font-family: monospace;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
    </style>
</head>
<body>

<div class="search-container">
    <h1>🔍 SecureSearch Engine</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🔍 Scenario:</strong> You're testing SecureSearch which has implemented XSS filters to block malicious scripts. The system removes common XSS patterns, but you need to find ways around these protections.
        <br><br>
        <strong>🎯 Objective:</strong> Bypass the XSS filters using encoding, case manipulation, or alternative event handlers to execute JavaScript.
    </div>

    <div class="security-info">
        <strong>🛡️ Security Features:</strong><br>
        • XSS Filter: Active<br>
        • Script Tag Blocking: Enabled<br>
        • JavaScript URL Blocking: Enabled<br>
        • Event Handler Blocking: Enabled
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'blocked') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <label for="search">Search Query:</label>
        <input type="text" name="search" id="search" placeholder="Enter your search term..." value="<?= htmlspecialchars($search_term) ?>" required>
        <button type="submit">Search</button>
    </form>

    <?php if ($search_term): ?>
        <div class="filter-result">
            <strong>Filter Processing:</strong><br>
            <strong>Original Input:</strong> <span class="original-input"><?= htmlspecialchars($search_term) ?></span><br>
            <strong>Filtered Output:</strong> <span class="filtered-input"><?= htmlspecialchars($filtered_input) ?></span><br><br>
            <strong>Search Results for:</strong> <?= $filtered_input ?>
        </div>
    <?php endif; ?>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Bypass the XSS filter to inject malicious JavaScript code. The filter removes common XSS patterns, but can you find a way around it?
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #007bff; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try these filter bypass techniques:<br><br>
            • <code>&lt;ScRiPt&gt;alert(1)&lt;/ScRiPt&gt;</code><br>
            • <code>&lt;scr&lt;script&gt;ipt&gt;alert(1)&lt;/script&gt;</code><br>
            • <code>&lt;script&gt;&#97;lert(1)&lt;/script&gt;</code><br>
            • <code>&lt;img src=x onerror=alert(1)&gt;</code><br>
            • <code>&lt;svg onload=alert(1)&gt;</code><br><br>
            These bypass the filter using case mixing, double encoding, or alternative tags!
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
