<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$challenge_id = 8;
$next_challenge_url = 'stored.php'; // Next challenge

$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$message = '';
$completed = false;
$comment = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $comment = $_POST['comment'] ?? '';

    // Insert the comment into the DB
    $stmt = $pdo->prepare("INSERT INTO xss_comments (user_id, challenge_id, comment) VALUES (?, ?, ?)");
    $stmt->execute([$_SESSION['user_id'], $challenge_id, $comment]);

    // Detect basic image onerror XSS
    if (preg_match('/<img\s+[^>]*onerror\s*=\s*["\']?alert\s*\([^)]*\)["\']?[^>]*>/i', $comment)) {
        $completed = true;

        // Mark challenge as completed
        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        // Clear all comments for this challenge once completed
        $stmt = $pdo->prepare("DELETE FROM xss_comments WHERE challenge_id = ?");
        $stmt->execute([$challenge_id]);

        $_SESSION[$session_key] = 0;
    } else {
        $_SESSION[$session_key]++;
        $message = "❌ That’s not quite right. Try using the <code>onerror</code> attribute!";
    }
}

// Get all comments for display
$stmt = $pdo->prepare("SELECT comment FROM xss_comments WHERE challenge_id = ? ORDER BY id DESC");
$stmt->execute([$challenge_id]);
$comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Stored Image XSS | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right, #c9d6ff, #e2e2e2);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #ffffff;
            padding: 40px 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #222;
            font-size: 28px;
            margin-bottom: 20px;
        }

        .error-message {
            color: #e74c3c;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .success-message {
            color: green;
            margin-top: 15px;
            font-size: 15px;
            font-weight: bold;
        }

        label {
            display: block;
            text-align: left;
            margin-bottom: 6px;
            font-size: 15px;
            color: #555;
        }

        textarea {
            padding: 12px 14px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 15px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            resize: vertical;
            min-height: 100px;
            box-sizing: border-box;
        }

        textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 6px rgba(0, 123, 255, 0.3);
            outline: none;
        }

        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:focus {
            outline: 2px dashed #0056b3;
            outline-offset: 2px;
        }

        .tip {
            margin-top: 15px;
            text-align: left;
            font-size: 14px;
            color: #333;
            background: #f9f9f9;
            padding: 10px 14px;
            border-left: 3px solid #007bff;
            border-radius: 4px;
        }

        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #f1f3f5;
            padding: 15px;
            border-left: 4px solid #007bff;
            border-radius: 6px;
            font-size: 14px;
        }

        .comment-box {
            margin-top: 30px;
        }

        .comment {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            text-align: left;
        }

        .comment strong {
            font-weight: bold;
        }

        button.secondary {
            background-color: #6c757d;
            width: auto;
            padding: 12px 25px;
            margin: 10px 5px;
        }

        button.secondary:hover {
            background-color: #5a6268;
        }

        button.hint-toggle {
            background-color: #8ea6b6;
            width: auto;
            padding: 12px 25px;
            margin: 10px 5px;
        }

        button.hint-toggle:hover {
            background-color: #708da2;
        }

        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
            font-weight: 600;
        }

    </style>
</head>
<body>
<div class="container">
    <h1>📸 PhotoShare Gallery</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>📸 Scenario:</strong> You're testing PhotoShare's image gallery feature. Users can add images by providing URLs, but the image URL input might be vulnerable to XSS through error events.
        <br><br>
        <strong>🎯 Objective:</strong> Use image error events to execute JavaScript code when an invalid image URL is processed.
    </div>

    <?php if ($message): ?>
        <div class="error-message"><?= $message ?></div>
    <?php endif; ?>

    <?php if ($completed): ?>
        <div class="success-message">🎉 Payload worked! Proceeding to next level...</div>
        <script>
            setTimeout(() => {
                window.location.href = "<?= $next_challenge_url ?>";
            }, 2500);
        </script>
    <?php endif; ?>

    <form method="POST">
        <label for="comment">Leave a comment:</label>
        <textarea name="comment" id="comment" required><?= htmlspecialchars($comment ?? '') ?></textarea>
        <button type="submit">Post Comment</button>
    </form>

    <div class="tip">
        💡 <strong>Hint:</strong> Try submitting an image tag with a fake source and an <code>onerror</code> event that triggers an alert.
    </div>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" class="hint-toggle">Stuck? Here's the solution</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these image onerror payloads:<br><br>
            • <code>&lt;img src=x onerror=alert(1)&gt;</code><br>
            • <code>&lt;img src="invalid" onerror="alert('XSS')"&gt;</code><br>
            • <code>&lt;img src=# onerror=alert(document.cookie)&gt;</code><br>
            • <code>&lt;img src="" onerror="alert('Hacked')"&gt;</code>
        </div>
    <?php endif; ?>

    <div class="comment-box">
        <h3>Recent Comments:</h3>
        <?php foreach ($comments as $row): ?>
            <div class="comment"><?= htmlspecialchars($row['comment']) ?></div>
        <?php endforeach; ?>
    </div>

    <button onclick="confirmBack()" class="secondary">Back to Dashboard</button>
</div>

<script>
function toggleHint() {
    const hintBox = document.getElementById('hint-box');
    hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
}

function confirmBack() {
    if (confirm("Return to dashboard?")) {
        window.location.href = '../../pages/dashboard.php';
    }
}
</script>
</body>
</html>
