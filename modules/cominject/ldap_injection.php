<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 22;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Simulated LDAP directory data
$ldap_users = [
    ['cn' => 'admin', 'mail' => '<EMAIL>', 'department' => 'IT', 'role' => 'administrator'],
    ['cn' => 'john.doe', 'mail' => '<EMAIL>', 'department' => 'Sales', 'role' => 'user'],
    ['cn' => 'jane.smith', 'mail' => '<EMAIL>', 'department' => 'HR', 'role' => 'user'],
    ['cn' => 'bob.wilson', 'mail' => '<EMAIL>', 'department' => 'IT', 'role' => 'user'],
    ['cn' => 'secret.user', 'mail' => '<EMAIL>', 'department' => 'Security', 'role' => 'secret']
];

$search_results = [];
$ldap_query = "";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $department = $_POST['department'] ?? '';

    // Construct vulnerable LDAP query (simulated)
    $ldap_query = "(&(cn=$username)(department=$department))";

    // Check for LDAP injection patterns
    if (strpos($username, '*') !== false || strpos($department, '*') !== false ||
        strpos($username, '(') !== false || strpos($department, '(') !== false ||
        strpos($username, ')') !== false || strpos($department, ')') !== false ||
        strpos($username, '|') !== false || strpos($department, '|') !== false ||
        strpos($username, '&') !== false || strpos($department, '&') !== false) {

        // Simulate LDAP injection success
        if (strpos($username, '*') !== false || strpos($department, '*') !== false) {
            // Wildcard injection - return all users
            $search_results = $ldap_users;
        } elseif (strpos($username, '|') !== false || strpos($department, '|') !== false) {
            // OR injection - return multiple users
            $search_results = array_slice($ldap_users, 0, 3);
        } elseif (strpos($username, ')') !== false || strpos($department, ')') !== false) {
            // Filter bypass - return admin or secret users
            $search_results = [$ldap_users[0], $ldap_users[4]]; // admin and secret user
        }

        if (!empty($search_results)) {
            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $message = "🎉 Challenge completed! You successfully performed LDAP injection!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = '../../pages/dashboard.php';
                }, 3000);
            </script>";


            // Redirect back to dashboard (last Command Injection challenge)
            
        }

    } else {
        // Normal LDAP search simulation
        foreach ($ldap_users as $user) {
            if (($username === '' || $user['cn'] === $username) &&
                ($department === '' || $user['department'] === $department)) {
                $search_results[] = $user;
            }
        }

        if (empty($search_results)) {
            $_SESSION[$session_key]++;
            $message = "No users found matching the criteria.";
        } else {
            $message = "Search completed. Found " . count($search_results) . " user(s).";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>LDAP Directory Search | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .ldap-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 700px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .directory-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
            border-left: 4px solid #ffc107;
        }
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input[type="text"] {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .query-display {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .results-table th, .results-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #007bff;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
    </style>
</head>
<body>

<div class="ldap-container">
    🏢 CorporateLDAP Directory Search</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🏢 Scenario:</strong> You're testing CorporateLDAP's directory search functionality for employee information. LDAP queries might be vulnerable to injection attacks using LDAP operators.
        <br><br>
        <strong>🎯 Objective:</strong> Manipulate LDAP search filters using operators like *, |, & to access unauthorized directory information.
    </div>

    <div class="directory-info">
        <strong>📁 Directory Information:</strong><br>
        • Server: ldap://company.local<br>
        • Base DN: ou=users,dc=company,dc=local<br>
        • Available attributes: cn, mail, department, role<br>
        • Search scope: Subtree
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'No users found') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <div class="search-section">
        <h3>User Search</h3>
        <form method="POST">
            <label for="username">Username (cn):</label>
            <input type="text" name="username" id="username" placeholder="e.g., john.doe">

            <label for="department">Department:</label>
            <input type="text" name="department" id="department" placeholder="e.g., IT, Sales, HR">

            <button type="submit">Search Directory</button>
        </form>
    </div>

    <?php if ($ldap_query): ?>
        <div class="query-display">
            <strong>LDAP Query:</strong> <?= htmlspecialchars($ldap_query) ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($search_results)): ?>
        <table class="results-table">
            <thead>
                <tr>
                    <th>Username (cn)</th>
                    <th>Email</th>
                    <th>Department</th>
                    <th>Role</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($search_results as $user): ?>
                    <tr>
                        <td><?= htmlspecialchars($user['cn']) ?></td>
                        <td><?= htmlspecialchars($user['mail']) ?></td>
                        <td><?= htmlspecialchars($user['department']) ?></td>
                        <td><?= htmlspecialchars($user['role']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #bbdefb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Exploit LDAP injection to bypass search filters and access unauthorized user information. Try to retrieve all users or specific sensitive accounts.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #007bff; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these LDAP injection techniques:<br><br>
            • <code>*</code> (in username or department field)<br>
            • <code>admin)(|(department=*</code><br>
            • <code>*)(department=*</code><br>
            • <code>admin)(&(department=*</code><br>
            • <code>*)(cn=*</code><br><br>
            These use LDAP operators and wildcards to bypass authentication filters.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
