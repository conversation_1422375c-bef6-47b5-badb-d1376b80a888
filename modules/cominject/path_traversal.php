<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 20;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$file_content = "";
$requested_file = "";

// Create some sample files for the challenge
$safe_dir = __DIR__ . '/safe_files/';
if (!is_dir($safe_dir)) {
    mkdir($safe_dir, 0777, true);
    file_put_contents($safe_dir . 'readme.txt', "Welcome to the file viewer!\nThis is a safe file in the safe directory.");
    file_put_contents($safe_dir . 'info.txt', "System Information:\nServer: Apache 2.4\nPHP Version: 8.0\nOS: Linux");
    file_put_contents($safe_dir . 'secret.txt', "PATH_TRAVERSAL_SUCCESS_FLAG\nCongratulations! You found the secret file!");
}

// Handle file request
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['file'])) {
    $requested_file = $_GET['file'];

    // Vulnerable path construction (intentionally vulnerable to path traversal)
    $file_path = $safe_dir . $requested_file;

    if (file_exists($file_path)) {
        $file_content = file_get_contents($file_path);

        // Check for successful path traversal
        if (strpos($file_content, 'PATH_TRAVERSAL_SUCCESS_FLAG') !== false ||
            strpos($file_content, 'root:') !== false ||
            strpos($file_content, '[boot loader]') !== false ||
            strpos($requested_file, '../') !== false ||
            strpos($requested_file, '..\\') !== false) {

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $message = "🎉 Challenge completed! You successfully performed path traversal attack!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'code_injection.php';
                }, 3000);
            </script>";


            // Redirect to next Command Injection challenge
            
        } else {
            $message = "File loaded successfully.";
        }
    } else {
        $_SESSION[$session_key]++;
        $message = "File not found: " . htmlspecialchars($requested_file);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Secure Files Document Viewer | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .viewer-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 700px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .security-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
            border-left: 4px solid #00b894;
        }
        .file-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input[type="text"] {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .file-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .available-files {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
    </style>
</head>
<body>

<div class="viewer-container">
    🗂️ SecureFiles Document Viewer</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🗂️ Scenario:</strong> You're testing SecureFiles' document viewer which restricts access to a safe directory. However, the path construction might be vulnerable to directory traversal attacks.
        <br><br>
        <strong>🎯 Objective:</strong> Use path traversal sequences (../) to access files outside the intended safe directory.
    </div>

    <div class="security-info">
        <strong>🛡️ Security Features:</strong><br>
        • File access restricted to safe directory<br>
        • Path validation: Active<br>
        • Directory traversal protection: Enabled<br>
        • File type filtering: Enabled
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'not found') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <div class="file-section">
        <h3>File Viewer</h3>
        <form method="GET">
            <label for="file">Enter filename to view:</label>
            <input type="text" name="file" id="file" placeholder="e.g., welcome.txt" value="<?= htmlspecialchars($requested_file) ?>" required>
            <button type="submit">View File</button>
        </form>
    </div>

    <div class="available-files">
        <strong>📋 Available Files in Safe Directory:</strong><br>
        • ******.txt - Welcome information<br>
        • ****.txt - System information<br>
        • ******.txt - Hidden file ?
    </div>

    <?php if ($file_content): ?>
        <div class="file-content">
            <strong>File Content (<?= htmlspecialchars($requested_file) ?>):</strong><br><br>
            <?= htmlspecialchars($file_content) ?>
        </div>
    <?php endif; ?>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #bbdefb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Use path traversal techniques to access files outside the safe directory. Try to read system files or the secret file using directory traversal.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #007bff; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try these path traversal techniques:<br><br>
            • <code>../secret.txt</code><br>
            • <code>../../etc/passwd</code><br>
            • <code>../../../windows/system32/drivers/etc/hosts</code><br>
            • <code>....//....//secret.txt</code><br>
            • <code>..%2fsecret.txt</code><br><br>
            These escape the safe directory to access files elsewhere!
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
