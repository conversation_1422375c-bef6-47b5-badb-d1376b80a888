<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$challenge_id = 11;
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$completed = false;
$message = '';
$output = '';

// Check if the user has failed 10 times
if ($_SESSION[$session_key] >= 10) {
    $hint_message = "✅ <strong>Working Solutions:</strong> Try any of these command injection payloads:<br><br>
    • <code>127.0.0.1 && cat /etc/passwd</code><br>
    • <code>127.0.0.1; type C:\\Windows\\System32\\drivers\\etc\\hosts</code><br>
    • <code>127.0.0.1 | cat /etc/shadow</code><br>
    • <code>127.0.0.1; ls -la /</code>";
} else {
    $hint_message = "";
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get raw page input (INTENTIONALLY VULNERABLE)
    $page = $_GET['page'] ?? '';

    // If empty, show nothing
    if ($page === '') {
        $message = "❌ Please enter a page.";
    } else {
        // Try to include the requested file
        ob_start();
        @include($page); // This is the LFI vulnerability (raw include)
        $output = ob_get_clean();

        // Detect whether the user included a sensitive file or the correct secret file
        if (
            strpos($output, 'root:') !== false ||
            strpos($output, '[boot loader]') !== false ||
            strpos($output, 'localhost') !== false ||
            strpos($output, 'LFI_SUCCESS_FLAG') !== false
        ) {
            $completed = true;

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $_SESSION['redirect'] = true;
            $_SESSION['redirect_time'] = time() + 3;
        } elseif (!file_exists($page)) {
            $_SESSION[$session_key]++;
            $message = "❌ That file doesn't exist. Try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Local File Inclusion | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #eef1f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            width: 500px;
            text-align: center;
        }

        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 6px;
            text-align: left;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .success-message {
            color: green;
            font-weight: bold;
        }

        .error-message {
            color: red;
            background: #ffe5e5;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        button[type="button"]:hover {
            background: #0069d9;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>📄 WebDocs File Viewer</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>📄 Scenario:</strong> You're testing WebDocs' file viewer which allows users to view documentation files through a web interface. The file parameter might allow command injection or path traversal.
        <br><br>
        <strong>🎯 Objective:</strong> Access files outside the intended directory or execute system commands through the file parameter.
    </div>
    <p>Try to read a sensitive file using the URL. Can you find the contents of a critical system file or a hidden password?</p>

    <?php if ($message): ?>
        <div class="error-message"><?= $message ?></div>
    <?php elseif ($completed): ?>
        <p class="success-message">✅ Challenge completed! Redirecting...</p>
    <?php endif; ?>

    <form method="GET">
        <input type="text" name="page" placeholder="you're in pages directory" required>
        <button type="submit">Load Page</button>
    </form>

    <?php if ($output): ?>
        <h3>File Output:</h3>
        <pre><?= htmlspecialchars($output) ?></pre>
    <?php endif; ?>

    <?php if ($_SESSION[$session_key] >= 10 && !$completed): ?>
        <button onclick="toggleHint()" style="margin-top: 20px;">Need a Hint?</button>
        <div id="hint" style="display:none; margin-top: 10px;">
            <?= $hint_message ?>
        </div>
    <?php endif; ?>

    <a href="../../pages/dashboard.php" 
        onclick="return confirm('⚠️ Are you sure you want to go back? Any unsolved progress for this challenge will not be recorded.');" 
        style="display:inline-block; margin-top: 30px; text-decoration: none;">
        <button type="button" style="background: #6c757d;">Back to Dashboard</button>
    </a>
</div>

<script>
    function toggleHint() {
        const hint = document.getElementById("hint");
        hint.style.display = hint.style.display === "none" ? "block" : "none";
    }

    // Redirect if completed
    if (<?php echo isset($_SESSION['redirect']) ? 'true' : 'false'; ?>) {
        setTimeout(() => {
            alert("Redirecting to the next challenge...");
            window.location.href = "reverse_shell.php";
        }, 3000);
    }
</script>
</body>
</html>
