<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 21;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$calculation_result = "";
$user_input = "";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_input = $_POST['expression'] ?? '';

    // Vulnerable eval() usage (intentionally dangerous)
    if (!empty($user_input)) {
        try {
            // This is extremely dangerous - never do this in real applications!
            ob_start();
            $result = eval("return $user_input;");
            $output = ob_get_clean();

            $calculation_result = $result;

            // Check for successful code injection
            if (strpos($user_input, 'phpinfo') !== false ||
                strpos($user_input, 'system') !== false ||
                strpos($user_input, 'exec') !== false ||
                strpos($user_input, 'shell_exec') !== false ||
                strpos($user_input, 'file_get_contents') !== false ||
                strpos($user_input, 'echo') !== false ||
                strpos($user_input, 'print') !== false) {

                $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
                $stmt->execute([$_SESSION['user_id'], $challenge_id]);

                $_SESSION[$session_key] = 0;
                $message = "🎉 Challenge completed! You successfully executed PHP code injection!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'ldap_injection.php';
                }, 3000);
            </script>";


                // Redirect to next Command Injection challenge
                
            } else {
                $message = "Calculation completed: " . htmlspecialchars($calculation_result);
            }

        } catch (ParseError $e) {
            $_SESSION[$session_key]++;
            $message = "Parse Error: " . $e->getMessage();
        } catch (Error $e) {
            $_SESSION[$session_key]++;
            $message = "Error: " . $e->getMessage();
        } catch (Exception $e) {
            $_SESSION[$session_key]++;
            $message = "Exception: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>PHP Calculator | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .calculator-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .app-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
            border-left: 4px solid #6c5ce7;
        }
        .calc-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input[type="text"] {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            font-family: monospace;
        }
        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #007bff;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #007bff;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #007bff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .result-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
            font-family: monospace;
            border: 1px solid #dee2e6;
        }
        .examples {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: left;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #007bff;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #007bff;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #007bff;
        }
    </style>
</head>
<body>

<div class="calculator-container">
    🧮 MathCalc Expression Evaluator</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🧮 Scenario:</strong> You're testing MathCalc's expression evaluator which uses PHP's eval() function to process mathematical expressions. This is extremely dangerous and allows arbitrary code execution.
        <br><br>
        <strong>🎯 Objective:</strong> Inject PHP code instead of mathematical expressions to execute system commands.
    </div>

    <div class="app-info">
        <strong>📱 Application Info:</strong><br>
        • Dynamic PHP expression evaluator<br>
        • Supports mathematical operations<br>
        • Real-time calculation processing<br>
        • Advanced expression parsing
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Error') !== false || strpos($message, 'Exception') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <div class="calc-section">
        <h3>Expression Calculator</h3>
        <form method="POST">
            <label for="expression">Enter mathematical expression:</label>
            <input type="text" name="expression" id="expression" placeholder="e.g., 2 + 3 * 4" value="<?= htmlspecialchars($user_input) ?>" required>
            <button type="submit">Calculate</button>
        </form>
    </div>

    <?php if ($calculation_result !== ""): ?>
        <div class="result-display">
            <strong>Result:</strong> <?= htmlspecialchars($calculation_result) ?>
        </div>
    <?php endif; ?>

    <div class="examples">
        <strong>📝 Example Expressions:</strong><br>
        • <code>2 + 3 * 4</code> - Basic arithmetic<br>
        • <code>sqrt(16)</code> - Square root function<br>
        • <code>pow(2, 3)</code> - Power function<br>
        • <code>pi() * 2</code> - Using constants
    </div>

    <div style="margin-top: 15px; text-align: left; background-color: #007bff; padding: 15px; border: 1px solid #ffcccc; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Exploit PHP code injection vulnerability. The calculator uses eval() to process expressions - can you execute arbitrary PHP code?
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #007bff; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these PHP code injection payloads:<br><br>
            • <code>phpinfo()</code><br>
            • <code>system('whoami')</code><br>
            • <code>file_get_contents('/etc/passwd')</code><br>
            • <code>echo "Hello World"</code><br>
            • <code>exec('ls -la')</code><br><br>
            The eval() function will execute any valid PHP code you provide!
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
