<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 13;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Create dummy tables for the challenge if they don't exist
try {
    // Create dummy employees table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS challenge_employees (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id VARCHAR(10) UNIQUE,
            name VARCHAR(100),
            department VARCHAR(50),
            salary DECIMAL(10,2),
            email VARCHAR(100),
            phone VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");

    // Insert dummy data if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM challenge_employees");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("
            INSERT INTO challenge_employees (employee_id, name, department, salary, email, phone) VALUES
            ('EMP001', 'John Smith', 'IT', 75000.00, '<EMAIL>', '555-0101'),
            ('EMP002', 'Sarah Johnson', 'HR', 65000.00, '<EMAIL>', '555-0102'),
            ('EMP003', 'Mike Davis', 'Finance', 80000.00, '<EMAIL>', '555-0103'),
            ('EMP004', 'Lisa Wilson', 'Marketing', 70000.00, '<EMAIL>', '555-0104'),
            ('EMP005', 'Admin User', 'IT', 95000.00, '<EMAIL>', '555-0001'),
            ('SECRET', 'Database Admin', 'IT', 120000.00, '<EMAIL>', '555-9999')
        ");
    }
} catch (Exception $e) {
    // Silently handle table creation errors
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employee_id = $_POST['employee_id'];

    // Vulnerable SQL query that will produce errors
    try {
        $query = "SELECT employee_id, name, department FROM challenge_employees WHERE employee_id = '$employee_id'";
        $stmt = $pdo->query($query);
        $employee = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($employee) {
            $message = "Employee found: " . htmlspecialchars($employee['name']) . " (" . htmlspecialchars($employee['department']) . ")";
        } else {
            $message = "No employee found with ID: " . htmlspecialchars($employee_id);
        }

        // Check if they successfully extracted database information through normal query
        if (strpos($employee_id, 'version') !== false ||
            strpos($employee_id, 'database') !== false ||
            strpos($employee_id, 'user') !== false ||
            strpos($employee_id, 'extractvalue') !== false ||
            strpos($employee_id, 'updatexml') !== false) {

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            // Reset fail count on success
            $_SESSION[$session_key] = 0;

            $message = "🎉 Challenge completed! You successfully used error-based SQL injection to extract database information.";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'time_based_blind.php';
                }, 3000);
            </script>";
        }

    } catch (PDOException $e) {
        $_SESSION[$session_key]++;
        $error_message = $e->getMessage();
        $message = "Database Error: " . htmlspecialchars($error_message);

        // Check if they're trying to extract information through errors
        if (strpos($error_message, 'version') !== false ||
            strpos($error_message, 'database') !== false ||
            strpos($error_message, 'user') !== false ||
            strpos($error_message, '~') !== false ||  // Common in extractvalue/updatexml errors
            strpos($error_message, 'XPATH') !== false) {

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $message = "🎉 Challenge completed! You successfully used error-based SQL injection. Error revealed: " . htmlspecialchars($error_message);

            // Redirect to next SQL challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'time_based_blind.php';
                }, 3000);
            </script>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Employee Directory System | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .lookup-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            text-align: left;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input {
            padding: 12px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #fff3cd;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #6c757d;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #5a6268;
        }
        .available-employees {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            font-size: 14px;
        }
    </style>
</head>
<body>

<div class="lookup-container">
    <h1>🏢 Employee Directory System</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🏢 Scenario:</strong> You're testing the company's employee directory system. The application shows detailed error messages when database queries fail, which might reveal sensitive information about the database structure and data.
        <br><br>
        <strong>🎯 Objective:</strong> Use error-based SQL injection to extract database information through error messages.
    </div>

    <div class="system-info">
        <strong>System Information:</strong><br>
        Database: MySQL 8.0<br>
        Application: Employee Directory v1.5<br>
        Status: Online
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Error') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <label for="employee_id">Enter Employee ID to lookup:</label>
        <input type="text" name="employee_id" id="employee_id" placeholder="e.g., EMP??..." required>
        <button type="submit">Lookup Employee</button>
    </form>

    <div class="available-employees">
        <strong>📋 Available Employee IDs:</strong><br>
        EMP***, EMP***, EMP***, EMP***, EMP***
    </div>

    <div style="margin-top: 15px; text-align: left; background-color: #e3f2fd; padding: 15px; border: 1px solid #bbdefb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Use error-based SQL injection to extract database information. Try to make the database reveal sensitive information through error messages.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #ffc107; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            💡 <strong>Hint:</strong> Try injecting SQL functions that might cause errors and reveal information:<br><br>
            • <code>EMP001' AND extractvalue(1, concat(0x7e, version(), 0x7e)) --</code><br>
            • <code>EMP001' AND updatexml(1, concat(0x7e, database(), 0x7e), 1) --</code><br>
            • <code>EMP001' AND (SELECT * FROM (SELECT COUNT(*),concat(user(),floor(rand(0)*2))x FROM information_schema.tables GROUP BY x)a) --</code><br><br>
            These payloads force the database to generate errors that contain sensitive information like version, database name, or current user.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
