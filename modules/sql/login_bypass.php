<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {

    exit;
}

$message = "";
$challenge_id = 4;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Vulnerable SQL query for testing
    $query = "SELECT * FROM challenge_users WHERE username = '$username' AND password = '$password' LIMIT 1";

    $stmt = $pdo->query($query);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        // Reset fail count on success
        $_SESSION[$session_key] = 0;

        $message = "✅ Challenge completed successfully!";

        // Redirect to next SQL challenge
        echo "<script>
            setTimeout(() => {
                window.location.href = 'union_select.php';
            }, 3000);
        </script>";

        return;
    } else {
        $_SESSION[$session_key]++;
        $message = "❌ Incorrect username or password. Try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Login - Secure Access | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 120px;
            margin-bottom: 20px;
        }
        label {
            text-align: left;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
        }
        input {
            padding: 12px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .error-message {
            color: red;
            margin-bottom: 15px;
            font-size: 14px;
        }
        footer {
            margin-top: 20px;
            font-size: 14px;
            color: #777;
        }
        footer a {
            color: #007bff;
            text-decoration: none;
        }
        footer a:hover {
            text-decoration: underline;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>

<div class="login-container">
    <img src="https://logos-world.net/wp-content/uploads/2023/02/World-Bank-Logo-1944.png" alt="Bank Logo" class="logo">
    <h1>Secure Bank Login</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🏦 Scenario:</strong> You're a security tester hired by SecureBank Corp. Their login system claims to be "unhackable" but you suspect it's vulnerable to SQL injection. Your mission: bypass the authentication without knowing any valid credentials.
        <br><br>
        <strong>🎯 Objective:</strong> Login as any user without valid credentials using SQL injection techniques.
    </div>

    <?php if ($message): ?>
        <p class="error-message"><?= htmlspecialchars($message) ?></p>
    <?php endif; ?>

    <form method="POST">
        <label for="username">Username</label>
        <input type="text" name="username" id="username" required>

        <label for="password">Password</label>
        <input type="password" name="password" id="password" required>

        <button type="submit">Login</button>
    </form>

    <footer>
        <p>Forgot your password? <a href="#">Click here</a> to reset it.</p>
    </footer>

    <div id="box" style="margin-top: 15px; text-align: left; background-color: #f8f9fa; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
        🕵️‍♂️ <strong>Tip:</strong> What if you could trick the database into always returning true? Explore how SQL conditions work and consider how operators like <code>OR</code> might change the outcome.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="margin-top: 20px; background-color:rgb(142, 166, 182);">Stuck? Here's the solution</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try entering any of these as the username (leave password blank):<br><br>
            • <code>admin' OR '1'='1</code><br>
            • <code>admin' OR 1=1 --</code><br>
            • <code>' OR '1'='1' --</code><br>
            • <code>admin'/**/OR/**/1=1#</code><br><br>
            These payloads make the SQL condition always true, bypassing authentication.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" style="margin-top: 10px; background-color: #6c757d;">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
