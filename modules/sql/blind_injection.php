<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 6; // Challenge ID for SQL Injection 3 (Blind Injection)

// Check if the challenge exists
$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$success = false; // Initialize success status to false

// Track failed attempts
if (!isset($_SESSION['blind_sql_attempts'])) {
    $_SESSION['blind_sql_attempts'] = 0;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_answer = $_POST['answer'];

    // Vulnerable query for blind SQL injection (quotes added)
    $query = "SELECT * FROM challenge_users WHERE username = '$user_answer' AND 1=1 LIMIT 1";

    try {
        $stmt = $pdo->query($query);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Reset attempt count on success
            $_SESSION['blind_sql_attempts'] = 0;

            // Mark challenge as completed
            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $message = "✅ Congratulations! You've successfully completed this challenge!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'error_based_injection.php';
                }, 3000);
            </script>";

            $success = true;

            // Redirect to next SQL challenge
            
        } else {
            $_SESSION['blind_sql_attempts']++;
            $message = "❌ Incorrect. Try again.";
        }
    } catch (PDOException $e) {
        $message = "SQL Error: " . $e->getMessage(); // Show SQL error for educational purposes
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Blind Injection | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .challenge-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        .challenge-container h1 {
            font-size: 24px;
            color: #333;
        }
        .challenge-container p {
            font-size: 16px;
            color: #666;
        }
        .challenge-container form {
            margin-top: 20px;
        }
        .challenge-container input[type="text"] {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        .challenge-container button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        .challenge-container button:hover {
            background-color: #0056b3;
        }
        .error-message {
            color: #dc3545;
            margin-top: 15px;
        }
        .logo {
            display: block;
            margin: 0 auto 30px;
            width: 120px;
        }
        .success-message {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .back-to-dashboard {
            margin-top: 20px;
        }
        .back-to-dashboard button {
            background-color: #007bff;
            color: white;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="challenge-container">
        <!-- Logo Section -->
        <img src="https://st4.depositphotos.com/5040187/19756/v/450/depositphotos_197569290-stock-illustration-logo-swoosh-global-red-letter.jpg" alt="Logo" class="logo">

        <h1>SecureNews Article Search</h1>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
            <strong>📰 Scenario:</strong> You're testing SecureNews's article search system. The application doesn't show database errors or results directly, but you suspect it's vulnerable to blind SQL injection. You need to extract information by asking the database true/false questions.
            <br><br>
            <strong>🎯 Objective:</strong> Use blind SQL injection techniques to extract the admin password character by character.
        </div>

        <?php if ($success): ?>
            <div class="success-message">
                <p><?= htmlspecialchars($message) ?></p>
            </div>
            <div class="back-to-dashboard">
                <a href="../../pages/dashboard.php">
                    <button>Go to Dashboard</button>
                </a>
            </div>
        <?php elseif ($message): ?>
            <div class="error-message">
                <p><?= htmlspecialchars($message) ?></p>
            </div>
        <?php endif; ?>

        <?php if (!$success): ?>
        <form method="POST">
            <label for="answer">Search Employee:</label><br>
            <input type="text" name="answer" id="answer" placeholder="Enter name" required><br>
            <button type="submit">Submit</button>
        </form>
        <?php endif; ?>

        <!-- Tips Section -->
        <div id="box" style="margin-top: 15px; text-align: left; background-color: #f8f9fa; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
            Table: challenge_users<br>
            username: admin<br><br>
            🕵️‍♂️ <strong>Tips:</strong> Imagine you're trying to extract data without seeing it directly. Can you ask the database a **true/false** question about a specific character in the admin's password? Start by checking if the first character equals something like <code>'a'</code>.
        </div>

        <!-- Conditional Solution Section -->
        <?php if ($_SESSION['blind_sql_attempts'] >= 10): ?>
            <button onclick="toggleHint()" style="margin-top: 20px; background-color:rgb(142, 166, 182);">Stuck? Show Solution</button>
            <div id="hint-box" style="display:none; margin-top: 15px; text-align: left; background-color: #f8f9fa; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
                ✅ <strong>Working Solutions:</strong> Try any of these blind SQL injection payloads:<br><br>
                • <code>admin' AND SUBSTRING((SELECT password FROM challenge_users WHERE username='admin'),1,1)='a' --</code><br>
                • <code>admin' AND ASCII(SUBSTRING((SELECT password FROM challenge_users WHERE username='admin'),1,1))=97 --</code><br>
                • <code>admin' AND (SELECT password FROM challenge_users WHERE username='admin') LIKE 'a%' --</code><br><br>
                These check if the first character of admin's password is 'a'. Success/failure tells you if the condition is true.
            </div>
        <?php endif; ?>

        <button onclick="confirmBack()" style="margin-top: 10px; background-color: #6c757d;">Back to Dashboard</button>
    </div>

    <script>
        function toggleHint() {
            const hintBox = document.getElementById('hint-box');
            hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
        }

        function confirmBack() {
            if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
                window.location.href = '../../pages/dashboard.php';
            }
        }
    </script>
</body>
</html>
