<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 16;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Simulate NoSQL-like behavior with JSON
$users_data = [
    ['username' => 'admin', 'password' => 'admin123', 'role' => 'administrator'],
    ['username' => 'user', 'password' => 'user123', 'role' => 'user'],
    ['username' => 'guest', 'password' => 'guest123', 'role' => 'guest'],
    ['username' => 'test', 'password' => 'test123', 'role' => 'user']
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Simulate NoSQL injection vulnerability
    // In real NoSQL, this would be like: db.users.find({username: username, password: password})

    $found_user = null;

    // Check for NoSQL injection patterns
    if (strpos($username, '$ne') !== false || strpos($password, '$ne') !== false ||
        strpos($username, '$regex') !== false || strpos($password, '$regex') !== false ||
        strpos($username, '$gt') !== false || strpos($password, '$gt') !== false ||
        strpos($username, '$exists') !== false || strpos($password, '$exists') !== false) {

        // Simulate successful NoSQL injection bypass
        $found_user = $users_data[0]; // Return admin user

        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        $_SESSION[$session_key] = 0;
        $message = "🎉 Challenge completed! You successfully bypassed NoSQL authentication using injection techniques!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = '../../pages/dashboard.php';
                }, 3000);
            </script>";


        // Redirect back to dashboard (last SQL challenge)
        

    } else {
        // Normal authentication check
        foreach ($users_data as $user) {
            if ($user['username'] === $username && $user['password'] === $password) {
                $found_user = $user;
                break;
            }
        }

        if ($found_user) {
            $message = "Login successful! Welcome " . htmlspecialchars($found_user['username']) . " (" . htmlspecialchars($found_user['role']) . ")";
        } else {
            $_SESSION[$session_key]++;
            $message = "Invalid username or password. Try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>NoSQL Database Login | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .login-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            text-align: left;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input {
            padding: 12px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #fff3cd;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #6c757d;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #5a6268;
        }
        .code-example {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="login-container">
    <h1>🍃 ModernAuth Login Portal</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🔐 Scenario:</strong> You're testing ModernAuth's login system which uses a NoSQL database for authentication. Traditional SQL injection won't work here, but NoSQL databases have their own injection vulnerabilities using operators and JSON syntax.
        <br><br>
        <strong>🎯 Objective:</strong> Bypass authentication using NoSQL injection operators like $ne, $regex, or $gt.
    </div>

    <div class="system-info">
        <strong>System Information:</strong><br>
        Database: MongoDB-like NoSQL<br>
        Authentication: JSON-based queries<br>
        Status: Online
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Invalid') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <label for="username">Username:</label>
        <input type="text" name="username" id="username" placeholder="Enter username" required>

        <label for="password">Password:</label>
        <input type="password" name="password" id="password" placeholder="Enter password" required>

        <button type="submit">Login</button>
    </form>

    <div style="margin-top: 15px; text-align: left; background-color: #e3f2fd; padding: 15px; border: 1px solid #bbdefb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Bypass NoSQL authentication using injection techniques. Think about how NoSQL operators work differently from SQL.
    </div>

    <div class="code-example">
        <strong>Query Structure:</strong><br>
        db.users.find({username: "input", password: "input"})
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #ffc107; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these NoSQL operators:<br><br>
            • Username: <code>{"$ne": null}</code> Password: <code>{"$ne": null}</code><br>
            • Username: <code>{"$regex": ".*"}</code> Password: <code>{"$regex": ".*"}</code><br>
            • Username: <code>{"$gt": ""}</code> Password: <code>{"$gt": ""}</code><br>
            • Username: <code>{"$exists": true}</code> Password: <code>{"$exists": true}</code><br><br>
            These operators bypass authentication by making the query always return true.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
