<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 14;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

$start_time = microtime(true);
$response_time = 0;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $product_id = $_POST['product_id'];

    // Vulnerable SQL query for time-based blind injection
    try {
        $query = "SELECT name, price FROM products WHERE id = '$product_id'";
        $stmt = $pdo->query($query);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        $end_time = microtime(true);
        $response_time = round(($end_time - $start_time) * 1000, 2);

        if ($product) {
            $message = "Product found: " . htmlspecialchars($product['name']) . " - $" . htmlspecialchars($product['price']);
        } else {
            $message = "No product found with ID: " . htmlspecialchars($product_id);
        }

        // Check if response took longer than 3 seconds (indicating successful time-based injection)
        if ($response_time > 3000) {
            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            $_SESSION[$session_key] = 0;
            $message = "🎉 Challenge completed! You successfully used time-based blind SQL injection. Response time: {$response_time}ms";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'second_order_injection.php';
                }, 3000);
            </script>";


            // Redirect to next SQL challenge
            
        }

    } catch (PDOException $e) {
        $_SESSION[$session_key]++;
        $message = "Database Error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Product Search System | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .search-container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            text-align: left;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input {
            padding: 12px;
            margin-bottom: 20px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #fff3cd;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #6c757d;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #5a6268;
        }
        .response-time {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<div class="search-container">
    <h1>🛍️ TechMart Product Search</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>🛒 Scenario:</strong> You're testing TechMart's product search system. The application seems to filter results properly, but you suspect it might be vulnerable to time-based blind SQL injection. Your goal is to confirm this vulnerability by making the database response significantly slower.
        <br><br>
        <strong>🎯 Objective:</strong> Inject SQL code that causes the database to delay its response by more than 3 seconds.
    </div>

    <div class="system-info">
        <strong>System Information:</strong><br>
        Database: MySQL 8.0<br>
        Products Available: 1-10<br>
        Status: Online
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Error') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <?php if ($response_time > 0): ?>
        <div class="response-time">Response time: <?= $response_time ?>ms</div>
    <?php endif; ?>

    <form method="POST">
        <label for="product_id">Enter Product ID:</label>
        <input type="text" name="product_id" id="product_id" placeholder="e.g., 1, 2, 3..." required>
        <button type="submit">Search Product</button>
    </form>

    <div style="margin-top: 15px; text-align: left; background-color: #e8f4fd; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Use time-based blind SQL injection to make the database response take longer than 3 seconds. Monitor the response time!
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #ffc107; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try any of these time-based payloads:<br><br>
            • <code>1' AND SLEEP(5) --</code><br>
            • <code>1'; WAITFOR DELAY '00:00:05' --</code><br>
            • <code>1' AND (SELECT * FROM (SELECT(SLEEP(5)))a) --</code><br>
            • <code>1' AND IF(1=1, SLEEP(5), 0) --</code><br><br>
            These payloads will make the database wait for 5+ seconds before responding.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
