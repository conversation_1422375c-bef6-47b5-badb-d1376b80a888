<?php
session_start();
require __DIR__ . '../../../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    
    exit;
}

$message = "";
$challenge_id = 15;

// Track failed attempts in session
$session_key = 'fail_count_chal_' . $challenge_id;
if (!isset($_SESSION[$session_key])) {
    $_SESSION[$session_key] = 0;
}

$checkChallengeStmt = $pdo->prepare("SELECT id FROM challenges WHERE id = ?");
$checkChallengeStmt->execute([$challenge_id]);
$challengeExists = $checkChallengeStmt->fetch(PDO::FETCH_ASSOC);

if (!$challengeExists) {
    echo "Challenge not found in the database!";
    exit;
}

// Handle registration
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {
    $username = $_POST['username'];
    $email = $_POST['email'];

    // Store user data (vulnerable to second-order injection)
    try {
        $stmt = $pdo->prepare("INSERT INTO temp_users (username, email) VALUES (?, ?)");
        $stmt->execute([$username, $email]);
        $message = "User registered successfully! You can now view your profile.";
    } catch (PDOException $e) {
        $message = "Registration failed: " . $e->getMessage();
    }
}

// Handle profile view (second-order injection happens here)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['view_profile'])) {
    $username = $_POST['profile_username'];

    try {
        // First query - get user data (this is where stored malicious data gets executed)
        $stmt = $pdo->prepare("SELECT username, email FROM temp_users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Second query - vulnerable to second-order injection
            $query = "SELECT * FROM temp_users WHERE email = '" . $user['email'] . "'";
            $result = $pdo->query($query);
            $profile_data = $result->fetchAll(PDO::FETCH_ASSOC);

            $message = "Profile loaded for: " . htmlspecialchars($user['username']);

            // Check if second-order injection was successful
            if (count($profile_data) > 1 || (isset($profile_data[0]) && $profile_data[0]['username'] !== $user['username'])) {
                $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
                $stmt->execute([$_SESSION['user_id'], $challenge_id]);

                $_SESSION[$session_key] = 0;
                $message = "🎉 Challenge completed! You successfully exploited second-order SQL injection!";
            // Redirect to next challenge
            echo "<script>
                setTimeout(() => {
                    window.location.href = 'nosql_injection.php';
                }, 3000);
            </script>";


                // Redirect to next SQL challenge
                
            }
        } else {
            $_SESSION[$session_key]++;
            $message = "User not found: " . htmlspecialchars($username);
        }

    } catch (PDOException $e) {
        $_SESSION[$session_key]++;
        $message = "Database Error: " . $e->getMessage();
    }
}

// Create temp table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS temp_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100),
        email VARCHAR(255)
    )");
} catch (PDOException $e) {
    // Table might already exist
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>User Registration System | TryMeOut</title>
    <link rel="icon" type="image/png" href="../../assets/images/logo-clear.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #e5e5e5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        input {
            padding: 12px;
            margin-bottom: 15px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            padding: 12px;
            width: 100%;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            word-wrap: break-word;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #hint-box {
            display: none;
            margin-top: 15px;
            text-align: left;
            background-color: #fff3cd;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            color: #856404;
        }
        .back-btn {
            background-color: #6c757d;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>👤 UserHub Registration System</h1>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left; border-left: 4px solid #2196f3;">
        <strong>👥 Scenario:</strong> You're testing UserHub's registration and profile system. The registration process stores data safely using prepared statements, but the profile viewing functionality might be vulnerable to second-order injection where stored malicious data gets executed later.
        <br><br>
        <strong>🎯 Objective:</strong> Register a user with malicious data in the email field, then trigger the injection when viewing that user's profile.
    </div>

    <?php if ($message): ?>
        <div class="message <?= strpos($message, 'Error') !== false ? 'error' : (strpos($message, 'completed') !== false ? 'success' : 'info') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>

    <div class="form-section">
        <h2>Register New User</h2>
        <form method="POST">
            <label for="username">Username:</label>
            <input type="text" name="username" id="username" required>

            <label for="email">Email:</label>
            <input type="text" name="email" id="email" required>

            <button type="submit" name="register">Register User</button>
        </form>
    </div>

    <div class="form-section">
        <h2>View User Profile</h2>
        <form method="POST">
            <label for="profile_username">Username to view:</label>
            <input type="text" name="profile_username" id="profile_username" required>

            <button type="submit" name="view_profile">View Profile</button>
        </form>
    </div>

    <div style="margin-top: 15px; text-align: left; background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">
        🎯 <strong>Challenge Goal:</strong> Exploit second-order SQL injection. Register a user with a malicious email, then view that user's profile to trigger the injection.
    </div>

    <?php if ($_SESSION[$session_key] >= 10): ?>
        <button onclick="toggleHint()" style="background-color: #ffc107; color: #212529;">Need Help? Show Hint</button>
        <div id="hint-box">
            ✅ <strong>Working Solutions:</strong> Try registering a user with any of these emails:<br><br>
            • <code><EMAIL>' UNION SELECT 'admin', '<EMAIL>' --</code><br>
            • <code>user'; INSERT INTO temp_users VALUES('hacked', '<EMAIL>') --</code><br>
            • <code>admin'; UPDATE temp_users SET username='hacked' WHERE id=1 --</code><br><br>
            Then view the profile. The malicious payload in the email executes during profile viewing.
        </div>
    <?php endif; ?>

    <button onclick="confirmBack()" class="back-btn">Back to Dashboard</button>
</div>

<script>
    function toggleHint() {
        const hintBox = document.getElementById('hint-box');
        hintBox.style.display = hintBox.style.display === "none" ? "block" : "none";
    }

    function confirmBack() {
        if (confirm("Are you sure you want to go back to the dashboard? If you haven't completed this challenge, it won't be marked as completed.")) {
            window.location.href = '../../pages/dashboard.php';
        }
    }
</script>

</body>
</html>
