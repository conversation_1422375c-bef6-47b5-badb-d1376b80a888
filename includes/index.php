<?php
// Prevent direct access to includes directory
header('HTTP/1.0 403 Forbidden');

// Track this access attempt for security monitoring
require_once __DIR__ . '/../config/access_tracker.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Forbidden - TryMeOut</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <style>
        body { font-family: 'Inter', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; margin: 0; }
        .error-container { text-align: center; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-radius: 24px; padding: 60px 40px; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); max-width: 500px; width: 90%; position: relative; }
        .error-container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #8b5cf6, #7c3aed); }
        .error-icon { width: 120px; height: 120px; margin: 0 auto 30px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem; }
        .error-code { font-size: 4rem; font-weight: 900; background: linear-gradient(135deg, #8b5cf6, #7c3aed); -webkit-background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 20px; }
        .error-title { font-size: 2rem; font-weight: 700; margin-bottom: 16px; color: #1e293b; }
        .error-message { font-size: 1.1rem; color: #475569; margin-bottom: 30px; line-height: 1.6; }
        .btn { padding: 12px 24px; border-radius: 12px; font-weight: 600; text-decoration: none; background: linear-gradient(135deg, #6366f1, #818cf8); color: white; display: inline-flex; align-items: center; gap: 8px; transition: all 0.3s ease; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4); }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">📁</div>
        <div class="error-code">403</div>
        <h1 class="error-title">Access Forbidden</h1>
        <p class="error-message">You don't have permission to access the includes directory. This contains sensitive PHP include files and functions.</p>
        <a href="../pages/dashboard.php" class="btn">🏠 Go to Dashboard</a>
    </div>
</body>
</html>
