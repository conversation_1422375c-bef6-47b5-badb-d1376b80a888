<?php
session_start();
require '../config/db_connect.php';

$certificate_code = $_GET['code'] ?? '';

if (empty($certificate_code)) {
    header('Location: ../pages/dashboard.php');
    exit();
}

// Get certificate details
$stmt = $pdo->prepare("
    SELECT uc.*, u.username, u.email, c.name as category_name, ct.*
    FROM user_certificates uc
    JOIN users u ON uc.user_id = u.id
    JOIN categories c ON uc.category_id = c.id
    LEFT JOIN certificate_templates ct ON c.id = ct.category_id AND ct.is_active = 1
    WHERE uc.certificate_code = ?
");
$stmt->execute([$certificate_code]);
$certificate = $stmt->fetch();

if (!$certificate) {
    $_SESSION['error_message'] = 'Certificate not found';
    header('Location: ../pages/dashboard.php');
    exit();
}

// Check if user owns this certificate or is admin
$can_view = false;
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['user_id'] == $certificate['user_id'] || $_SESSION['user_role'] === 'admin') {
        $can_view = true;
    }
} else {
    // Allow public viewing for verification
    $can_view = true;
}

if (!$can_view) {
    $_SESSION['error_message'] = 'Access denied';
    header('Location: ../pages/dashboard.php');
    exit();
}

// Update download count if user is downloading their own certificate
if (isset($_SESSION['user_id']) && $_SESSION['user_id'] == $certificate['user_id']) {
    $stmt = $pdo->prepare("
        UPDATE user_certificates
        SET download_count = download_count + 1,
            last_downloaded = NOW(),
            is_downloaded = 1
        WHERE certificate_code = ?
    ");
    $stmt->execute([$certificate_code]);
}

// Update verification count
$stmt = $pdo->prepare("
    INSERT INTO certificate_verifications (certificate_code, verification_count, last_verified)
    VALUES (?, 1, NOW())
    ON DUPLICATE KEY UPDATE
    verification_count = verification_count + 1,
    last_verified = NOW()
");
$stmt->execute([$certificate_code]);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($certificate_code) ?> - <?= htmlspecialchars($certificate['category_name']) ?> Certificate</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @page {
            size: A4 landscape;
            margin: 20mm;
            orientation: landscape;
        }

        @media print {
            @page {
                size: A4 landscape;
                margin: 20mm;
                orientation: landscape;
            }
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        body {
            font-family: "Times New Roman", serif;
            margin: 0;
            padding: 0;
            background: white;
            width: 297mm;
            height: 210mm;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            background-image: url("../assets/images/logo-clear.png");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.03;
            z-index: 0;
        }

        .certificate-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15mm;
            box-sizing: border-box;
        }

        .certificate {
            width: 100%;
            height: 100%;
            max-width: 267mm;
            max-height: 180mm;
            padding: 20mm 25mm;
            border: 6px solid <?= $certificate['border_color'] ?>;
            border-radius: 15px;
            background: white;
            text-align: center;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-sizing: border-box;
        }

        .certificate::before {
            content: "";
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid <?= $certificate['accent_color'] ?>;
            border-radius: 10px;
        }

        .header {
            margin-bottom: 15px;
        }

        .title {
            font-size: 36px;
            font-weight: bold;
            color: <?= $certificate['accent_color'] ?>;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .recipient {
            font-size: 28px;
            font-weight: bold;
            color: <?= $certificate['text_color'] ?>;
            margin: 15px 0;
            text-decoration: underline;
            text-decoration-color: <?= $certificate['accent_color'] ?>;
        }

        .achievement {
            font-size: 16px;
            line-height: 1.4;
            margin: 12px 0;
            color: #555;
        }

        .category-name {
            font-size: 22px;
            font-weight: bold;
            color: <?= $certificate['accent_color'] ?>;
            margin: 15px 0;
        }

        .details {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            font-size: 14px;
        }

        .signature-section {
            text-align: center;
        }

        .signature-line {
            border-top: 2px solid <?= $certificate['text_color'] ?>;
            width: 150px;
            margin: 15px auto 8px;
        }

        .certificate-code {
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 10px;
            color: #999;
            font-family: monospace;
        }

        .verification-url {
            position: absolute;
            bottom: 15px;
            left: 20px;
            font-size: 10px;
            color: #999;
        }

        .logo {
            width: 70px;
            height: 70px;
            margin: 0 auto 15px;
            background-image: url("../assets/images/logo-clear.png");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 8px;
            border: 2px solid <?= $certificate['accent_color'] ?>;
            padding: 8px;
        }

        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .verification-info {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        @media print {
            .action-buttons, .verification-info {
                display: none;
            }
            body {
                margin: 0;
                padding: 0;
                width: 297mm;
                height: 210mm;
                overflow: hidden;
            }
            .certificate-container {
                padding: 10mm;
                width: 100%;
                height: 100%;
            }
            .certificate {
                max-width: 277mm;
                max-height: 190mm;
                padding: 15mm 20mm;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="verification-info">
        <h6><i class="fas fa-shield-alt text-success"></i> Certificate Verified</h6>
        <small>
            <strong>ID:</strong> <?= htmlspecialchars($certificate_code) ?><br>
            <strong>Issued:</strong> <?= date('M j, Y', strtotime($certificate['issued_at'])) ?><br>
            <strong>Downloads:</strong> <?= $certificate['download_count'] ?>
        </small>
    </div>

    <div class="action-buttons">
        <?php if (isset($_SESSION['user_id']) && $_SESSION['user_id'] == $certificate['user_id']): ?>
            <button class="btn btn-primary me-2" onclick="window.print()">
                <i class="fas fa-print"></i> Print
            </button>
            <button class="btn btn-success me-2" onclick="downloadPDF()">
                <i class="fas fa-download"></i> Download PDF
            </button>
        <?php endif; ?>
        <a href="../pages/dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </a>
    </div>

    <div class="certificate-container">
        <div class="certificate">
            <div class="header">
                <div class="logo"></div>
                <div class="title">Certificate of Completion</div>
                <div class="subtitle">TryMeOut: A VULNERABILITY WEB APPLICATION<br>FOR SECURITY STUDENTS LEARNING</div>
            </div>

            <div class="achievement">
                This is to certify that
            </div>

            <div class="recipient"><?= htmlspecialchars($certificate['username']) ?></div>

            <div class="achievement">
                has successfully completed all challenges in the
            </div>

            <div class="category-name"><?= htmlspecialchars($certificate['category_name']) ?> Security Mastery</div>

            <div class="achievement">
                demonstrating exceptional proficiency in<br>
                <strong><?= htmlspecialchars($certificate['category_name']) ?></strong> vulnerability assessment and security testing
            </div>

            <div class="details">
                <div>
                    <strong>Completion Date:</strong><br>
                    <?= date('F j, Y', strtotime($certificate['completion_date'])) ?>
                </div>
                <div class="signature-section">
                    <div class="signature-line"></div>
                    <div><strong>TryMeOut</strong><br>Vulnerability Web Application</div>
                </div>
                <div>
                    <strong>Issue Date:</strong><br>
                    <?= date('F j, Y', strtotime($certificate['issued_at'])) ?>
                </div>
            </div>

            <div class="certificate-code">
                Certificate ID: <?= htmlspecialchars($certificate_code) ?>
            </div>

            <div class="verification-url">
                Verify at: trymeout.tech/utils/verify_certificate.php?code=<?= htmlspecialchars($certificate_code) ?>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // For now, we'll use the browser's print to PDF functionality
            // In a production environment, you'd implement proper PDF generation
            alert('Use your browser\'s "Print to PDF" option to save as PDF');
            window.print();
        }
    </script>
</body>
</html>
