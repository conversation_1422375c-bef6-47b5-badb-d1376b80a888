<?php
/**
 * Admin Profile Picture Management Utility
 * Provides functions for admins to manage user profile pictures
 *
 * Note: This file requires db_connect.php and upload_config.php to be included
 * before using this class. Include them in your calling script.
 */

class AdminProfileManager {
    
    private $pdo;
    private $admin_id;
    
    public function __construct($pdo, $admin_id) {
        $this->pdo = $pdo;
        $this->admin_id = $admin_id;
    }
    
    /**
     * Remove a user's profile picture and set to default
     */
    public function removeUserProfilePicture($user_id, $reason = 'Admin action') {
        try {
            $this->pdo->beginTransaction();
            
            // Get user info
            $stmt = $this->pdo->prepare("SELECT username, profile_picture FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            // Remove physical file if it's not the default
            if ($user['profile_picture'] && $user['profile_picture'] !== DEFAULT_PROFILE_PICTURE) {
                $file_path = '../' . $user['profile_picture'];
                if (file_exists($file_path) && is_file($file_path)) {
                    unlink($file_path);
                    error_log("Admin removed profile picture file: " . $file_path);
                }

                // Also remove any other profile pictures for this user (new naming pattern)
                $upload_dir = '../assets/uploads/';
                if (is_dir($upload_dir)) {
                    $files = glob($upload_dir . $user_id . '_*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                                unlink($file);
                                error_log("Admin removed additional profile picture: " . $file);
                            }
                        }
                    }
                }
            }
            
            // Update database to default picture
            $stmt = $this->pdo->prepare("UPDATE users SET profile_picture = ? WHERE id = ?");
            $stmt->execute([DEFAULT_PROFILE_PICTURE, $user_id]);
            
            // Log admin action
            $this->logAdminAction('remove_profile_picture', 'user', $user_id, 
                "Removed profile picture for user: {$user['username']}. Reason: {$reason}");
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            throw $e;
        }
    }
    
    /**
     * Get users with custom profile pictures (for moderation)
     */
    public function getUsersWithCustomPictures($limit = 50, $offset = 0) {
        // Ensure limit and offset are integers
        $limit = (int)$limit;
        $offset = (int)$offset;

        $sql = "
            SELECT
                u.id,
                u.username,
                u.email,
                u.profile_picture,
                u.created_at,
                COUNT(DISTINCT up.id) as total_completions
            FROM users u
            LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
            WHERE u.profile_picture IS NOT NULL
            AND u.profile_picture != ?
            AND u.profile_picture != ''
            GROUP BY u.id, u.username, u.email, u.profile_picture, u.created_at
            ORDER BY u.created_at DESC
            LIMIT {$limit} OFFSET {$offset}
        ";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([DEFAULT_PROFILE_PICTURE]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get count of users with custom profile pictures
     */
    public function getCustomPictureCount() {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) 
            FROM users 
            WHERE profile_picture IS NOT NULL 
            AND profile_picture != ? 
            AND profile_picture != ''
        ");
        $stmt->execute([DEFAULT_PROFILE_PICTURE]);
        return $stmt->fetchColumn();
    }
    
    /**
     * Bulk remove profile pictures for multiple users
     */
    public function bulkRemoveProfilePictures($user_ids, $reason = 'Bulk admin action') {
        $removed_count = 0;
        $errors = [];
        
        foreach ($user_ids as $user_id) {
            try {
                $this->removeUserProfilePicture($user_id, $reason);
                $removed_count++;
            } catch (Exception $e) {
                $errors[] = "User ID {$user_id}: " . $e->getMessage();
            }
        }
        
        return [
            'removed_count' => $removed_count,
            'errors' => $errors
        ];
    }
    
    /**
     * Check if a profile picture file is potentially inappropriate
     * This is a basic check - you might want to integrate with image analysis APIs
     */
    public function analyzeProfilePicture($file_path) {
        $analysis = [
            'file_exists' => false,
            'file_size' => 0,
            'dimensions' => null,
            'file_type' => null,
            'warnings' => []
        ];
        
        if (!file_exists($file_path)) {
            $analysis['warnings'][] = 'File does not exist';
            return $analysis;
        }
        
        $analysis['file_exists'] = true;
        $analysis['file_size'] = filesize($file_path);
        
        // Get image info
        $image_info = getimagesize($file_path);
        if ($image_info) {
            $analysis['dimensions'] = [
                'width' => $image_info[0],
                'height' => $image_info[1]
            ];
            $analysis['file_type'] = $image_info['mime'];
        }
        
        // Basic checks
        if ($analysis['file_size'] > UPLOAD_MAX_SIZE) {
            $analysis['warnings'][] = 'File size exceeds maximum allowed';
        }
        
        if ($image_info && ($image_info[0] > MAX_IMAGE_WIDTH || $image_info[1] > MAX_IMAGE_HEIGHT)) {
            $analysis['warnings'][] = 'Image dimensions exceed maximum allowed';
        }
        
        return $analysis;
    }
    
    /**
     * Get profile picture moderation statistics
     */
    public function getModerationStats() {
        $stats = [];
        
        // Total users with custom pictures
        $stats['custom_pictures'] = $this->getCustomPictureCount();
        
        // Total users with default pictures
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) 
            FROM users 
            WHERE profile_picture IS NULL 
            OR profile_picture = ? 
            OR profile_picture = ''
        ");
        $stmt->execute([DEFAULT_PROFILE_PICTURE]);
        $stats['default_pictures'] = $stmt->fetchColumn();
        
        // Recent removals (last 30 days) - with fallback if admin_logs doesn't exist
        try {
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'admin_logs'");
            if ($stmt->rowCount() > 0) {
                $stmt = $this->pdo->prepare("
                    SELECT COUNT(*)
                    FROM admin_logs
                    WHERE action = 'remove_profile_picture'
                    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                ");
                $stmt->execute();
                $stats['recent_removals'] = $stmt->fetchColumn();
            } else {
                $stats['recent_removals'] = 0; // Table doesn't exist yet
            }
        } catch (Exception $e) {
            $stats['recent_removals'] = 0; // Fallback value
        }
        
        return $stats;
    }
    
    /**
     * Log admin action
     */
    private function logAdminAction($action, $target_type, $target_id, $details) {
        try {
            // Check if admin_logs table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'admin_logs'");
            if ($stmt->rowCount() === 0) {
                // Table doesn't exist, log to error log instead
                error_log("Admin Action: User {$this->admin_id} performed '{$action}' on {$target_type} {$target_id}: {$details}");
                return;
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, ip_address)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $this->admin_id,
                $action,
                $target_type,
                $target_id,
                $details,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            // Fallback to error log if database logging fails
            error_log("Admin Action (DB Failed): User {$this->admin_id} performed '{$action}' on {$target_type} {$target_id}: {$details}");
            error_log("DB Error: " . $e->getMessage());
        }
    }
    
    /**
     * Get admin action history for profile picture management
     */
    public function getProfilePictureActionHistory($limit = 50) {
        // Ensure limit is integer
        $limit = (int)$limit;

        try {
            // Check if admin_logs table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'admin_logs'");
            if ($stmt->rowCount() === 0) {
                return []; // Return empty array if table doesn't exist
            }

            $stmt = $this->pdo->prepare("
                SELECT
                    al.*,
                    u.username as admin_username,
                    target_user.username as target_username
                FROM admin_logs al
                LEFT JOIN users u ON al.admin_id = u.id
                LEFT JOIN users target_user ON al.target_id = target_user.id AND al.target_type = 'user'
                WHERE al.action = 'remove_profile_picture'
                ORDER BY al.created_at DESC
                LIMIT {$limit}
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            // Return empty array on error
            return [];
        }
    }
}

// Helper function for standalone usage
function removeUserProfilePicture($user_id, $admin_id, $reason = 'Admin action') {
    global $pdo;
    $manager = new AdminProfileManager($pdo, $admin_id);
    return $manager->removeUserProfilePicture($user_id, $reason);
}
?>
