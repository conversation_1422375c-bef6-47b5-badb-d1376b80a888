<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied. Admin privileges required.']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['badge_id']) || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$badge_id = (int)$input['badge_id'];
$action = $input['action'];

if ($action !== 'revoke') {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

try {
    // Get badge details before deletion for logging
    $stmt = $pdo->prepare("
        SELECT ub.*, u.username, c.name as category_name
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        LEFT JOIN categories c ON ub.category_id = c.id
        WHERE ub.id = ?
    ");
    $stmt->execute([$badge_id]);
    $badge = $stmt->fetch();
    
    if (!$badge) {
        echo json_encode(['success' => false, 'message' => 'Badge not found']);
        exit;
    }
    
    // Delete the badge
    $stmt = $pdo->prepare("DELETE FROM user_badges WHERE id = ?");
    $stmt->execute([$badge_id]);
    
    if ($stmt->rowCount() > 0) {
        // Log the admin action
        $log_message = "Admin {$_SESSION['username']} revoked {$badge['category_name']} badge from user {$badge['username']} (Badge ID: {$badge_id})";
        error_log($log_message);
        
        echo json_encode([
            'success' => true,
            'message' => 'Badge revoked successfully',
            'details' => [
                'badge_id' => $badge_id,
                'username' => $badge['username'],
                'category' => $badge['category_name'],
                'revoked_by' => $_SESSION['username'],
                'revoked_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Badge could not be revoked']);
    }
    
} catch (PDOException $e) {
    error_log("Error revoking badge: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred while revoking badge'
    ]);
} catch (Exception $e) {
    error_log("Error revoking badge: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred'
    ]);
}
?>
