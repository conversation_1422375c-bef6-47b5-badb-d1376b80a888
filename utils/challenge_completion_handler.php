<?php
/**
 * Challenge Completion Handler
 * Centralized function to handle challenge completion and badge awards
 */

require_once __DIR__ . '/badge_award_system.php';

/**
 * Mark challenge as completed and check for badge awards
 * @param PDO $pdo Database connection
 * @param int $user_id User ID
 * @param int $challenge_id Challenge ID
 * @return array Result with completion status and any awarded badges
 */
function completeChallenge($pdo, $user_id, $challenge_id) {
    try {
        $pdo->beginTransaction();
        
        // Mark challenge as completed
        $stmt = $pdo->prepare("
            REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) 
            VALUES (?, ?, 'completed', NOW())
        ");
        $stmt->execute([$user_id, $challenge_id]);
        
        // Check and award badges
        $badgeSystem = new BadgeAwardSystem($pdo);
        $awarded_badges = $badgeSystem->checkAndAwardBadges($user_id, $challenge_id);
        
        $pdo->commit();
        
        return [
            'success' => true,
            'challenge_completed' => true,
            'badges_awarded' => $awarded_badges,
            'badge_count' => count($awarded_badges)
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error in challenge completion: " . $e->getMessage());
        
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'challenge_completed' => false,
            'badges_awarded' => [],
            'badge_count' => 0
        ];
    }
}

/**
 * Get completion message with badge information
 * @param array $completion_result Result from completeChallenge()
 * @return string HTML message
 */
function getCompletionMessage($completion_result) {
    if (!$completion_result['success']) {
        return "❌ Error completing challenge. Please try again.";
    }
    
    $message = "🎉 Challenge completed successfully!";
    
    if ($completion_result['badge_count'] > 0) {
        $message .= "<br><br>🏆 <strong>Badge" . ($completion_result['badge_count'] > 1 ? 's' : '') . " Earned:</strong><br>";
        
        foreach ($completion_result['badges_awarded'] as $badge) {
            $message .= "<div class='badge-notification' style='background: " . htmlspecialchars($badge['color']) . "; color: white; padding: 8px 12px; border-radius: 6px; margin: 4px 0; display: inline-block;'>";
            $message .= htmlspecialchars($badge['icon']) . " " . htmlspecialchars($badge['name']);
            $message .= "<br><small>" . htmlspecialchars($badge['description']) . "</small>";
            $message .= "</div><br>";
        }
    }
    
    return $message;
}

/**
 * Get JavaScript for badge notifications
 * @param array $completion_result Result from completeChallenge()
 * @return string JavaScript code
 */
function getBadgeNotificationScript($completion_result) {
    if ($completion_result['badge_count'] == 0) {
        return '';
    }
    
    $script = "<script>";
    $script .= "setTimeout(function() {";
    
    foreach ($completion_result['badges_awarded'] as $badge) {
        $script .= "
            if (typeof showBadgeNotification === 'function') {
                showBadgeNotification('" . addslashes($badge['name']) . "', '" . addslashes($badge['description']) . "', '" . addslashes($badge['icon']) . "', '" . addslashes($badge['color']) . "');
            }
        ";
    }
    
    $script .= "}, 1000);";
    $script .= "</script>";
    
    return $script;
}

/**
 * Add badge notification CSS and JavaScript to page
 * @return string HTML with CSS and JS
 */
function getBadgeNotificationAssets() {
    return '
    <style>
    .badge-notification-popup {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 350px;
        animation: slideInRight 0.5s ease-out;
        border: 2px solid rgba(255,255,255,0.2);
    }
    
    .badge-notification-popup .badge-icon {
        font-size: 2rem;
        margin-bottom: 8px;
        display: block;
    }
    
    .badge-notification-popup .badge-title {
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 4px;
    }
    
    .badge-notification-popup .badge-desc {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .badge-notification-popup .close-btn {
        position: absolute;
        top: 8px;
        right: 12px;
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        opacity: 0.7;
    }
    
    .badge-notification-popup .close-btn:hover {
        opacity: 1;
    }
    
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    </style>
    
    <script>
    function showBadgeNotification(name, description, icon, color) {
        // Create notification element
        const notification = document.createElement("div");
        notification.className = "badge-notification-popup";
        notification.style.background = `linear-gradient(135deg, ${color} 0%, ${adjustColor(color, -20)} 100%)`;
        
        notification.innerHTML = `
            <button class="close-btn" onclick="closeBadgeNotification(this)">&times;</button>
            <div class="badge-icon">${icon}</div>
            <div class="badge-title">Badge Earned!</div>
            <div class="badge-title">${name}</div>
            <div class="badge-desc">${description}</div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                closeBadgeNotification(notification.querySelector(".close-btn"));
            }
        }, 8000);
    }
    
    function closeBadgeNotification(btn) {
        const notification = btn.parentNode;
        notification.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    function adjustColor(color, amount) {
        // Simple color adjustment function
        const usePound = color[0] === "#";
        const col = usePound ? color.slice(1) : color;
        const num = parseInt(col, 16);
        let r = (num >> 16) + amount;
        let g = (num >> 8 & 0x00FF) + amount;
        let b = (num & 0x0000FF) + amount;
        r = r > 255 ? 255 : r < 0 ? 0 : r;
        g = g > 255 ? 255 : g < 0 ? 0 : g;
        b = b > 255 ? 255 : b < 0 ? 0 : b;
        return (usePound ? "#" : "") + (r << 16 | g << 8 | b).toString(16).padStart(6, "0");
    }
    </script>
    ';
}
?>
