<?php
session_start();
require '../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/signin.php");
    exit;
}

// -- If coming in with a ?cid=... param, clear progress & redirect --
if (isset($_GET['cid'])) {
    $cid = (int)$_GET['cid'];

    // manual ID → module path mapping
    $links = [
        4  => '../modules/sql/login_bypass.php',
        5  => '../modules/sql/union_select.php',
        6  => '../modules/sql/blind_injection.php',
        7  => '../modules/xss/basic_alert.php',
        8  => '../modules/xss/image_on_error.php',
        9  => '../modules/xss/stored.php',
        10 => '../modules/cominject/ping_injection.php',
        11 => '../modules/cominject/read_file_system.php',
        12 => '../modules/cominject/reverse_shell.php',
        13 => '../modules/sql/error_based_injection.php',
        14 => '../modules/sql/time_based_blind.php',
        15 => '../modules/sql/second_order_injection.php',
        16 => '../modules/sql/nosql_injection.php',
        17 => '../modules/xss/reflected_filter_bypass.php',
        18 => '../modules/xss/dom_advanced.php',
        19 => '../modules/xss/file_upload_xss.php',
        20 => '../modules/cominject/path_traversal.php',
        21 => '../modules/cominject/code_injection.php',
        22 => '../modules/cominject/ldap_injection.php',
    ];

    if (isset($links[$cid])) {
        // Delete previous progress
        $del = $pdo->prepare("DELETE FROM user_progress WHERE user_id = ? AND challenge_id = ?");
        $del->execute([$_SESSION['user_id'], $cid]);

        // Redirect into the module
        header("Location: " . $links[$cid]);
        exit;
    }
}

// 1) Fetch categories and challenges
$categoriesStmt = $pdo->prepare("SELECT id, name FROM categories ORDER BY name ASC");
$categoriesStmt->execute();
$categories = $categoriesStmt->fetchAll();

$challengesStmt = $pdo->prepare("SELECT * FROM challenges ORDER BY category_id, id ASC");
$challengesStmt->execute();
$allChallenges = $challengesStmt->fetchAll();

// 2) Group challenges by category
$challengesByCategory = [];
foreach ($allChallenges as $challenge) {
    $challengesByCategory[$challenge['category_id']][] = $challenge;
}

// 3) Fetch user progress (challenge_id => status)
$progStmt = $pdo->prepare("SELECT challenge_id, status FROM user_progress WHERE user_id = ?");
$progStmt->execute([$_SESSION['user_id']]);
$userProgress = $progStmt->fetchAll(PDO::FETCH_KEY_PAIR);

// 4) Define links map for display (relative paths)
$links = [
    4  => '../modules/sql/login_bypass.php',
    5  => '../modules/sql/union_select.php',
    6  => '../modules/sql/blind_injection.php',
    7  => '../modules/xss/basic_alert.php',
    8  => '../modules/xss/image_on_error.php',
    9  => '../modules/xss/stored.php',
    10 => '../modules/cominject/ping_injection.php',
    11 => '../modules/cominject/read_file_system.php',
    12 => '../modules/cominject/reverse_shell.php',
    13 => '../modules/sql/error_based_injection.php',
    14 => '../modules/sql/time_based_blind.php',
    15 => '../modules/sql/second_order_injection.php',
    16 => '../modules/sql/nosql_injection.php',
    17 => '../modules/xss/reflected_filter_bypass.php',
    18 => '../modules/xss/dom_advanced.php',
    19 => '../modules/xss/file_upload_xss.php',
    20 => '../modules/cominject/path_traversal.php',
    21 => '../modules/cominject/code_injection.php',
    22 => '../modules/cominject/ldap_injection.php',
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Security Challenges | TryMeOut</title>
  <link rel="icon" type="image/png" href="assets/images/logo-clear.png">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <style>
    body { background-color: #121212; color: #e0e0e0; font-family: 'Roboto', sans-serif; }
    .container { margin-top: 80px; }
    h1 {
      text-align: center; font-size: 3rem; font-weight: bold;
      color: #00FF00; margin-bottom: 40px; letter-spacing: 2px;
      font-family: 'Press Start 2P', cursive;
    }
    .category-card {
      margin-bottom: 20px; border-radius: 15px; height: 250px;
      display: flex; align-items: center; justify-content: center;
      background: #1a1a1a; transition: transform .3s, box-shadow .3s;
      box-shadow: 0 4px 10px rgba(0,0,0,.5);
    }
    .category-card:hover { transform: translateY(-10px); box-shadow: 0 12px 25px rgba(0,255,0,.3); }
    .category-content { text-align: center; }
    .category-content h4 { color:#fff; font-size:2rem; font-weight:bold; }
    .category-content .btn {
      background:#2e563f; color:#fff; border-radius:8px;
      padding:8px 20px; margin-top:20px; font-size:1.1rem;
      transition:background .3s;
    }
    .category-content .btn:hover { background:#3a7a5c; }
    .difficulty-badge {
      font-size:1rem; padding:5px 10px; border-radius:10px;
      margin-top:10px; display:inline-block; text-align:center;
    }
    .difficulty-easy { background:#28a745; color:#fff; }
    .difficulty-medium { background:#ffc107; color:#121212; }
    .difficulty-hard { background:#fd7e14; color:#fff; }
    .difficulty-expert { background:#dc3545; color:#fff; }
    .level-box {
      display:block; background:#222; padding:20px; border-radius:12px;
      text-decoration:none; color:#fff; transition:transform .3s, background .3s, box-shadow .3s;
      margin-bottom:15px; text-align:center; position:relative;
    }
    .level-box:hover {
      transform: translateY(-8px); background:#333;
      box-shadow: 0 8px 15px rgba(0,255,0,.3);
    }
    .level-box i { font-size:3rem; color:#2e563f; margin-bottom:10px; }
    .level-box h4 { font-size:1.5rem; margin-bottom:10px; }
    .level-box p { font-size:1rem; margin-bottom:10px; color:#bbb; }
    .badge-completed {
      position:absolute; top:10px; right:10px;
      background:#28a745; color:#fff; padding:5px 10px; border-radius:5px; font-size:.9rem;
    }
    .back-button {
      background:#2e563f; color:#fff; border-radius:8px; padding:10px 20px;
      font-size:1.2rem; transition:background .3s; text-decoration:none;
      display:block; margin:30px auto; width:auto; max-width:250px; text-align:center;
    }
    .back-button:hover { background:#3a7a5c; }
  </style>
</head>
<body>

<div class="container">
  <h1>Security Challenges</h1>
  <a href="../pages/dashboard.php" class="back-button">Back to Dashboard</a>

  <div class="row">
    <?php foreach ($categories as $category): ?>
      <div class="col-md-4">
        <div class="category-card">
          <div class="category-content">
            <i class="bi bi-shield-lock" style="font-size:3rem;color:#00FF00;"></i>
            <h4><?= htmlspecialchars($category['name']) ?></h4>
            <button class="btn" data-bs-toggle="collapse" data-bs-target="#cat<?= $category['id'] ?>">View Challenges</button>
          </div>
        </div>

        <div class="collapse" id="cat<?= $category['id'] ?>">
          <?php if (!empty($challengesByCategory[$category['id']])): ?>
            <?php foreach ($challengesByCategory[$category['id']] as $challenge): ?>
              <?php
                $cid = $challenge['id'];
                $completed = isset($userProgress[$cid]) && $userProgress[$cid] === 'completed';
                $onclick = $completed
                  ? "return confirm('You have already completed this challenge. Proceeding will delete your previous progress. Continue?');"
                  : '';
              ?>
              <a href="?cid=<?= $cid ?>"
                 class="level-box"
                 <?= $onclick ? "onclick=\"{$onclick}\"" : "" ?>>
                <?php if ($completed): ?>
                  <span class="badge-completed">Completed</span>
                <?php endif; ?>

                <i class="bi bi-shield-lock"></i>
                <h4><?= htmlspecialchars($challenge['title']) ?></h4>
                <p><?= htmlspecialchars($challenge['description']) ?></p>
                <div class="difficulty-badge difficulty-<?= strtolower($challenge['difficulty']) ?>">
                  <?= ucfirst($challenge['difficulty']) ?>
                </div>
              </a>
            <?php endforeach; ?>
          <?php else: ?>
            <p>No challenges available in this category</p>
          <?php endif; ?>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
