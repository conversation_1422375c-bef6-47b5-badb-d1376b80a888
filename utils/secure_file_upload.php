<?php
/**
 * Secure File Upload Utility for Profile Pictures
 * Handles file validation, security checks, and safe upload processing
 */

// Try different paths for the upload config
$config_paths = [
    '../config/upload_config.php',
    dirname(__DIR__) . '/config/upload_config.php',
    __DIR__ . '/../config/upload_config.php'
];

$config_loaded = false;
foreach ($config_paths as $config_path) {
    if (file_exists($config_path)) {
        require_once $config_path;
        $config_loaded = true;
        error_log("SecureFileUpload: Loaded config from: " . $config_path);
        break;
    }
}

if (!$config_loaded) {
    error_log("SecureFileUpload: Could not load upload configuration file");
    throw new Exception("Upload configuration not found");
}

class SecureFileUpload {
    
    private $errors = [];
    
    /**
     * Validate uploaded file for security and requirements
     */
    public function validateFile($file) {
        $this->errors = [];

        error_log("SecureFileUpload: Starting file validation");
        error_log("SecureFileUpload: File error code: " . ($file['error'] ?? 'not set'));

        // Check if file was uploaded
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = "File upload failed. Please try again. Error code: " . ($file['error'] ?? 'unknown');
            return false;
        }

        error_log("SecureFileUpload: File size: " . $file['size'] . " bytes");

        // Check file size
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            $this->errors[] = "File size too large. Your file is " . formatFileSize($file['size']) . ", but maximum allowed size is " . formatFileSize(UPLOAD_MAX_SIZE) . ".";
            error_log("SecureFileUpload: File too large - " . $file['size'] . " bytes (limit: " . UPLOAD_MAX_SIZE . ")");
            return false;
        }

        // Check if file is empty
        if ($file['size'] == 0) {
            $this->errors[] = "File is empty. Please select a valid image file.";
            return false;
        }

        // Get file extension
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        error_log("SecureFileUpload: File extension: " . $file_extension);

        // Check allowed extensions
        if (!in_array($file_extension, ALLOWED_IMAGE_EXTENSIONS)) {
            $this->errors[] = "Invalid file type. Only " . implode(', ', ALLOWED_IMAGE_EXTENSIONS) . " files are allowed.";
            return false;
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        error_log("SecureFileUpload: MIME type: " . $mime_type);

        if (!in_array($mime_type, ALLOWED_IMAGE_MIME_TYPES)) {
            $this->errors[] = "Invalid file format. Detected type: " . $mime_type . ". Only " . implode(', ', ALLOWED_IMAGE_MIME_TYPES) . " are allowed.";
            error_log("SecureFileUpload: Invalid MIME type - " . $mime_type);
            return false;
        }

        // Additional security: Check if file is actually an image
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            $this->errors[] = "File is not a valid image or is corrupted.";
            return false;
        }

        error_log("SecureFileUpload: Image dimensions: " . $image_info[0] . "x" . $image_info[1]);

        // Check image dimensions (optional - prevent extremely large images)
        if ($image_info[0] > MAX_IMAGE_WIDTH || $image_info[1] > MAX_IMAGE_HEIGHT) {
            $this->errors[] = "Image dimensions too large. Your image is " . $image_info[0] . "x" . $image_info[1] . " pixels, but maximum allowed is " . MAX_IMAGE_WIDTH . "x" . MAX_IMAGE_HEIGHT . " pixels.";
            error_log("SecureFileUpload: Image dimensions too large - " . $image_info[0] . "x" . $image_info[1]);
            return false;
        }

        // Security: Scan for malicious content in file headers
        if ($this->containsMaliciousContent($file['tmp_name'])) {
            $this->errors[] = "File contains potentially malicious content and cannot be uploaded.";
            return false;
        }

        error_log("SecureFileUpload: File validation completed successfully");
        return true;
    }
    
    /**
     * Upload and process the profile picture
     */
    public function uploadProfilePicture($file, $user_id) {
        error_log("SecureFileUpload: Starting upload for user ID: " . $user_id);
        error_log("SecureFileUpload: File details: " . print_r($file, true));

        if (!$this->validateFile($file)) {
            error_log("SecureFileUpload: File validation failed: " . implode(', ', $this->errors));
            return false;
        }

        error_log("SecureFileUpload: File validation passed");

        // Create upload directory if it doesn't exist
        if (!is_dir(UPLOAD_DIR)) {
            error_log("SecureFileUpload: Upload directory doesn't exist, creating: " . UPLOAD_DIR);
            if (!mkdir(UPLOAD_DIR, 0755, true)) {
                $this->errors[] = "Failed to create upload directory.";
                error_log("SecureFileUpload: Failed to create upload directory: " . UPLOAD_DIR);
                return false;
            }
        }

        // Generate secure filename with user ID prefix and original filename
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $original_name = pathinfo($file['name'], PATHINFO_FILENAME);

        // Sanitize the original filename to prevent security issues
        $safe_original_name = preg_replace('/[^a-zA-Z0-9._-]/', '_', $original_name);
        $safe_original_name = substr($safe_original_name, 0, 50); // Limit length

        // Create filename: {user_id}_{original_filename}.{extension}
        $new_filename = $user_id . '_' . $safe_original_name . '.' . $file_extension;
        $upload_path = UPLOAD_DIR . $new_filename;

        error_log("SecureFileUpload: Generated filename: " . $new_filename);
        error_log("SecureFileUpload: Upload path: " . $upload_path);

        // Remove old profile picture if exists
        $this->removeOldProfilePicture($user_id);

        // Check if temp file exists before moving
        if (!file_exists($file['tmp_name'])) {
            $this->errors[] = "Temporary file not found. Upload may have failed.";
            error_log("SecureFileUpload: Temporary file not found: " . $file['tmp_name']);
            return false;
        }

        // Check if destination directory is writable
        if (!is_writable(UPLOAD_DIR)) {
            $this->errors[] = "Upload directory is not writable. Please contact administrator.";
            error_log("SecureFileUpload: Upload directory not writable: " . UPLOAD_DIR);
            return false;
        }

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
            $this->errors[] = "Failed to save uploaded file. Check directory permissions.";
            error_log("SecureFileUpload: move_uploaded_file failed from " . $file['tmp_name'] . " to " . $upload_path);
            error_log("SecureFileUpload: Upload directory permissions: " . substr(sprintf('%o', fileperms(UPLOAD_DIR)), -4));
            error_log("SecureFileUpload: PHP error: " . error_get_last()['message'] ?? 'No PHP error');
            return false;
        }

        error_log("SecureFileUpload: File moved successfully to: " . $upload_path);

        // Set proper file permissions
        chmod($upload_path, 0644);

        // Return the database path (relative path without ../)
        $return_path = UPLOAD_URL_PATH . $new_filename;
        error_log("SecureFileUpload: Returning path: " . $return_path);
        return $return_path;
    }
    
    /**
     * Remove old profile picture files
     */
    private function removeOldProfilePicture($user_id) {
        // Remove files with both old and new naming patterns
        $old_patterns = [
            UPLOAD_DIR . 'user_' . $user_id . '.jpg',
            UPLOAD_DIR . 'user_' . $user_id . '.jpeg',
            UPLOAD_DIR . 'user_' . $user_id . '.png',
            UPLOAD_DIR . 'user_' . $user_id . '.gif'
        ];

        // Remove old pattern files
        foreach ($old_patterns as $pattern) {
            if (file_exists($pattern) && is_file($pattern)) {
                unlink($pattern);
                error_log("Removed old profile picture: " . $pattern);
            }
        }

        // Remove new pattern files (user_id_filename.ext)
        $upload_dir = UPLOAD_DIR;
        if (is_dir($upload_dir)) {
            $files = glob($upload_dir . $user_id . '_*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    // Verify it's an image file before deleting
                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                        unlink($file);
                        error_log("Removed profile picture: " . $file);
                    }
                }
            }
        }
    }
    
    /**
     * Check for malicious content in file
     */
    private function containsMaliciousContent($file_path) {
        $content = file_get_contents($file_path, false, null, 0, 1024); // Read first 1KB
        
        // Check for common malicious patterns using centralized configuration
        foreach (MALICIOUS_PATTERNS as $pattern) {
            if (stripos($content, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    

    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }

    /**
     * Get suggestions for fixing upload issues
     */
    public function getSuggestions($file) {
        $suggestions = [];

        if ($file['size'] > UPLOAD_MAX_SIZE) {
            $suggestions[] = "Reduce file size by compressing the image or using a lower quality setting.";
        }

        $image_info = getimagesize($file['tmp_name']);
        if ($image_info && ($image_info[0] > MAX_IMAGE_WIDTH || $image_info[1] > MAX_IMAGE_HEIGHT)) {
            $suggestions[] = "Resize image to maximum " . MAX_IMAGE_WIDTH . "x" . MAX_IMAGE_HEIGHT . " pixels.";
        }

        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, ALLOWED_IMAGE_EXTENSIONS)) {
            $suggestions[] = "Convert image to one of these formats: " . implode(', ', ALLOWED_IMAGE_EXTENSIONS);
        }

        if ($image_info === false) {
            $suggestions[] = "The file appears to be corrupted. Try re-saving it in an image editor.";
        }

        return $suggestions;
    }
    
    /**
     * Get last error message
     */
    public function getLastError() {
        return end($this->errors);
    }
}
?>
