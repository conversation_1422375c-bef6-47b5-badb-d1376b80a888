<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied. Admin privileges required.']);
    exit();
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed. Use POST.']);
    exit();
}

// Get certificate code from request
$input = json_decode(file_get_contents('php://input'), true);
$certificate_code = $input['certificate_code'] ?? '';

if (empty($certificate_code)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Certificate code is required.']);
    exit();
}

try {
    // Start transaction
    $pdo->beginTransaction();

    // Check if certificate exists
    $stmt = $pdo->prepare("
        SELECT uc.*, u.username, u.email, c.name as category_name
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories c ON uc.category_id = c.id
        WHERE uc.certificate_code = ?
    ");
    $stmt->execute([$certificate_code]);
    $certificate = $stmt->fetch();

    if (!$certificate) {
        $pdo->rollBack();
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Certificate not found.']);
        exit();
    }

    // Check if certificate is already revoked
    if (isset($certificate['is_revoked']) && $certificate['is_revoked']) {
        $pdo->rollBack();
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Certificate is already revoked.']);
        exit();
    }

    // Add revoked column if it doesn't exist
    try {
        $pdo->exec("ALTER TABLE user_certificates ADD COLUMN is_revoked TINYINT(1) DEFAULT 0");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $pdo->exec("ALTER TABLE user_certificates ADD COLUMN revoked_at TIMESTAMP NULL DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $pdo->exec("ALTER TABLE user_certificates ADD COLUMN revoked_by INT(11) NULL DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Delete the certificate from database (complete revocation)
    $stmt = $pdo->prepare("
        DELETE FROM user_certificates
        WHERE certificate_code = ?
    ");
    $stmt->execute([$certificate_code]);

    // Log the revocation action
    $stmt = $pdo->prepare("
        INSERT INTO admin_actions (admin_id, action_type, target_type, target_id, details, created_at)
        VALUES (?, 'revoke', 'certificate', ?, ?, NOW())
    ");

    $details = json_encode([
        'certificate_code' => $certificate_code,
        'user_id' => $certificate['user_id'],
        'username' => $certificate['username'],
        'category' => $certificate['category_name'],
        'revoked_by' => $_SESSION['username']
    ]);

    try {
        $stmt->execute([$_SESSION['user_id'], $certificate['id'], $details]);
    } catch (PDOException $e) {
        // Admin actions table might not exist, create it
        try {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS admin_actions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    admin_id INT NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    target_type VARCHAR(50) NOT NULL,
                    target_id INT NOT NULL,
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_admin_id (admin_id),
                    INDEX idx_action_type (action_type),
                    INDEX idx_created_at (created_at)
                )
            ");

            // Try inserting the log again
            $stmt->execute([$_SESSION['user_id'], $certificate['id'], $details]);
        } catch (PDOException $e2) {
            // If still fails, continue without logging
        }
    }

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Certificate revoked successfully.',
        'data' => [
            'certificate_code' => $certificate_code,
            'username' => $certificate['username'],
            'category' => $certificate['category_name'],
            'revoked_at' => date('Y-m-d H:i:s'),
            'revoked_by' => $_SESSION['username']
        ]
    ]);

} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();

    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred while revoking certificate.',
        'error' => $e->getMessage()
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    $pdo->rollBack();

    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while revoking certificate.',
        'error' => $e->getMessage()
    ]);
}
?>
