<?php
session_start();
require '../config/db_connect.php';

// Ensure user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/signin.php');
    exit();
}

class CertificateGenerator {
    private $pdo;
    private $user_id;

    public function __construct($pdo, $user_id) {
        $this->pdo = $pdo;
        $this->user_id = $user_id;
    }

    public function checkEligibility($category_id) {
        // Get total challenges in category
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM challenges WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $total_challenges = $stmt->fetchColumn();

        // Get completed challenges by user in category
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT c.id)
            FROM challenges c
            INNER JOIN user_progress up ON c.id = up.challenge_id
            WHERE c.category_id = ? AND up.user_id = ? AND up.status = 'completed'
        ");
        $stmt->execute([$category_id, $this->user_id]);
        $completed_challenges = $stmt->fetchColumn();

        return [
            'eligible' => $completed_challenges >= $total_challenges && $total_challenges > 0,
            'completed' => $completed_challenges,
            'total' => $total_challenges
        ];
    }

    public function generateCertificate($category_id) {
        $eligibility = $this->checkEligibility($category_id);

        if (!$eligibility['eligible']) {
            throw new Exception("User has not completed all challenges in this category");
        }

        // Check if certificate already exists
        $stmt = $this->pdo->prepare("SELECT * FROM user_certificates WHERE user_id = ? AND category_id = ?");
        $stmt->execute([$this->user_id, $category_id]);
        $existing_cert = $stmt->fetch();

        if ($existing_cert) {
            return $existing_cert['certificate_code'];
        }

        // Get user and category information
        $stmt = $this->pdo->prepare("SELECT username, email FROM users WHERE id = ?");
        $stmt->execute([$this->user_id]);
        $user = $stmt->fetch();

        $stmt = $this->pdo->prepare("SELECT name FROM categories WHERE id = ?");
        $stmt->execute([$category_id]);
        $category = $stmt->fetch();

        $stmt = $this->pdo->prepare("SELECT * FROM certificate_templates WHERE category_id = ? AND is_active = 1");
        $stmt->execute([$category_id]);
        $template = $stmt->fetch();

        // Generate unique certificate code
        $certificate_code = $this->generateCertificateCode($category_id);

        // Insert certificate record
        $stmt = $this->pdo->prepare("
            INSERT INTO user_certificates
            (user_id, category_id, certificate_code, completion_date, challenges_completed, total_challenges)
            VALUES (?, ?, ?, CURDATE(), ?, ?)
        ");
        $stmt->execute([
            $this->user_id,
            $category_id,
            $certificate_code,
            $eligibility['completed'],
            $eligibility['total']
        ]);

        // Generate PDF certificate
        $this->createPDFCertificate($certificate_code, $user, $category, $template, $eligibility);

        return $certificate_code;
    }

    private function generateCertificateCode($category_id) {
        $prefix = 'TMO';

        // Generate mixed alphanumeric random string (letters and numbers)
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $random_part = '';
        for ($i = 0; $i < 8; $i++) {
            $random_part .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $prefix . '-' . $random_part;
    }

    private function createPDFCertificate($certificate_code, $user, $category, $template, $eligibility) {
        // Create certificates directory if it doesn't exist
        $cert_dir = 'certificates';
        if (!file_exists($cert_dir)) {
            mkdir($cert_dir, 0755, true);
        }

        // Generate HTML certificate
        $html = $this->generateCertificateHTML($certificate_code, $user, $category, $template, $eligibility);

        // Create filename with certificate serial and title
        $safe_category_name = preg_replace('/[^A-Za-z0-9\-_]/', '_', $category['name']);
        $filename = $certificate_code . '_' . $safe_category_name . '_Certificate.html';
        $filepath = $cert_dir . '/' . $filename;
        file_put_contents($filepath, $html);

        // Update certificate record with file path
        $stmt = $this->pdo->prepare("UPDATE user_certificates SET certificate_path = ? WHERE certificate_code = ?");
        $stmt->execute([$filepath, $certificate_code]);

        return $filepath;
    }

    private function generateCertificateHTML($certificate_code, $user, $category, $template, $eligibility) {
        $completion_date = date('F j, Y');
        $issue_date = date('F j, Y');

        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($certificate_code) . ' - ' . htmlspecialchars($category['name']) . ' Certificate</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 20mm;
            orientation: landscape;
        }

        @media print {
            @page {
                size: A4 landscape;
                margin: 20mm;
                orientation: landscape;
            }
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        body {
            font-family: "Times New Roman", serif;
            margin: 0;
            padding: 0;
            background: white;
            width: 297mm;
            height: 210mm;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            background-image: url("assets/images/logo-clear.png");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.03;
            z-index: 0;
        }

        .certificate-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15mm;
            box-sizing: border-box;
        }

        .certificate {
            width: 100%;
            height: 100%;
            max-width: 267mm;
            max-height: 180mm;
            padding: 20mm 25mm;
            border: 6px solid ' . $template['border_color'] . ';
            border-radius: 15px;
            background: white;
            text-align: center;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-sizing: border-box;
        }

        .certificate::before {
            content: "";
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid ' . $template['accent_color'] . ';
            border-radius: 10px;
        }

        .header {
            margin-bottom: 15px;
        }

        .title {
            font-size: 36px;
            font-weight: bold;
            color: ' . $template['accent_color'] . ';
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .recipient {
            font-size: 28px;
            font-weight: bold;
            color: ' . $template['text_color'] . ';
            margin: 15px 0;
            text-decoration: underline;
            text-decoration-color: ' . $template['accent_color'] . ';
        }

        .achievement {
            font-size: 16px;
            line-height: 1.4;
            margin: 12px 0;
            color: #555;
        }

        .category-name {
            font-size: 22px;
            font-weight: bold;
            color: ' . $template['accent_color'] . ';
            margin: 15px 0;
        }

        .details {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            font-size: 14px;
        }

        .signature-section {
            text-align: center;
        }

        .signature-line {
            border-top: 2px solid ' . $template['text_color'] . ';
            width: 150px;
            margin: 15px auto 8px;
        }

        .certificate-code {
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 10px;
            color: #999;
            font-family: monospace;
        }

        .verification-url {
            position: absolute;
            bottom: 15px;
            left: 20px;
            font-size: 10px;
            color: #999;
        }

        .logo {
            width: 70px;
            height: 70px;
            margin: 0 auto 15px;
            background-image: url("assets/images/logo-clear.png");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 8px;
            border: 2px solid ' . $template['accent_color'] . ';
            padding: 8px;
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate">
            <div class="header">
                <div class="logo"></div>
                <div class="title">Certificate of Completion</div>
                <div class="subtitle">TryMeOut: A VULNERABILITY WEB APPLICATION<br>FOR SECURITY STUDENTS LEARNING</div>
            </div>

            <div class="achievement">
                This is to certify that
            </div>

            <div class="recipient">' . htmlspecialchars($user['username']) . '</div>

            <div class="achievement">
                has successfully completed all challenges in the
            </div>

            <div class="category-name">' . htmlspecialchars($category['name']) . ' Security Mastery</div>

            <div class="achievement">
                demonstrating exceptional proficiency in<br>
                <strong>' . htmlspecialchars($category['name']) . '</strong> vulnerability assessment and security testing
            </div>

            <div class="details">
                <div>
                    <strong>Completion Date:</strong><br>
                    ' . $completion_date . '
                </div>
                <div class="signature-section">
                    <div class="signature-line"></div>
                    <div><strong>TryMeOut</strong><br>Vulnerability Web Application</div>
                </div>
                <div>
                    <strong>Issue Date:</strong><br>
                    ' . $issue_date . '
                </div>
            </div>

            <div class="certificate-code">
                Certificate ID: ' . $certificate_code . '
            </div>

            <div class="verification-url">
                Verify at: trymeout.tech/utils/view_certificate.php?code=' . $certificate_code . '
            </div>
        </div>
    </div>
</body>
</html>';
    }
}

// Handle certificate generation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_certificate'])) {
    $category_id = (int)$_POST['category_id'];

    try {
        $generator = new CertificateGenerator($pdo, $_SESSION['user_id']);
        $certificate_code = $generator->generateCertificate($category_id);

        $_SESSION['certificate_generated'] = $certificate_code;
        header('Location: ../utils/view_certificate.php?code=' . $certificate_code);
        exit();

    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
        header('Location: ../pages/dashboard.php');
        exit();
    }
}

// If accessed directly, redirect to dashboard
header('Location: ../pages/dashboard.php');
exit();
?>
