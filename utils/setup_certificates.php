<?php
require '../config/db_connect.php';

try {
    // Create certificates table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `user_certificates` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `category_id` int(11) NOT NULL,
          `certificate_code` varchar(50) NOT NULL UNIQUE,
          `issued_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `completion_date` date NOT NULL,
          `challenges_completed` int(11) NOT NULL,
          `total_challenges` int(11) NOT NULL,
          `certificate_path` varchar(255) DEFAULT NULL,
          `is_downloaded` tinyint(1) DEFAULT 0,
          `download_count` int(11) DEFAULT 0,
          `last_downloaded` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_user_category` (`user_id`, `category_id`),
          KEY `fk_cert_user` (`user_id`),
          KEY `fk_cert_category` (`category_id`),
          CONSTRAINT `fk_cert_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
          CONSTRAINT `fk_cert_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    echo "✅ user_certificates table created successfully<br>";

    // Create certificate templates table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `certificate_templates` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `category_id` int(11) NOT NULL,
          `template_name` varchar(100) NOT NULL,
          `background_color` varchar(7) DEFAULT '#ffffff',
          `border_color` varchar(7) DEFAULT '#000000',
          `text_color` varchar(7) DEFAULT '#000000',
          `accent_color` varchar(7) DEFAULT '#007bff',
          `logo_path` varchar(255) DEFAULT NULL,
          `is_active` tinyint(1) DEFAULT 1,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `fk_template_category` (`category_id`),
          CONSTRAINT `fk_template_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    echo "✅ certificate_templates table created successfully<br>";

    // Create certificate verification table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `certificate_verifications` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `certificate_code` varchar(50) NOT NULL,
          `verification_count` int(11) DEFAULT 0,
          `last_verified` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_cert_code` (`certificate_code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    echo "✅ certificate_verifications table created successfully<br>";

    // Check if templates already exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM certificate_templates");
    $templateCount = $stmt->fetchColumn();

    if ($templateCount == 0) {
        // Insert default certificate templates
        $pdo->exec("
            INSERT INTO `certificate_templates` (`category_id`, `template_name`, `background_color`, `border_color`, `text_color`, `accent_color`) VALUES
            (1, 'SQL Mastery Certificate', '#f8f9fa', '#dc3545', '#212529', '#dc3545'),
            (2, 'XSS Expert Certificate', '#f8f9fa', '#fd7e14', '#212529', '#fd7e14'),
            (3, 'Command Injection Specialist Certificate', '#f8f9fa', '#28a745', '#212529', '#28a745')
        ");
        
        echo "✅ Default certificate templates inserted successfully<br>";
    } else {
        echo "ℹ️ Certificate templates already exist<br>";
    }

    echo "<br><strong>🎉 Certificate system setup completed successfully!</strong><br>";
    echo "<br><a href='../pages/dashboard.php' class='btn btn-primary'>Go to Dashboard</a>";
    echo " <a href='../admin/dashboard.php' class='btn btn-secondary'>Admin Dashboard</a>";

} catch (PDOException $e) {
    echo "❌ Error setting up certificate system: " . $e->getMessage();
}
?>
