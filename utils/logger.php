<?php
/**
 * Comprehensive Logging Utility
 * Provides centralized logging functionality for the TryMeOut platform
 */

class Logger {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Log system events
     */
    public function logSystem($level, $category, $message, $details = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO system_logs (log_level, category, message, details, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $level,
                $category,
                $message,
                $details ? json_encode($details) : null,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Logger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log security events
     */
    public function logSecurity($event_type, $user_id = null, $username = null, $details = null, $risk_level = 'LOW') {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO security_logs (event_type, user_id, username, ip_address, user_agent, details, risk_level) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $event_type,
                $user_id,
                $username,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                $details ? json_encode($details) : null,
                $risk_level
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Logger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log audit events (data changes)
     */
    public function logAudit($action_type, $table_name, $record_id = null, $old_values = null, $new_values = null) {
        try {
            $user_id = $_SESSION['user_id'] ?? null;
            $username = $_SESSION['username'] ?? null;
            
            $stmt = $this->pdo->prepare("
                INSERT INTO audit_logs (action_type, table_name, record_id, user_id, username, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $action_type,
                $table_name,
                $record_id,
                $user_id,
                $username,
                $old_values ? json_encode($old_values) : null,
                $new_values ? json_encode($new_values) : null,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Logger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log application events
     */
    public function logApplication($module, $action, $response_code = 200, $execution_time = null, $memory_usage = null) {
        try {
            $user_id = $_SESSION['user_id'] ?? null;
            $session_id = session_id();
            
            $stmt = $this->pdo->prepare("
                INSERT INTO application_logs (module, action, user_id, session_id, request_method, request_uri, response_code, execution_time, memory_usage, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $module,
                $action,
                $user_id,
                $session_id,
                $_SERVER['REQUEST_METHOD'] ?? null,
                $_SERVER['REQUEST_URI'] ?? null,
                $response_code,
                $execution_time,
                $memory_usage,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Logger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log errors
     */
    public function logError($error_type, $error_message, $file_path = null, $line_number = null, $stack_trace = null, $request_data = null) {
        try {
            $user_id = $_SESSION['user_id'] ?? null;
            $session_id = session_id();
            
            $stmt = $this->pdo->prepare("
                INSERT INTO error_logs (error_type, error_message, file_path, line_number, stack_trace, user_id, session_id, request_data, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $error_type,
                $error_message,
                $file_path,
                $line_number,
                $stack_trace,
                $user_id,
                $session_id,
                $request_data ? json_encode($request_data) : null,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Logger Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Convenience methods for different log levels
     */
    public function info($category, $message, $details = null) {
        return $this->logSystem('INFO', $category, $message, $details);
    }
    
    public function warning($category, $message, $details = null) {
        return $this->logSystem('WARNING', $category, $message, $details);
    }
    
    public function error($category, $message, $details = null) {
        return $this->logSystem('ERROR', $category, $message, $details);
    }
    
    public function critical($category, $message, $details = null) {
        return $this->logSystem('CRITICAL', $category, $message, $details);
    }
    
    /**
     * Security event convenience methods
     */
    public function loginSuccess($user_id, $username, $details = null) {
        return $this->logSecurity('LOGIN_SUCCESS', $user_id, $username, $details, 'LOW');
    }
    
    public function loginFailed($username = null, $details = null) {
        return $this->logSecurity('LOGIN_FAILED', null, $username, $details, 'MEDIUM');
    }
    
    public function logout($user_id, $username) {
        return $this->logSecurity('LOGOUT', $user_id, $username, null, 'LOW');
    }
    
    public function suspiciousActivity($user_id, $username, $details, $risk_level = 'HIGH') {
        return $this->logSecurity('SUSPICIOUS_ACTIVITY', $user_id, $username, $details, $risk_level);
    }
    
    public function permissionDenied($user_id = null, $username = null, $details = null) {
        return $this->logSecurity('PERMISSION_DENIED', $user_id, $username, $details, 'MEDIUM');
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Clean old logs (for maintenance)
     */
    public function cleanOldLogs($days = 90) {
        try {
            $tables = ['system_logs', 'security_logs', 'audit_logs', 'application_logs', 'error_logs'];
            $deleted_total = 0;
            
            foreach ($tables as $table) {
                $stmt = $this->pdo->prepare("DELETE FROM $table WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                $stmt->execute([$days]);
                $deleted = $stmt->rowCount();
                $deleted_total += $deleted;
                
                $this->info('MAINTENANCE', "Cleaned $deleted old records from $table", ['days' => $days]);
            }
            
            return $deleted_total;
        } catch (Exception $e) {
            $this->error('MAINTENANCE', 'Failed to clean old logs: ' . $e->getMessage());
            return false;
        }
    }
}

// Global logger instance
function getLogger() {
    global $pdo;
    static $logger = null;
    
    if ($logger === null) {
        $logger = new Logger($pdo);
    }
    
    return $logger;
}

// Convenience functions
function logInfo($category, $message, $details = null) {
    return getLogger()->info($category, $message, $details);
}

function logWarning($category, $message, $details = null) {
    return getLogger()->warning($category, $message, $details);
}

function logError($category, $message, $details = null) {
    return getLogger()->error($category, $message, $details);
}

function logCritical($category, $message, $details = null) {
    return getLogger()->critical($category, $message, $details);
}

function logSecurity($event_type, $user_id = null, $username = null, $details = null, $risk_level = 'LOW') {
    return getLogger()->logSecurity($event_type, $user_id, $username, $details, $risk_level);
}

function logAudit($action_type, $table_name, $record_id = null, $old_values = null, $new_values = null) {
    return getLogger()->logAudit($action_type, $table_name, $record_id, $old_values, $new_values);
}
?>
