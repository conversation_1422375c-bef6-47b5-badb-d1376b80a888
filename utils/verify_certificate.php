<?php
require '../config/db_connect.php';

$certificate_code = $_GET['code'] ?? $_POST['certificate_code'] ?? '';
$verification_result = null;

if (!empty($certificate_code)) {
    // Get certificate details
    $stmt = $pdo->prepare("
        SELECT uc.*, u.username, c.name as category_name, ct.accent_color
        FROM user_certificates uc
        JOIN users u ON uc.user_id = u.id
        JOIN categories c ON uc.category_id = c.id
        LEFT JOIN certificate_templates ct ON c.id = ct.category_id AND ct.is_active = 1
        WHERE uc.certificate_code = ?
    ");
    $stmt->execute([$certificate_code]);
    $certificate = $stmt->fetch();

    if ($certificate) {
        // Update verification count
        $stmt = $pdo->prepare("
            INSERT INTO certificate_verifications (certificate_code, verification_count, last_verified)
            VALUES (?, 1, NOW())
            ON DUPLICATE KEY UPDATE
            verification_count = verification_count + 1,
            last_verified = NOW()
        ");
        $stmt->execute([$certificate_code]);

        $verification_result = [
            'status' => 'valid',
            'certificate' => $certificate
        ];
    } else {
        $verification_result = [
            'status' => 'invalid',
            'message' => 'Certificate not found or invalid certificate code'
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Verification - VulnPlatform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', sans-serif;
        }

        .verification-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .verification-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background-image: url("../assets/images/logo-clear.png");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 10px;
            border: 2px solid #667eea;
            padding: 10px;
        }

        .verification-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .certificate-details {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #28a745;
        }

        .verification-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .valid-badge {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }

        .invalid-badge {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        .certificate-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-card">
            <div class="text-center mb-4">
                <div class="logo"></div>
                <h1 class="mb-2">Certificate Verification</h1>
                <p class="text-muted">Verify the authenticity of TryMeOut certificates</p>
            </div>

            <?php if (!$verification_result): ?>
                <!-- Verification Form -->
                <div class="verification-form">
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="certificate_code" class="form-label">
                                <i class="fas fa-certificate me-2"></i>Certificate Code
                            </label>
                            <input type="text"
                                   class="form-control form-control-lg"
                                   id="certificate_code"
                                   name="certificate_code"
                                   placeholder="Enter certificate code (e.g., TMO01000124011ABCD)"
                                   required
                                   style="font-family: monospace;">
                            <div class="form-text">
                                Enter the certificate code found on the certificate document
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>Verify Certificate
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($verification_result['status'] === 'valid'): ?>
                <!-- Valid Certificate -->
                <div class="text-center">
                    <div class="verification-badge valid-badge">
                        <i class="fas fa-check-circle me-2"></i>Certificate Verified
                    </div>
                </div>

                <div class="certificate-details">
                    <h4 class="text-success mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Valid Certificate
                    </h4>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>Certificate Holder</h5>
                            <p class="h4 text-primary"><?= htmlspecialchars($certificate['username']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>Category</h5>
                            <p class="h4" style="color: <?= $certificate['accent_color'] ?? '#007bff' ?>;">
                                <?= htmlspecialchars($certificate['category_name']) ?>
                            </p>
                        </div>
                    </div>

                    <div class="certificate-info">
                        <div class="info-item">
                            <i class="fas fa-calendar-alt text-primary mb-2" style="font-size: 1.5rem;"></i>
                            <h6>Issue Date</h6>
                            <p class="mb-0"><?= date('F j, Y', strtotime($certificate['issued_at'])) ?></p>
                        </div>

                        <div class="info-item">
                            <i class="fas fa-trophy text-warning mb-2" style="font-size: 1.5rem;"></i>
                            <h6>Challenges Completed</h6>
                            <p class="mb-0"><?= $certificate['challenges_completed'] ?>/<?= $certificate['total_challenges'] ?></p>
                        </div>

                        <div class="info-item">
                            <i class="fas fa-percentage text-success mb-2" style="font-size: 1.5rem;"></i>
                            <h6>Completion Rate</h6>
                            <p class="mb-0">100%</p>
                        </div>

                        <div class="info-item">
                            <i class="fas fa-fingerprint text-info mb-2" style="font-size: 1.5rem;"></i>
                            <h6>Certificate ID</h6>
                            <p class="mb-0" style="font-family: monospace; font-size: 0.9rem;">
                                <?= htmlspecialchars($certificate_code) ?>
                            </p>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <a href="../utils/view_certificate.php?code=<?= $certificate_code ?>"
                           class="btn btn-success me-2" target="_blank">
                            <i class="fas fa-eye me-1"></i>View Full Certificate
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print Verification
                        </button>
                    </div>
                </div>

            <?php else: ?>
                <!-- Invalid Certificate -->
                <div class="text-center">
                    <div class="verification-badge invalid-badge">
                        <i class="fas fa-times-circle me-2"></i>Certificate Invalid
                    </div>
                </div>

                <div class="alert alert-danger">
                    <h4 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>Certificate Not Found
                    </h4>
                    <p><?= htmlspecialchars($verification_result['message']) ?></p>
                    <hr>
                    <p class="mb-0">
                        Please check the certificate code and try again. If you believe this is an error,
                        contact the certificate issuer.
                    </p>
                </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <a href="../utils/verify_certificate.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-search me-1"></i>Verify Another Certificate
                </a>
                <a href="../pages/dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-1"></i>Back to Dashboard
                </a>
            </div>

            <div class="text-center mt-4 text-muted">
                <small>
                    <i class="fas fa-shield-alt me-1"></i>
                    This verification system ensures the authenticity of TryMeOut certificates.
                    <br>
                    All certificates are digitally signed and tamper-proof.
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
</body>
</html>
