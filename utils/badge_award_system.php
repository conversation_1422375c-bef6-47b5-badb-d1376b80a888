<?php
/**
 * Centralized Badge Award System
 * Handles automatic badge awards based on category completion
 */

class BadgeAwardSystem {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Check and award badges after a challenge completion
     * @param int $user_id
     * @param int $challenge_id
     * @return array Array of newly awarded badges
     */
    public function checkAndAwardBadges($user_id, $challenge_id) {
        try {
            // Get the category of the completed challenge
            $stmt = $this->pdo->prepare("SELECT category_id FROM challenges WHERE id = ?");
            $stmt->execute([$challenge_id]);
            $challenge = $stmt->fetch();
            
            if (!$challenge) {
                return [];
            }
            
            $category_id = $challenge['category_id'];
            
            // Check if user has completed ALL challenges in this category
            $completion_check = $this->checkCategoryCompletion($user_id, $category_id);
            
            if ($completion_check['is_complete']) {
                return $this->awardCategoryBadge($user_id, $category_id);
            }
            
            // Check for other badge types (challenge count, etc.)
            return $this->checkOtherBadgeTypes($user_id);
            
        } catch (Exception $e) {
            error_log("Error in badge award system: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if user has completed all challenges in a category
     * @param int $user_id
     * @param int $category_id
     * @return array
     */
    private function checkCategoryCompletion($user_id, $category_id) {
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_challenges,
                COUNT(CASE WHEN uc.status = 'completed' THEN 1 END) as completed_challenges
            FROM challenges c
            LEFT JOIN user_progress uc ON c.id = uc.challenge_id AND uc.user_id = ?
            WHERE c.category_id = ?
        ");
        $stmt->execute([$user_id, $category_id]);
        $result = $stmt->fetch();
        
        return [
            'is_complete' => $result['total_challenges'] > 0 && $result['completed_challenges'] == $result['total_challenges'],
            'completed' => $result['completed_challenges'],
            'total' => $result['total_challenges']
        ];
    }
    
    /**
     * Award category completion badge
     * @param int $user_id
     * @param int $category_id
     * @return array
     */
    private function awardCategoryBadge($user_id, $category_id) {
        $awarded_badges = [];
        
        try {
            // Get the badge for this category
            $stmt = $this->pdo->prepare("
                SELECT id, name, description, icon, color 
                FROM badges 
                WHERE category_id = ? AND requirement_type = 'category_complete'
            ");
            $stmt->execute([$category_id]);
            $badge = $stmt->fetch();
            
            if (!$badge) {
                // Create a default badge if none exists
                $badge = $this->createDefaultCategoryBadge($category_id);
            }
            
            if ($badge) {
                // Check if badge already awarded
                $stmt = $this->pdo->prepare("
                    SELECT COUNT(*) FROM user_badges 
                    WHERE user_id = ? AND badge_id = ?
                ");
                $stmt->execute([$user_id, $badge['id']]);
                
                if ($stmt->fetchColumn() == 0) {
                    // Award the badge
                    $stmt = $this->pdo->prepare("
                        INSERT INTO user_badges (user_id, badge_id, category_id, earned_at)
                        VALUES (?, ?, ?, NOW())
                    ");
                    $stmt->execute([$user_id, $badge['id'], $category_id]);
                    
                    $awarded_badges[] = [
                        'id' => $badge['id'],
                        'name' => $badge['name'],
                        'description' => $badge['description'],
                        'icon' => $badge['icon'],
                        'color' => $badge['color'],
                        'category_id' => $category_id
                    ];
                    
                    // Log the badge award
                    $this->logBadgeAward($user_id, $badge['id'], $category_id, 'category_complete');
                }
            }
            
        } catch (Exception $e) {
            error_log("Error awarding category badge: " . $e->getMessage());
        }
        
        return $awarded_badges;
    }
    
    /**
     * Create a default badge for a category if none exists
     * @param int $category_id
     * @return array|null
     */
    private function createDefaultCategoryBadge($category_id) {
        try {
            // Get category info
            $stmt = $this->pdo->prepare("SELECT name, icon, color FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $category = $stmt->fetch();
            
            if (!$category) {
                return null;
            }
            
            // Create badge
            $badge_name = $category['name'] . ' Master';
            $badge_description = 'Completed all ' . $category['name'] . ' challenges';
            $badge_icon = $category['icon'] ?: '🏆';
            $badge_color = $category['color'] ?: '#007bff';
            
            $stmt = $this->pdo->prepare("
                INSERT INTO badges (name, description, icon, color, category_id, requirement_type)
                VALUES (?, ?, ?, ?, ?, 'category_complete')
            ");
            $stmt->execute([$badge_name, $badge_description, $badge_icon, $badge_color, $category_id]);
            
            $badge_id = $this->pdo->lastInsertId();
            
            return [
                'id' => $badge_id,
                'name' => $badge_name,
                'description' => $badge_description,
                'icon' => $badge_icon,
                'color' => $badge_color
            ];
            
        } catch (Exception $e) {
            error_log("Error creating default badge: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check for other badge types (challenge count, etc.)
     * @param int $user_id
     * @return array
     */
    private function checkOtherBadgeTypes($user_id) {
        $awarded_badges = [];
        
        try {
            // Get total completed challenges
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM user_progress 
                WHERE user_id = ? AND status = 'completed'
            ");
            $stmt->execute([$user_id]);
            $total_completed = $stmt->fetchColumn();
            
            // Check for challenge count badges
            $stmt = $this->pdo->prepare("
                SELECT id, name, description, icon, color, requirement_value
                FROM badges 
                WHERE requirement_type = 'challenge_count' 
                AND requirement_value <= ?
                ORDER BY requirement_value DESC
            ");
            $stmt->execute([$total_completed]);
            $challenge_badges = $stmt->fetchAll();
            
            foreach ($challenge_badges as $badge) {
                // Check if already awarded
                $stmt = $this->pdo->prepare("
                    SELECT COUNT(*) FROM user_badges 
                    WHERE user_id = ? AND badge_id = ?
                ");
                $stmt->execute([$user_id, $badge['id']]);
                
                if ($stmt->fetchColumn() == 0) {
                    // Award the badge
                    $stmt = $this->pdo->prepare("
                        INSERT INTO user_badges (user_id, badge_id, earned_at)
                        VALUES (?, ?, NOW())
                    ");
                    $stmt->execute([$user_id, $badge['id']]);
                    
                    $awarded_badges[] = [
                        'id' => $badge['id'],
                        'name' => $badge['name'],
                        'description' => $badge['description'],
                        'icon' => $badge['icon'],
                        'color' => $badge['color']
                    ];
                    
                    // Log the badge award
                    $this->logBadgeAward($user_id, $badge['id'], null, 'challenge_count');
                    
                    // Only award the highest applicable badge
                    break;
                }
            }
            
        } catch (Exception $e) {
            error_log("Error checking other badge types: " . $e->getMessage());
        }
        
        return $awarded_badges;
    }
    
    /**
     * Log badge award for audit purposes
     * @param int $user_id
     * @param int $badge_id
     * @param int|null $category_id
     * @param string $award_type
     */
    private function logBadgeAward($user_id, $badge_id, $category_id, $award_type) {
        try {
            // Check if admin_actions_logs table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'admin_actions_logs'");
            if ($stmt->rowCount() > 0) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO admin_actions_logs 
                    (admin_username, action_type, action_description, target_user_id, ip_address, user_agent, success, created_at)
                    VALUES (?, 'BADGE_AUTO_AWARDED', ?, ?, ?, ?, 1, NOW())
                ");
                
                $description = "Automatically awarded badge (ID: $badge_id) for $award_type" . 
                              ($category_id ? " in category $category_id" : "");
                
                $stmt->execute([
                    'SYSTEM',
                    $description,
                    $user_id,
                    $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                    $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
                ]);
            }
        } catch (Exception $e) {
            error_log("Error logging badge award: " . $e->getMessage());
        }
    }
    
    /**
     * Get user's badge progress for a category
     * @param int $user_id
     * @param int $category_id
     * @return array
     */
    public function getCategoryProgress($user_id, $category_id) {
        $completion = $this->checkCategoryCompletion($user_id, $category_id);
        
        // Check if badge already earned
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) FROM user_badges ub
            JOIN badges b ON ub.badge_id = b.id
            WHERE ub.user_id = ? AND b.category_id = ? AND b.requirement_type = 'category_complete'
        ");
        $stmt->execute([$user_id, $category_id]);
        $has_badge = $stmt->fetchColumn() > 0;
        
        return [
            'completed_challenges' => $completion['completed'],
            'total_challenges' => $completion['total'],
            'is_complete' => $completion['is_complete'],
            'has_badge' => $has_badge,
            'progress_percentage' => $completion['total'] > 0 ? 
                round(($completion['completed'] / $completion['total']) * 100) : 0
        ];
    }
}
?>
