<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

// Get user ID from query string
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die("User ID is required.");
}

$user_id = intval($_GET['id']); // Cast to integer for safety

// Fetch the user details
$stmt = $pdo->prepare("SELECT id, username, email, role FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    die("User not found.");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = htmlspecialchars(trim($_POST['username']));
    $email = htmlspecialchars(trim($_POST['email']));
    $role = $_POST['role'];

    // Input validation
    if (empty($username) || empty($email) || empty($role)) {
        $error_message = "All fields are required.";
    } else {
        try {
            require '../config/logger.php';
            $logger = getLogger();

            // Get old values for logging
            $old_stmt = $pdo->prepare("SELECT username, email, role FROM users WHERE id = ?");
            $old_stmt->execute([$user_id]);
            $old_user = $old_stmt->fetch();

            // Update user in the database
            $update_stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?");
            if ($update_stmt->execute([$username, $email, $role, $user_id])) {

                // Log changes for each field that was modified
                if ($old_user['username'] !== $username) {
                    $logger->logUserProfileChange($user_id, $old_user['username'], 'username', $old_user['username'], $username);
                }
                if ($old_user['email'] !== $email) {
                    $logger->logUserProfileChange($user_id, $username, 'email', $old_user['email'], $email);
                }
                if ($old_user['role'] !== $role) {
                    $logger->logUserProfileChange($user_id, $username, 'role', $old_user['role'], $role);
                }

                // Log admin action
                $logger->logSecurityEvent('ADMIN_USER_EDIT', $_SESSION['username'] ?? 'admin', $_SESSION['user_id'] ?? null, 'MEDIUM', [
                    'target_user_id' => $user_id,
                    'target_username' => $username,
                    'changes' => [
                        'username' => ['old' => $old_user['username'], 'new' => $username],
                        'email' => ['old' => $old_user['email'], 'new' => $email],
                        'role' => ['old' => $old_user['role'], 'new' => $role]
                    ],
                    'admin_action' => 'user_profile_edit'
                ]);

                $_SESSION['message'] = "User updated successfully.";
                header('Location: ../admin/dashboard.php');
                exit;
            } else {
                $error_message = "Failed to update user.";
            }
        } catch (Exception $e) {
            error_log("Admin user edit error: " . $e->getMessage());
            $error_message = "An error occurred while updating the user.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Edit User</title>
    <link rel="icon" type="image/png" href="assets/images/logo-clear.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container my-5">
        <h1>Edit User</h1>
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>
        <form method="POST" action="edit_user.php?id=<?= htmlspecialchars($user['id']) ?>">
            <div class="mb-3">
                <label for="username" class="form-label">Username:</label>
                <input type="text" name="username" id="username" class="form-control" value="<?= htmlspecialchars($user['username']) ?>" required>
            </div>
            <div class="mb-3">
                <label for="email" class="form-label">Email:</label>
                <input type="email" name="email" id="email" class="form-control" value="<?= htmlspecialchars($user['email']) ?>" required>
            </div>
            <div class="mb-3">
                <label for="role" class="form-label">Role:</label>
                <select name="role" id="role" class="form-select" required>
                    <option value="student" <?= $user['role'] === 'student' ? 'selected' : '' ?>>Student</option>
                    <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>Admin</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Update User</button>
            <a href="../admin/dashboard.php" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</body>
</html>
