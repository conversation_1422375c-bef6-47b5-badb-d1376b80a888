<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

// Get user ID from query string
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die("User ID is required.");
}

$user_id = $_GET['id'];

// Delete the user
$stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
$stmt->execute([$user_id]);

$_SESSION['message'] = "User deleted successfully.";
header('Location: ../admin/dashboard.php');
exit;
?>
