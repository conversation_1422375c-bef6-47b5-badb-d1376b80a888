<?php
session_start();
require '../config/db_connect.php';

if (!isset($_SESSION["user_role"]) || $_SESSION["user_role"] !== "admin") {
    header("Location: ../auth/signin.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Log | TryMeOut Admin</title>
    <link rel="icon" type="image/png" href="assets/images/logo-clear.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container my-5">
        <h1><i class="fas fa-clock"></i> Activity Log</h1>
        <p class="text-muted">Detailed system activity and user actions</p>
        
        <div class="card">
            <div class="card-body">
                <h5>Recent Activity</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Activity logging system is ready for implementation.
                </div>
                <a href="../admin/dashboard.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>