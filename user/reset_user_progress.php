<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied. Admin privileges required.']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['user_id']) || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$user_id = (int)$input['user_id'];
$action = $input['action'];

if ($action !== 'reset_progress') {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

// Validate user exists
try {
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $username = $user['username'];
    
    // Begin transaction
    $pdo->beginTransaction();
    
    // 1. Delete user progress (completed challenges)
    $stmt = $pdo->prepare("DELETE FROM user_progress WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $progress_deleted = $stmt->rowCount();
    
    // 2. Delete user certificates
    $certificates_deleted = 0;
    try {
        $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $certificates_deleted = $stmt->rowCount();
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 3. Delete user badges
    $badges_deleted = 0;
    try {
        $stmt = $pdo->prepare("DELETE FROM user_badges WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $badges_deleted = $stmt->rowCount();
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 4. Reset any session-based progress counters (if stored in database)
    try {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 5. Delete any stored challenge attempts or hints used
    try {
        $stmt = $pdo->prepare("DELETE FROM user_challenge_attempts WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 6. Reset user statistics (if stored separately)
    try {
        $stmt = $pdo->prepare("DELETE FROM user_statistics WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 7. Delete any achievement records
    try {
        $stmt = $pdo->prepare("DELETE FROM user_achievements WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        // Table might not exist, continue
    }
    
    // 8. Clean up any certificate files from filesystem
    $certificate_files_deleted = 0;
    $certificates_dir = 'certificates/';
    if (is_dir($certificates_dir)) {
        $files = glob($certificates_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                $filename = basename($file);
                // Check if filename contains user-specific patterns
                if (strpos($filename, "user_{$user_id}_") !== false || 
                    strpos($filename, "_{$user_id}.") !== false) {
                    if (unlink($file)) {
                        $certificate_files_deleted++;
                    }
                }
            }
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    // Log the admin action
    $log_message = "Admin {$_SESSION['username']} reset all progress for user {$username} (ID: {$user_id})";
    error_log($log_message);
    
    // Prepare success response with details
    $response = [
        'success' => true,
        'message' => 'User progress reset successfully',
        'details' => [
            'username' => $username,
            'user_id' => $user_id,
            'progress_records_deleted' => $progress_deleted,
            'certificates_deleted' => $certificates_deleted,
            'badges_deleted' => $badges_deleted,
            'certificate_files_deleted' => $certificate_files_deleted,
            'reset_by' => $_SESSION['username'],
            'reset_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    
    error_log("Error resetting user progress: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred while resetting progress'
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on any other error
    $pdo->rollBack();
    
    error_log("Error resetting user progress: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred'
    ]);
}
?>
