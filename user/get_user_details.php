<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied. Admin privileges required.']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Get user ID from query parameter
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit;
}

try {
    // Get basic user information
    $stmt = $pdo->prepare("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.role,
            u.created_at,
            COUNT(DISTINCT up.id) as completed_challenges,
            MAX(up.completed_at) as last_activity
        FROM users u
        LEFT JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        WHERE u.id = ?
        GROUP BY u.id, u.username, u.email, u.role, u.created_at
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get certificates count
    $certificates_count = 0;
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_certificates WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $certificates_count = $stmt->fetchColumn();
    } catch (PDOException $e) {
        // Table might not exist
    }
    
    // Get badges count
    $badges_count = 0;
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_badges WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $badges_count = $stmt->fetchColumn();
    } catch (PDOException $e) {
        // Table might not exist
    }
    
    // Get category progress
    $stmt = $pdo->prepare("
        SELECT 
            c.name,
            c.id as category_id,
            COUNT(ch.id) as total_challenges,
            COUNT(CASE WHEN up.status = 'completed' THEN 1 END) as completed_challenges
        FROM categories c
        LEFT JOIN challenges ch ON c.id = ch.category_id
        LEFT JOIN user_progress up ON ch.id = up.challenge_id AND up.user_id = ?
        GROUP BY c.id, c.name
        ORDER BY c.id
    ");
    $stmt->execute([$user_id]);
    $category_progress = $stmt->fetchAll();
    
    // Format category progress
    $formatted_category_progress = [];
    foreach ($category_progress as $cat) {
        $formatted_category_progress[] = [
            'name' => $cat['name'],
            'completed' => (int)$cat['completed_challenges'],
            'total' => (int)$cat['total_challenges']
        ];
    }
    
    // Get recent activities (last 10 completed challenges)
    $stmt = $pdo->prepare("
        SELECT 
            c.title as challenge_title,
            up.completed_at,
            cat.name as category_name
        FROM user_progress up
        JOIN challenges c ON up.challenge_id = c.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE up.user_id = ? AND up.status = 'completed'
        ORDER BY up.completed_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $recent_activities = $stmt->fetchAll();
    
    // Format recent activities
    $formatted_activities = [];
    foreach ($recent_activities as $activity) {
        $formatted_activities[] = [
            'challenge_title' => $activity['challenge_title'],
            'completed_at' => date('M j, Y H:i', strtotime($activity['completed_at'])),
            'category_name' => $activity['category_name']
        ];
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'user' => [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'created_at' => date('M j, Y', strtotime($user['created_at'])),
            'completed_challenges' => (int)$user['completed_challenges'],
            'certificates_count' => (int)$certificates_count,
            'badges_count' => (int)$badges_count,
            'last_activity' => $user['last_activity'] ? date('M j, Y H:i', strtotime($user['last_activity'])) : null,
            'category_progress' => $formatted_category_progress,
            'recent_activities' => $formatted_activities
        ]
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    error_log("Error fetching user details: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred while fetching user details'
    ]);
} catch (Exception $e) {
    error_log("Error fetching user details: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred'
    ]);
}
?>
