<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied. Admin privileges required.']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Get badge ID from query parameter
$badge_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$badge_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid badge ID']);
    exit;
}

try {
    // Get badge details with user and category information
    $stmt = $pdo->prepare("
        SELECT 
            ub.id,
            ub.user_id,
            ub.category_id,
            ub.badge_type,
            ub.earned_at,
            u.username,
            u.email,
            c.name as category_name
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        LEFT JOIN categories c ON ub.category_id = c.id
        WHERE ub.id = ?
    ");
    $stmt->execute([$badge_id]);
    $badge = $stmt->fetch();
    
    if (!$badge) {
        echo json_encode(['success' => false, 'message' => 'Badge not found']);
        exit;
    }
    
    // Format the response
    $response = [
        'success' => true,
        'badge' => [
            'id' => (int)$badge['id'],
            'user_id' => (int)$badge['user_id'],
            'category_id' => (int)$badge['category_id'],
            'username' => $badge['username'],
            'email' => $badge['email'],
            'category_name' => $badge['category_name'] ?: 'General',
            'badge_type' => $badge['badge_type'] ?: 'completion',
            'earned_at' => date('M j, Y H:i', strtotime($badge['earned_at']))
        ]
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    error_log("Error fetching badge details: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred while fetching badge details'
    ]);
} catch (Exception $e) {
    error_log("Error fetching badge details: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred'
    ]);
}
?>
