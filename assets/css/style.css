* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f4f4f9;
    color: #333;
}

header {
    background-color: #222;
    color: white;
    padding: 1rem 0;
}

.container {
    width: 90%;
    margin: 0 auto;
    max-width: 1200px;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 1.5rem;
}

.nav-links a {
    text-decoration: none;
    color: white;
    font-size: 1rem;
    transition: color 0.3s;
}

.nav-links a:hover,
.nav-links .active {
    color: #4caf50;
}

.cta-btn {
    background-color: #4caf50;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    color: white;
}

.hero {
    text-align: center;
    padding: 5rem 0;
}

.hero-text h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.primary-btn {
    text-decoration: none;
    background-color: #4caf50;
    color: white;
    padding: 1rem 2rem;
    border-radius: 5px;
    font-size: 1.1rem;
}

.features {
    display: flex;
    justify-content: space-between;
    margin-top: 4rem;
    gap: 2rem;
}

.feature-item {
    text-align: center;
    width: 30%;
}

.feature-item h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

footer {
    background-color: #222;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 4rem;
}
