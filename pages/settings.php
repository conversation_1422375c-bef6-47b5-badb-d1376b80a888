<?php
session_start();

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require '../config/db_connect.php';
    require '../config/logger.php';
    require '../config/upload_config.php'; // Load upload configuration

    if (!isset($_SESSION['user_id'])) {
        header('Location: ../auth/signin.php');
        exit;
    }

    // Log settings access
    try {
        $logger = getLogger();
        $logger->logUserActivity('SETTINGS_ACCESS', $_SESSION['user_id'], [
            'username' => $_SESSION['username'] ?? 'Unknown',
            'role' => $_SESSION['user_role'] ?? 'student'
        ]);
    } catch (Exception $e) {
        // Log error but continue
        error_log("Logger error in settings.php: " . $e->getMessage());
    }

    // Get user data from the database
    $user_id = $_SESSION['user_id'];
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // Ensure user data exists and has required fields
    if (!$user || empty($user)) {
        // Fallback if user not found
        $user = [
            'id' => $user_id,
            'username' => $_SESSION['username'] ?? 'Unknown User',
            'email' => $_SESSION['email'] ?? 'No email available',
            'profile_picture' => null
        ];
    } else {
        // Ensure email field exists and has a value
        if (empty($user['email'])) {
            $user['email'] = $_SESSION['email'] ?? 'No email available';
        }
    }

    // Handle profile picture path
    $profile_picture = '../assets/uploads/default.jpg';
    if (!empty($user['profile_picture'])) {
        // Handle different path formats
        if ($user['profile_picture'] === 'default-picture.jpg') {
            $profile_picture = '../assets/uploads/default.jpg';
        } elseif (strpos($user['profile_picture'], 'assets/uploads/') === 0) {
            // Path starts with assets/uploads/ - add ../
            $profile_picture = '../' . $user['profile_picture'];
        } elseif (strpos($user['profile_picture'], '../assets/uploads/') === 0) {
            // Path already has ../ prefix
            $profile_picture = $user['profile_picture'];
        } else {
            // Assume it's a filename only, add full path
            $profile_picture = '../assets/uploads/' . $user['profile_picture'];
        }

        // Check if file actually exists, fallback to default if not
        if (!file_exists($profile_picture)) {
            error_log("Profile picture file not found: " . $profile_picture . " for user ID: " . $user_id);
            $profile_picture = '../assets/uploads/default.jpg';
        }
    }

    // error_log("Profile picture path resolved to: " . $profile_picture . " for user ID: " . $user_id);

} catch (Exception $e) {
    // Handle initialization errors
    error_log("Settings page initialization error: " . $e->getMessage());
    $_SESSION['error_message'] = "System error occurred. Please try again later.";
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $success_messages = [];
        $error_messages = [];

        // Update name
        if (isset($_POST['name']) && !empty(trim($_POST['name']))) {
            $new_name = trim(htmlspecialchars($_POST['name']));

            // Validate username
            if (strlen($new_name) < 3) {
                $error_messages[] = "Username must be at least 3 characters long.";
            } elseif (strlen($new_name) > 50) {
                $error_messages[] = "Username must be less than 50 characters.";
            } else {
                try {
                    // Get old username for logging
                    $old_username = $_SESSION['username'];

                    $stmt = $pdo->prepare("UPDATE users SET username = ? WHERE id = ?");
                    if ($stmt->execute([$new_name, $user_id])) {
                        $success_messages[] = "Username updated successfully!";
                        $_SESSION['username'] = $new_name; // Update session

                        // Log the username change (includes automatic security logging)
                        $logger->logUserProfileChange($user_id, $old_username, 'username', $old_username, $new_name);
                    } else {
                        $error_messages[] = "Failed to update username.";
                    }
                } catch (PDOException $e) {
                    error_log("Username update error: " . $e->getMessage());
                    $error_messages[] = "Database error occurred while updating username.";
                }
            }
        }

        // Update password
        if (isset($_POST['password']) && !empty($_POST['password'])) {
            $new_password = $_POST['password'];
            $verify_password = $_POST['verify_password'] ?? '';

            // Validate password
            if (strlen($new_password) < 12) {
                $error_messages[] = "Password must be at least 12 characters long.";
            } elseif ($new_password !== $verify_password) {
                $error_messages[] = "Passwords do not match.";
            } else {
                try {
                    // Get current password hash for logging (we'll redact it)
                    $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $old_password_hash = $stmt->fetchColumn();

                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
                    if ($stmt->execute([$hashed_password, $user_id])) {
                        $success_messages[] = "Password updated successfully!";

                        // Log the password change
                        $logger->logUserProfileChange($user_id, $_SESSION['username'], 'password_hash', 'REDACTED', 'REDACTED');

                        // Log security event
                        $logger->logSecurityEvent('PASSWORD_CHANGED', $_SESSION['username'], $user_id, 'MEDIUM', [
                            'changed_by' => 'user',
                            'method' => 'settings_page',
                            'password_strength' => strlen($new_password) . '_chars'
                        ]);
                    } else {
                        $error_messages[] = "Failed to update password.";
                    }
                } catch (PDOException $e) {
                    error_log("Password update error: " . $e->getMessage());
                    $error_messages[] = "Database error occurred while updating password.";
                }
            }
        }

        // Update profile picture using secure upload
        if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] == 0) {
            try {
                // Debug: Log file upload attempt
                error_log("Profile picture upload attempt for user ID: " . $user_id);
                error_log("File details: " . print_r($_FILES['profile_picture'], true));

                // Check if secure upload file exists
                if (!file_exists('../utils/secure_file_upload.php')) {
                    $error_messages[] = "Upload system is not available. Please contact administrator.";
                    error_log("Secure upload file not found: ../utils/secure_file_upload.php");
                } else {
                    require_once '../utils/secure_file_upload.php';

                    $uploader = new SecureFileUpload();
                    $upload_result = $uploader->uploadProfilePicture($_FILES['profile_picture'], $user_id);

                    if ($upload_result) {
                        try {
                            // Get old profile picture for logging
                            $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE id = ?");
                            $stmt->execute([$user_id]);
                            $old_profile_picture = $stmt->fetchColumn();

                            // Update the database with the relative path
                            $stmt = $pdo->prepare("UPDATE users SET profile_picture = ? WHERE id = ?");
                            if ($stmt->execute([$upload_result, $user_id])) {
                                $success_messages[] = "Profile picture updated successfully!";
                                error_log("Profile picture updated successfully for user ID: " . $user_id . " with path: " . $upload_result);

                                // Log the profile picture change
                                $logger->logUserProfileChange($user_id, $_SESSION['username'], 'profile_picture', $old_profile_picture, $upload_result);

                                // Log security event with enhanced details
                                $logger->logSecurityEvent('PROFILE_PICTURE_CHANGED', $_SESSION['username'], $user_id, 'LOW', [
                                    'old_picture' => $old_profile_picture,
                                    'new_picture' => $upload_result,
                                    'original_filename' => $_FILES['profile_picture']['name'],
                                    'generated_filename' => basename($upload_result),
                                    'file_size' => $_FILES['profile_picture']['size'],
                                    'file_type' => $_FILES['profile_picture']['type'],
                                    'naming_pattern' => 'user_id_prefix',
                                    'changed_by' => 'user'
                                ]);

                                // Refresh user data
                                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                                $stmt->execute([$user_id]);
                                $user = $stmt->fetch();

                                // Recalculate profile picture path with the new data
                                if (!empty($user['profile_picture'])) {
                                    if ($user['profile_picture'] === 'default-picture.jpg') {
                                        $profile_picture = '../assets/uploads/default.jpg';
                                    } elseif (strpos($user['profile_picture'], 'assets/uploads/') === 0) {
                                        $profile_picture = '../' . $user['profile_picture'];
                                    } elseif (strpos($user['profile_picture'], '../assets/uploads/') === 0) {
                                        $profile_picture = $user['profile_picture'];
                                    } else {
                                        $profile_picture = '../assets/uploads/' . $user['profile_picture'];
                                    }
                                } else {
                                    $profile_picture = '../assets/uploads/default.jpg';
                                }
                            } else {
                                $error_messages[] = "Failed to save profile picture to database.";
                                error_log("Database update failed for user ID: " . $user_id);
                            }
                        } catch (PDOException $e) {
                            error_log("Profile picture database update error: " . $e->getMessage());
                            $error_messages[] = "Database error occurred while updating profile picture.";
                        }
                    } else {
                        $upload_errors = $uploader->getErrors();
                        $error_messages = array_merge($error_messages, $upload_errors);
                        error_log("Upload failed with errors: " . implode(', ', $upload_errors));

                        // Add helpful suggestions
                        $suggestions = $uploader->getSuggestions($_FILES['profile_picture']);
                        if (!empty($suggestions)) {
                            $error_messages[] = "<strong>Suggestions to fix:</strong><br>• " . implode("<br>• ", $suggestions);
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("Profile picture upload error: " . $e->getMessage());
                $error_messages[] = "Error occurred during file upload: " . $e->getMessage();
            }
        } elseif (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] != 0) {
            // Handle file upload errors
            $upload_error_messages = [
                UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini.',
                UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.',
                UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded.',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded.',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder.',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk.',
                UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload.'
            ];

            $error_code = $_FILES['profile_picture']['error'];
            $error_message = isset($upload_error_messages[$error_code]) ? $upload_error_messages[$error_code] : 'Unknown upload error.';
            $error_messages[] = "File upload error: " . $error_message;
            error_log("File upload error code " . $error_code . ": " . $error_message);
        }

        // Set session messages
        if (!empty($success_messages)) {
            $_SESSION['success_message'] = implode(' ', $success_messages);
        }
        if (!empty($error_messages)) {
            $_SESSION['error_message'] = implode(' ', $error_messages);
        }

        // Reload the page after updates
        header("Location: settings.php");
        exit;

    } catch (Exception $e) {
        error_log("Settings form processing error: " . $e->getMessage());
        $_SESSION['error_message'] = "An unexpected error occurred. Please try again.";
        header("Location: settings.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Settings</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Learning Platform Colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --accent-color: #10b981;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;

            /* Neutral Palette */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Learning Platform Theme */
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-bg: rgba(255, 255, 255, 0.98);
            --sidebar-bg: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);

            /* Shadows & Effects */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;

            /* Transitions */
            --transition-fast: all 0.15s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--bg-gradient);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--gray-700);
            line-height: 1.6;
            font-weight: 400;
        }

        /* Modern Learning Sidebar */
        .sidebar {
            width: 300px;
            min-height: 100vh;
            background: var(--sidebar-bg);
            padding: 0;
            color: white;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-2xl);
            backdrop-filter: blur(20px);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
            padding: 32px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .profile-pic {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            border: 4px solid rgba(255,255,255,0.2);
            transition: var(--transition-normal);
        }

        .profile-pic:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(99, 102, 241, 0.4);
        }

        .profile-info h4 {
            font-size: 1.3rem;
            color: white;
            font-weight: 700;
            margin: 15px 0 5px;
        }

        .profile-info p {
            font-size: 0.9rem;
            color: #bdc3c7;
            margin: 0;
            opacity: 0.8;
        }

        /* Modern Learning Navigation */
        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 16px 24px;
            font-size: 0.95rem;
            border-radius: var(--radius-md);
            margin: 4px 16px;
            transition: var(--transition-normal);
            font-weight: 500;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(99, 102, 241, 0.2);
            color: white;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
            font-weight: 600;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: white;
            border-radius: 2px;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Sidebar Footer / Copyright */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 5px;
        }

        .copyright-info {
            text-align: center;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        .copyright-info p {
            margin: 0;
        }

        .main-content {
            margin-left: 300px;
            min-height: 100vh;
            background: var(--gray-50);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(6, 182, 212, 0.03) 100%);
            pointer-events: none;
        }

        .content {
            padding: 32px;
            position: relative;
            z-index: 1;
        }

        /* Modern Settings Cards */
        .settings-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 32px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            margin-bottom: 32px;
            position: relative;
            overflow: hidden;
        }

        .settings-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        /* Modern Settings Layout */
        .settings-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .settings-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: var(--radius-xl);
            padding: 32px;
            margin-bottom: 32px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .settings-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .settings-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .settings-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        /* Settings Layout */
        .settings-form-container {
            max-width: 800px;
            margin: 0 auto;
        }



        /* Settings Form Card */
        .settings-form-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 32px;
            border: 1px solid var(--gray-200);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .settings-form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        /* Modern Form Controls */
        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
            font-size: 0.95rem;
            display: block;
        }

        .form-control {
            width: 100%;
            border-radius: var(--radius-md);
            border: 2px solid var(--gray-200);
            padding: 14px 16px;
            font-size: 0.95rem;
            transition: var(--transition-normal);
            background: white;
            font-family: inherit;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .form-control:hover {
            border-color: var(--gray-300);
        }

        .form-control[type="file"] {
            padding: 12px 16px;
            background: var(--gray-50);
        }

        .form-text {
            color: var(--gray-500);
            font-size: 0.85rem;
            margin-top: 6px;
            display: block;
        }

        /* Password Field with Toggle */
        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-sm);
            transition: var(--transition-normal);
        }

        .password-toggle:hover {
            background: var(--gray-100);
            color: var(--primary-color);
        }

        /* Editable Field Styles */
        .editable-field-container {
            position: relative;
        }

        .field-display {
            background: var(--gray-50);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: 14px 16px;
            transition: var(--transition-normal);
        }

        .field-display:hover {
            border-color: var(--gray-300);
            background: var(--gray-100);
        }

        .display-value {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .current-value {
            font-size: 0.95rem;
            color: var(--gray-700);
            font-weight: 500;
            flex: 1;
        }

        .btn-edit {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: var(--radius-sm);
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-edit:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .field-edit {
            animation: slideDown 0.3s ease;
        }

        .edit-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .edit-buttons {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .btn-save {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--radius-sm);
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-save:hover {
            background: #16a34a;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-cancel {
            background: var(--gray-500);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--radius-sm);
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-cancel:hover {
            background: var(--gray-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Modern Buttons */
        .btn {
            border-radius: var(--radius-md);
            padding: 14px 28px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: var(--transition-normal);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        }

        .btn-secondary {
            background: var(--gray-500);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--gray-600);
            transform: translateY(-1px);
        }



        /* Success/Error Messages */
        .alert {
            border-radius: var(--radius-md);
            border: none;
            padding: 16px 20px;
            font-weight: 500;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        @media (max-width: 576px) {
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <div class="profile-info text-center">
                <img src="<?= htmlspecialchars($profile_picture); ?>?v=<?= time(); ?>" alt="Profile Picture" class="profile-pic">
                <h4><?= htmlspecialchars($user['username'] ?? 'Unknown User'); ?></h4>
                <p><?= htmlspecialchars($user['email'] ?? $_SESSION['email'] ?? 'No email'); ?></p>
            </div>
        </div>
        <div class="p-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../utils/start_challenge.php">
                        <i class="fas fa-flag"></i>Challenges
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="leaderboard.php">
                        <i class="fas fa-trophy"></i>Leaderboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#certificates" onclick="scrollToCertificates()">
                        <i class="fas fa-certificate"></i>Certificates
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                </li>
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../admin/dashboard.php">
                        <i class="fas fa-user-shield"></i>Admin Panel
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Copyright Section -->
        <div class="sidebar-footer">
            <div class="copyright-info">
                <p>&copy; <?= date('Y') ?> TryMeOut. All Rights Reserved.</p>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content">
            <div class="settings-container">
                <!-- Modern Settings Header -->
                <div class="settings-header">
                    <h1>⚙️ Account Settings</h1>
                    <p>Manage your profile, security, and preferences</p>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($_SESSION['success_message']) ?>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= htmlspecialchars($_SESSION['error_message']) ?>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['upload_success'])): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($_SESSION['upload_success']) ?>
                    </div>
                    <?php unset($_SESSION['upload_success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['upload_error'])): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= htmlspecialchars($_SESSION['upload_error']) ?>
                    </div>
                    <?php unset($_SESSION['upload_error']); ?>
                <?php endif; ?>

                <!-- Settings Form Container -->
                <div class="settings-form-container">
                    <div class="settings-form-card">
                        <form action="settings.php" method="POST" enctype="multipart/form-data">
                            <!-- Profile Picture Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    Profile Picture
                                </div>
                                <div class="form-group">
                                    <label for="profile_picture" class="form-label">Upload New Picture</label>
                                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/jpeg,image/jpg,image/png,image/gif">
                                    <small class="form-text">Supported formats: JPG, PNG, GIF. Max size: 5MB</small>

                                    <!-- Current profile picture preview -->
                                    <div class="mt-3">
                                        <label class="form-label">Current Profile Picture:</label>
                                        <div class="d-flex align-items-center gap-3">
                                            <img src="<?= htmlspecialchars($profile_picture); ?>?v=<?= time(); ?>" alt="Current Profile Picture"
                                                 style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Account Information
                                </div>
                                <div class="form-group">
                                    <label for="name" class="form-label">Username</label>
                                    <div class="editable-field-container">
                                        <!-- Display Mode -->
                                        <div id="username-display" class="field-display">
                                            <div class="display-value">
                                                <span class="current-value"><?= htmlspecialchars($user['username'] ?? ''); ?></span>
                                                <button type="button" class="btn-edit" onclick="enableUsernameEdit()">
                                                    <i class="fas fa-edit"></i>
                                                    Edit
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Edit Mode -->
                                        <div id="username-edit" class="field-edit" style="display: none;">
                                            <div class="edit-controls">
                                                <input type="text" class="form-control" id="name" value="<?= htmlspecialchars($user['username'] ?? ''); ?>" required>
                                                <div class="edit-buttons">
                                                    <button type="button" class="btn-save" onclick="saveUsernameEdit()">
                                                        <i class="fas fa-check"></i>
                                                        Save
                                                    </button>
                                                    <button type="button" class="btn-cancel" onclick="cancelUsernameEdit()">
                                                        <i class="fas fa-times"></i>
                                                        Cancel
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="form-text">This is how your name will appear to other users</small>
                                </div>
                            </div>

                            <!-- Security Section -->
                            <div class="form-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    Security Settings
                                </div>
                                <div class="form-group">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="editable-field-container">
                                        <!-- Display Mode -->
                                        <div id="password-display" class="field-display">
                                            <div class="display-value">
                                                <span class="current-value">••••••••••••</span>
                                                <button type="button" class="btn-edit" onclick="enablePasswordEdit()">
                                                    <i class="fas fa-edit"></i>
                                                    Change Password
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Edit Mode -->
                                        <div id="password-edit" class="field-edit" style="display: none;">
                                            <div class="edit-controls">
                                                <div class="password-field">
                                                    <input type="password" class="form-control" id="password" placeholder="Enter new password">
                                                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                                        <i class="fas fa-eye" id="password-icon"></i>
                                                    </button>
                                                </div>
                                                <div class="password-field mt-3">
                                                    <input type="password" class="form-control" id="verify_password" placeholder="Confirm new password">
                                                    <button type="button" class="password-toggle" onclick="togglePassword('verify_password')">
                                                        <i class="fas fa-eye" id="verify_password-icon"></i>
                                                    </button>
                                                </div>
                                                <div class="edit-buttons">
                                                    <button type="button" class="btn-save" onclick="savePasswordEdit()">
                                                        <i class="fas fa-check"></i>
                                                        Save Password
                                                    </button>
                                                    <button type="button" class="btn-cancel" onclick="cancelPasswordEdit()">
                                                        <i class="fas fa-times"></i>
                                                        Cancel
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="form-text">Password must be 12-20 characters with letters, numbers, and special characters</small>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
    <script>
        // Modern password visibility toggle
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '-icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Form validation (now only for profile picture uploads)
        document.querySelector('form').addEventListener('submit', function(e) {
            // The main form now only handles profile picture uploads
            // Username and password changes are handled separately
            console.log('Form submitted for profile picture upload');
        });

        // File upload validation and preview
        document.getElementById('profile_picture').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // File size validation (5MB = 5 * 1024 * 1024 bytes)
                const maxSize = 5 * 1024 * 1024;
                if (file.size > maxSize) {
                    alert('File size too large. Maximum allowed size is 5MB.');
                    this.value = '';
                    return;
                }

                // File type validation
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Invalid file type. Only JPG, PNG, and GIF files are allowed.');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const profilePic = document.querySelector('.profile-pic');
                    if (profilePic) {
                        profilePic.src = e.target.result;
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        function scrollToCertificates() {
            window.location.href = 'dashboard.php#certificates';
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 300);
            });
        }, 5000);

        // Editable Field Functions
        let originalUsername = '<?= htmlspecialchars($user['username'] ?? ''); ?>';

        function enableUsernameEdit() {
            document.getElementById('username-display').style.display = 'none';
            document.getElementById('username-edit').style.display = 'block';
            document.getElementById('name').focus();
        }

        function cancelUsernameEdit() {
            document.getElementById('username-edit').style.display = 'none';
            document.getElementById('username-display').style.display = 'block';
            document.getElementById('name').value = originalUsername;
        }

        function saveUsernameEdit() {
            const newUsername = document.getElementById('name').value.trim();

            if (!newUsername) {
                alert('Username cannot be empty');
                return;
            }

            if (newUsername === originalUsername) {
                cancelUsernameEdit();
                return;
            }

            // Create a form to submit the username change
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const usernameInput = document.createElement('input');
            usernameInput.type = 'hidden';
            usernameInput.name = 'name';
            usernameInput.value = newUsername;

            form.appendChild(usernameInput);
            document.body.appendChild(form);
            form.submit();
        }

        function enablePasswordEdit() {
            document.getElementById('password-display').style.display = 'none';
            document.getElementById('password-edit').style.display = 'block';
            document.getElementById('password').focus();
        }

        function cancelPasswordEdit() {
            document.getElementById('password-edit').style.display = 'none';
            document.getElementById('password-display').style.display = 'block';
            document.getElementById('password').value = '';
            document.getElementById('verify_password').value = '';
        }

        function savePasswordEdit() {
            const password = document.getElementById('password').value;
            const verifyPassword = document.getElementById('verify_password').value;

            if (!password) {
                alert('Please enter a new password');
                return;
            }

            if (password !== verifyPassword) {
                alert('Passwords do not match. Please check and try again.');
                return;
            }

            if (password.length < 12) {
                alert('Password must be at least 12 characters long.');
                return;
            }

            // Validate password policy
            const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[^\w\s]).{12,20}$/;
            if (!passwordRegex.test(password)) {
                alert('Password must be 12-20 characters long, contain at least one letter, one number, and one special character (no spaces allowed).');
                return;
            }

            // Create a form to submit the password change
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const passwordInput = document.createElement('input');
            passwordInput.type = 'hidden';
            passwordInput.name = 'password';
            passwordInput.value = password;

            const verifyPasswordInput = document.createElement('input');
            verifyPasswordInput.type = 'hidden';
            verifyPasswordInput.name = 'verify_password';
            verifyPasswordInput.value = verifyPassword;

            form.appendChild(passwordInput);
            form.appendChild(verifyPasswordInput);
            document.body.appendChild(form);
            form.submit();
        }
    </script>
</body>
</html>
