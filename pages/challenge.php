<?php
session_start();
require '../config/db_connect.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/signin.php");
    exit;
}

if (!isset($_GET['challenge_id'])) {
    echo "Challenge ID is missing.";
    exit;
}

$challenge_id = (int) $_GET['challenge_id'];

// Fetch the challenge
$stmt = $pdo->prepare("SELECT * FROM challenges WHERE id = ?");
$stmt->execute([$challenge_id]);
$challenge = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$challenge) {
    echo "Challenge not found!";
    exit;
}

// Handle form submission
$message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_answer = trim($_POST['answer']);

    if ($user_answer === $challenge['solution']) {
        // Mark progress as completed
        $stmt = $pdo->prepare("REPLACE INTO user_progress (user_id, challenge_id, status, completed_at) VALUES (?, ?, 'completed', NOW())");
        $stmt->execute([$_SESSION['user_id'], $challenge_id]);

        // Find next challenge in same category
        $nextStmt = $pdo->prepare("SELECT id FROM challenges WHERE category_id = ? AND level = ? LIMIT 1");
        $nextStmt->execute([$challenge['category_id'], $challenge['level'] + 1]);
        $next = $nextStmt->fetch();

        if ($next) {
            header("Location: challenge.php?challenge_id=" . $next['id']);
            exit;
        } else {
            $message = "Congratulations! You’ve completed all challenges in this category.";
        }
    } else {
        $message = "Incorrect. Try again.";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($challenge['title']) ?> - TryMeOut Challenge</title>
    <link rel="icon" type="image/png" href="assets/images/logo-clear.png">
    <style>
        body {
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            padding: 30px;
        }
        input[type="text"], button {
            padding: 10px;
            margin-top: 10px;
            width: 300px;
            background: #111;
            color: #0f0;
            border: 1px solid #0f0;
        }
    </style>
</head>
<body>

<h1><?= htmlspecialchars($challenge['title']) ?> (<?= htmlspecialchars($challenge['difficulty']) ?>)</h1>
<p><?= htmlspecialchars($challenge['description']) ?></p>

<?php if (!empty($challenge['hint'])): ?>
    <p><strong>Hint:</strong> <?= htmlspecialchars($challenge['hint']) ?></p>
<?php endif; ?>

<form method="post">
    <input type="text" name="answer" placeholder="Enter your answer">
    <br>
    <button type="submit">Submit</button>
</form>

<?php if ($message): ?>
    <p><?= htmlspecialchars($message) ?></p>
<?php endif; ?>

</body>
</html>
