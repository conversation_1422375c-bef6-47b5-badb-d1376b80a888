<?php
session_start();
require '../config/db_connect.php';
require '../config/logger.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/signin.php');
    exit;
}

// Log leaderboard access
$logger = getLogger();
$logger->logUserActivity('LEADERBOARD_ACCESS', $_SESSION['user_id'], [
    'username' => $_SESSION['username'],
    'role' => $_SESSION['user_role']
]);

// Get current user info with profile picture
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare("SELECT id, username, email, profile_picture FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Ensure user data exists and has required fields
if (!$user || empty($user)) {
    // Fallback if user not found
    $user = [
        'id' => $user_id,
        'username' => $_SESSION['username'] ?? 'Unknown User',
        'email' => $_SESSION['email'] ?? 'No email available',
        'profile_picture' => null
    ];
} else {
    // Ensure email field exists and has a value
    if (empty($user['email'])) {
        $user['email'] = $_SESSION['email'] ?? 'No email available';
    }
}

// Set current_user for compatibility
$current_user = $user;

// Handle profile picture path
$profile_picture = '../assets/uploads/default.jpg';
if (!empty($user['profile_picture'])) {
    if (strpos($user['profile_picture'], 'assets/') === 0) {
        $profile_picture = '../' . $user['profile_picture'];
    } elseif (strpos($user['profile_picture'], '../assets/') === 0) {
        $profile_picture = $user['profile_picture'];
    } else {
        $profile_picture = $user['profile_picture'];
    }
}

// Get leaderboard data - only users with at least 1 completed challenge
$stmt = $pdo->query("
    SELECT
        u.id,
        u.username,
        u.profile_picture,
        COUNT(DISTINCT up.challenge_id) as completed_challenges,
        COUNT(DISTINCT CASE WHEN c.category_id = 1 THEN up.challenge_id END) as sql_completed,
        COUNT(DISTINCT CASE WHEN c.category_id = 2 THEN up.challenge_id END) as xss_completed,
        COUNT(DISTINCT CASE WHEN c.category_id = 3 THEN up.challenge_id END) as cmd_completed,
        MAX(up.completed_at) as last_activity,
        (SELECT COUNT(*) FROM challenges) as total_challenges
    FROM users u
    INNER JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
    LEFT JOIN challenges c ON up.challenge_id = c.id
    GROUP BY u.id, u.username, u.profile_picture
    HAVING completed_challenges > 0
    ORDER BY completed_challenges DESC, last_activity DESC
");
$leaderboard = $stmt->fetchAll();

// Get category totals
$stmt = $pdo->query("
    SELECT
        category_id,
        COUNT(*) as total
    FROM challenges
    GROUP BY category_id
");
$category_totals = [];
while ($row = $stmt->fetch()) {
    $category_totals[$row['category_id']] = $row['total'];
}

// Calculate user rank and check if user has completed challenges
$user_rank = 0;
$user_in_leaderboard = false;
foreach ($leaderboard as $index => $user) {
    if ($user['id'] == $user_id) {
        $user_rank = $index + 1;
        $user_in_leaderboard = true;
        break;
    }
}

// If user is not in leaderboard (0 challenges completed), get their actual status
if (!$user_in_leaderboard) {
    $stmt = $pdo->prepare("SELECT COUNT(DISTINCT challenge_id) as completed FROM user_progress WHERE user_id = ? AND status = 'completed'");
    $stmt->execute([$user_id]);
    $user_completed = $stmt->fetchColumn();

    if ($user_completed == 0) {
        $user_rank = "Not Ranked";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Leaderboard</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Learning Platform Colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --accent-color: #10b981;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;

            /* Neutral Palette */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Learning Platform Theme */
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-bg: rgba(255, 255, 255, 0.98);
            --sidebar-bg: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);

            /* Shadows & Effects */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;

            /* Transitions */
            --transition-fast: all 0.15s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--bg-gradient);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--gray-700);
            line-height: 1.6;
            font-weight: 400;
        }

        /* Modern Learning Sidebar */
        .sidebar {
            width: 300px;
            min-height: 100vh;
            background: var(--sidebar-bg);
            padding: 0;
            color: white;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-2xl);
            backdrop-filter: blur(20px);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
            padding: 32px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .profile-pic {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            border: 4px solid rgba(255,255,255,0.2);
            transition: var(--transition-normal);
        }

        .profile-pic:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(99, 102, 241, 0.4);
        }

        .profile-info h4 {
            font-size: 1.3rem;
            color: white;
            font-weight: 700;
            margin: 15px 0 5px;
        }

        .profile-info p {
            font-size: 0.9rem;
            color: #bdc3c7;
            margin: 0;
            opacity: 0.8;
        }

        /* Modern Learning Navigation */
        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 16px 24px;
            font-size: 0.95rem;
            border-radius: var(--radius-md);
            margin: 4px 16px;
            transition: var(--transition-normal);
            font-weight: 500;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(99, 102, 241, 0.2);
            color: white;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
            font-weight: 600;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: white;
            border-radius: 2px;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Sidebar Footer / Copyright */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 5px;
        }

        .copyright-info {
            text-align: center;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        .copyright-info p {
            margin: 0;
        }

        .main-content {
            margin-left: 300px;
            min-height: 100vh;
            background: var(--gray-50);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(6, 182, 212, 0.03) 100%);
            pointer-events: none;
        }

        .content {
            padding: 24px;
            position: relative;
            z-index: 1;
        }

        /* Compact Learning Cards */
        .leaderboard-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }

        .leaderboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .rank-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 32px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            text-align: center;
            margin-bottom: 32px;
            position: relative;
            overflow: hidden;
        }

        .rank-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--warning-color), var(--primary-color));
        }

        .stats-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            text-align: center;
            transition: var(--transition-normal);
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-light);
        }

        /* Compact Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-item {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: 20px;
            text-align: center;
            border: 1px solid var(--gray-200);
            transition: var(--transition-normal);
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-light);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 1.3rem;
            color: white;
        }

        .stat-icon.users { background: linear-gradient(135deg, var(--info-color), var(--primary-color)); }
        .stat-icon.challenges { background: linear-gradient(135deg, var(--success-color), var(--accent-color)); }
        .stat-icon.average { background: linear-gradient(135deg, var(--warning-color), #f97316); }
        .stat-icon.highest { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

        .stat-number {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* Compact User Rank Card */
        .user-rank-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .user-rank-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .rank-display {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 6px;
        }

        .rank-label {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .user-info {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Compact Leaderboard List */
        .leaderboard-list {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            padding: 24px;
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .leaderboard-list::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .leaderboard-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--gray-100);
        }

        .leaderboard-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
        }

        .leaderboard-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 10px;
            font-size: 1rem;
        }

        /* Compact User Row Styling */
        .user-entry {
            display: flex;
            align-items: center;
            padding: 16px;
            border-radius: var(--radius-md);
            margin-bottom: 10px;
            transition: var(--transition-normal);
            border: 1px solid transparent;
            position: relative;
        }

        .user-entry:hover {
            background: var(--gray-50);
            border-color: var(--gray-200);
            transform: translateX(4px);
        }

        .user-entry.current-user {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.1));
            border-color: var(--primary-light);
        }

        .user-entry.top-3 {
            background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1));
            border-color: var(--warning-color);
        }

        .rank-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1rem;
            margin-right: 12px;
            color: white;
            background: var(--gray-500);
        }

        .rank-number.gold { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333; }
        .rank-number.silver { background: linear-gradient(135deg, #c0c0c0, #e5e5e5); color: #333; }
        .rank-number.bronze { background: linear-gradient(135deg, #cd7f32, #d4a574); color: white; }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 2px solid var(--gray-200);
            margin-right: 12px;
            object-fit: cover;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .user-badges {
            display: flex;
            gap: 6px;
            margin-top: 6px;
        }

        .challenge-badge {
            padding: 3px 6px;
            border-radius: var(--radius-sm);
            font-size: 0.7rem;
            font-weight: 600;
            color: white;
        }

        .challenge-badge.sql { background: var(--info-color); }
        .challenge-badge.xss { background: var(--success-color); }
        .challenge-badge.cmd { background: var(--danger-color); }

        .user-score {
            text-align: right;
        }

        .score-number {
            font-size: 1.3rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 2px;
        }

        .score-label {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-bottom: 6px;
        }

        .progress-bar-container {
            width: 100px;
            height: 5px;
            background: var(--gray-200);
            border-radius: 3px;
            overflow: hidden;
            margin-left: auto;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: 3px;
            transition: width 1s ease;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <div class="profile-info text-center">
                <img src="<?= htmlspecialchars($profile_picture); ?>" alt="Profile Picture" class="profile-pic">
                <h4><?= htmlspecialchars($user['username'] ?? 'Unknown User'); ?></h4>
                <p><?= htmlspecialchars($user['email'] ?? $_SESSION['email'] ?? 'No email'); ?></p>
            </div>
        </div>
        <div class="p-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../utils/start_challenge.php">
                        <i class="fas fa-flag"></i>Challenges
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="leaderboard.php">
                        <i class="fas fa-trophy"></i>Leaderboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#certificates" onclick="scrollToCertificates()">
                        <i class="fas fa-certificate"></i>Certificates
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                </li>
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../admin/dashboard.php">
                        <i class="fas fa-user-shield"></i>Admin Panel
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Copyright Section -->
        <div class="sidebar-footer">
            <div class="copyright-info">
                <p>&copy; <?= date('Y') ?> TryMeOut. All Rights Reserved.</p>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content">
            <!-- Clean Page Header -->
            <div class="leaderboard-card">
                <div class="text-center">
                    <h1 class="display-4 mb-3" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 800;">
                        🏆 Leaderboard
                    </h1>
                    <p class="lead mb-0" style="color: var(--gray-600); font-size: 1.1rem; font-weight: 500;">
                        See how you rank against other security enthusiasts
                    </p>
                </div>
            </div>

            <!-- User Rank Display -->
            <div class="user-rank-card">
                <div class="rank-display">
                    <?= $user_rank === "Not Ranked" ? "?" : "#" . $user_rank ?>
                </div>
                <div class="rank-label">Your Current Rank</div>
                <div class="user-info"><?= htmlspecialchars($current_user['username']) ?></div>
                <?php if ($user_rank === "Not Ranked"): ?>
                    <p class="mt-3 mb-0" style="opacity: 0.9;">Complete your first challenge to appear on the leaderboard!</p>
                <?php else: ?>
                    <p class="mt-3 mb-0" style="opacity: 0.9;">Keep challenging yourself to climb higher!</p>
                <?php endif; ?>
            </div>

            <!-- Clean Statistics -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?= count($leaderboard) ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon challenges">
                        <i class="fas fa-flag-checkered"></i>
                    </div>
                    <div class="stat-number"><?= $leaderboard[0]['total_challenges'] ?? 0 ?></div>
                    <div class="stat-label">Total Challenges</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon average">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number"><?= number_format(array_sum(array_column($leaderboard, 'completed_challenges')) / max(count($leaderboard), 1), 1) ?></div>
                    <div class="stat-label">Average Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon highest">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="stat-number"><?= max(array_column($leaderboard, 'completed_challenges')) ?></div>
                    <div class="stat-label">Highest Score</div>
                </div>
            </div>

            <!-- Clean Leaderboard List -->
            <div class="leaderboard-list">
                <div class="leaderboard-header">
                    <div class="leaderboard-icon">
                        <i class="fas fa-list-ol"></i>
                    </div>
                    <h3 class="leaderboard-title">Rankings</h3>
                </div>

                <?php foreach ($leaderboard as $index => $user_entry): ?>
                    <?php
                    // Fix profile picture path
                    $user_profile_pic = '../assets/uploads/default.jpg';
                    if (!empty($user_entry['profile_picture'])) {
                        if (strpos($user_entry['profile_picture'], 'assets/') === 0) {
                            $user_profile_pic = '../' . $user_entry['profile_picture'];
                        } elseif (strpos($user_entry['profile_picture'], '../assets/') === 0) {
                            $user_profile_pic = $user_entry['profile_picture'];
                        } else {
                            $user_profile_pic = $user_entry['profile_picture'];
                        }
                    }

                    $rank_class = '';
                    if ($index === 0) $rank_class = 'gold';
                    elseif ($index === 1) $rank_class = 'silver';
                    elseif ($index === 2) $rank_class = 'bronze';

                    $entry_class = '';
                    if ($user_entry['id'] == $user_id) $entry_class .= ' current-user';
                    if ($index < 3) $entry_class .= ' top-3';
                    ?>

                    <div class="user-entry<?= $entry_class ?>">
                        <div class="rank-number <?= $rank_class ?>">
                            <?= $index + 1 ?>
                        </div>

                        <img src="<?= htmlspecialchars($user_profile_pic) ?>"
                             alt="Profile" class="user-avatar">

                        <div class="user-details">
                            <div class="user-name">
                                <?= htmlspecialchars($user_entry['username']) ?>
                                <?php if ($user_entry['id'] == $user_id): ?>
                                    <span class="badge bg-primary ms-2">You</span>
                                <?php endif; ?>
                            </div>
                            <div class="user-badges">
                                <span class="challenge-badge sql">
                                    SQL <?= $user_entry['sql_completed'] ?>/<?= $category_totals[1] ?? 0 ?>
                                </span>
                                <span class="challenge-badge xss">
                                    XSS <?= $user_entry['xss_completed'] ?>/<?= $category_totals[2] ?? 0 ?>
                                </span>
                                <span class="challenge-badge cmd">
                                    CMD <?= $user_entry['cmd_completed'] ?>/<?= $category_totals[3] ?? 0 ?>
                                </span>
                            </div>
                        </div>

                        <div class="user-score">
                            <div class="score-number"><?= $user_entry['completed_challenges'] ?></div>
                            <div class="score-label">challenges</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill"
                                     style="width: <?= $user_entry['total_challenges'] > 0 ? round(($user_entry['completed_challenges'] / $user_entry['total_challenges']) * 100) : 0 ?>%">
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (empty($leaderboard)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No users on the leaderboard yet</h5>
                        <p class="text-muted">Be the first to complete a challenge!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function scrollToCertificates() {
            window.location.href = 'dashboard.php#certificates';
        }
    </script>
</body>
</html>
