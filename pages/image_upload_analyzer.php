<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../config/db_connect.php';
require '../config/upload_config.php';

if (!isset($_SESSION['user_id'])) {
    die('Please log in first. <a href="../auth/signin.php">Sign In</a>');
}

$user_id = $_SESSION['user_id'];

echo "<h2>🔍 Image Upload Analyzer</h2>";
echo "<p>This tool will help identify why some images fail to upload.</p>";

// Display current validation rules
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📋 Current Validation Rules:</h3>";
echo "<ul>";
echo "<li><strong>Max file size:</strong> " . formatFileSize(UPLOAD_MAX_SIZE) . "</li>";
echo "<li><strong>Max dimensions:</strong> " . MAX_IMAGE_WIDTH . "x" . MAX_IMAGE_HEIGHT . " pixels</li>";
echo "<li><strong>Allowed extensions:</strong> " . implode(', ', ALLOWED_IMAGE_EXTENSIONS) . "</li>";
echo "<li><strong>Allowed MIME types:</strong> " . implode(', ', ALLOWED_IMAGE_MIME_TYPES) . "</li>";
echo "</ul>";
echo "</div>";

// Handle file analysis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['analyze_file'])) {
    $file = $_FILES['analyze_file'];
    
    echo "<div style='background: #fff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
    echo "<h3>📊 File Analysis Results:</h3>";
    
    // Basic file information
    echo "<h4>Basic Information:</h4>";
    echo "<table style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'><strong>Original filename:</strong></td><td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($file['name']) . "</td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'><strong>File size:</strong></td><td style='border: 1px solid #ddd; padding: 8px;'>" . number_format($file['size']) . " bytes (" . formatFileSize($file['size']) . ")</td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'><strong>Upload error code:</strong></td><td style='border: 1px solid #ddd; padding: 8px;'>" . $file['error'] . "</td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'><strong>Temporary file:</strong></td><td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($file['tmp_name']) . "</td></tr>";
    echo "</table>";
    
    if ($file['error'] === 0 && file_exists($file['tmp_name'])) {
        // File extension analysis
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        echo "<h4>Extension Analysis:</h4>";
        echo "<p><strong>Detected extension:</strong> " . htmlspecialchars($file_extension) . "</p>";
        echo "<p><strong>Extension allowed:</strong> " . (in_array($file_extension, ALLOWED_IMAGE_EXTENSIONS) ? '✅ Yes' : '❌ No') . "</p>";
        
        // MIME type analysis
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        echo "<h4>MIME Type Analysis:</h4>";
        echo "<p><strong>Detected MIME type:</strong> " . htmlspecialchars($mime_type) . "</p>";
        echo "<p><strong>MIME type allowed:</strong> " . (in_array($mime_type, ALLOWED_IMAGE_MIME_TYPES) ? '✅ Yes' : '❌ No') . "</p>";
        
        // Image analysis
        $image_info = getimagesize($file['tmp_name']);
        echo "<h4>Image Analysis:</h4>";
        if ($image_info !== false) {
            echo "<p><strong>Image dimensions:</strong> " . $image_info[0] . "x" . $image_info[1] . " pixels</p>";
            echo "<p><strong>Image type:</strong> " . image_type_to_mime_type($image_info[2]) . "</p>";
            echo "<p><strong>Dimensions within limits:</strong> " . (($image_info[0] <= MAX_IMAGE_WIDTH && $image_info[1] <= MAX_IMAGE_HEIGHT) ? '✅ Yes' : '❌ No') . "</p>";
            
            // Additional image details
            if (isset($image_info['channels'])) {
                echo "<p><strong>Color channels:</strong> " . $image_info['channels'] . "</p>";
            }
            if (isset($image_info['bits'])) {
                echo "<p><strong>Bits per channel:</strong> " . $image_info['bits'] . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ <strong>Not a valid image or corrupted</strong></p>";
        }
        
        // File size validation
        echo "<h4>Size Validation:</h4>";
        echo "<p><strong>File size within limit:</strong> " . (($file['size'] <= UPLOAD_MAX_SIZE) ? '✅ Yes' : '❌ No') . "</p>";
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            echo "<p style='color: red;'>File is " . formatFileSize($file['size'] - UPLOAD_MAX_SIZE) . " over the limit</p>";
        }
        
        // Malicious content check
        echo "<h4>Security Scan:</h4>";
        $content = file_get_contents($file['tmp_name'], false, null, 0, 1024);
        $malicious_found = [];
        foreach (MALICIOUS_PATTERNS as $pattern) {
            if (stripos($content, $pattern) !== false) {
                $malicious_found[] = $pattern;
            }
        }
        
        if (empty($malicious_found)) {
            echo "<p>✅ <strong>No malicious patterns detected</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ <strong>Malicious patterns found:</strong> " . implode(', ', $malicious_found) . "</p>";
        }
        
        // Overall validation result
        echo "<h4>🎯 Overall Validation Result:</h4>";
        $validation_passed = true;
        $issues = [];
        
        if ($file['error'] !== 0) {
            $validation_passed = false;
            $issues[] = "Upload error (code: " . $file['error'] . ")";
        }
        
        if (!in_array($file_extension, ALLOWED_IMAGE_EXTENSIONS)) {
            $validation_passed = false;
            $issues[] = "Invalid file extension";
        }
        
        if (!in_array($mime_type, ALLOWED_IMAGE_MIME_TYPES)) {
            $validation_passed = false;
            $issues[] = "Invalid MIME type";
        }
        
        if ($image_info === false) {
            $validation_passed = false;
            $issues[] = "Not a valid image";
        }
        
        if ($file['size'] > UPLOAD_MAX_SIZE) {
            $validation_passed = false;
            $issues[] = "File too large";
        }
        
        if ($image_info && ($image_info[0] > MAX_IMAGE_WIDTH || $image_info[1] > MAX_IMAGE_HEIGHT)) {
            $validation_passed = false;
            $issues[] = "Image dimensions too large";
        }
        
        if (!empty($malicious_found)) {
            $validation_passed = false;
            $issues[] = "Contains malicious patterns";
        }
        
        if ($validation_passed) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;'>";
            echo "✅ <strong>This image should upload successfully!</strong>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;'>";
            echo "❌ <strong>This image will fail upload due to:</strong><br>";
            echo "• " . implode("<br>• ", $issues);
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ File upload failed or temporary file not accessible</p>";
    }
    
    echo "</div>";
}
?>

<div style="background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3>🧪 Test Your Images</h3>
    <p>Upload any image to see detailed analysis of why it might fail validation:</p>
    
    <form method="POST" enctype="multipart/form-data" style="margin-top: 15px;">
        <div style="margin-bottom: 15px;">
            <label for="analyze_file" style="display: block; margin-bottom: 5px; font-weight: bold;">Select Image to Analyze:</label>
            <input type="file" name="analyze_file" id="analyze_file" accept="image/*" required 
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 100%; max-width: 400px;">
        </div>
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            🔍 Analyze Image
        </button>
    </form>
</div>

<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
    <h4>💡 Common Issues & Solutions:</h4>
    <ul>
        <li><strong>File too large:</strong> Resize image to under 5MB</li>
        <li><strong>Dimensions too large:</strong> Resize to max 2000x2000 pixels</li>
        <li><strong>Invalid format:</strong> Convert to JPG, PNG, or GIF</li>
        <li><strong>Corrupted image:</strong> Re-save the image in an image editor</li>
        <li><strong>MIME type mismatch:</strong> File extension doesn't match actual format</li>
    </ul>
</div>

<p style="margin-top: 20px;">
    <a href="settings.php">← Back to Settings</a> | 
    <a href="test_profile_upload.php">Test Upload</a>
</p>
