<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | About</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.12"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            --accent-color: #6366f1;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 10px 40px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.12);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
        }

        /* Navbar styling (same as other pages) */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar-nav .nav-link {
            color: var(--text-primary) !important;
            font-weight: 500;
            margin: 0 10px;
            padding: 8px 16px !important;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            background: rgba(99, 102, 241, 0.1);
            color: var(--accent-color) !important;
        }

        /* Hero Section */
        .hero-section {
            background: var(--primary-gradient);
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 10;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            font-weight: 400;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Content Sections */
        .content-section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 700px;
            margin: 0 auto;
        }

        /* Content Cards */
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
            margin-bottom: 40px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        /* Feature Cards */
        .feature-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 40px 30px;
            text-align: center;
            border: 2px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
        }

        .feature-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: var(--primary-gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .feature-description {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.6;
        }

        /* Team Cards */
        .team-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            border: 2px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
        }

        .team-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .team-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            font-weight: 700;
        }

        .team-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .team-role {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 15px;
        }

        .team-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* Stats Section */
        .stats-section {
            background: var(--secondary-gradient);
            padding: 80px 0;
        }

        .stat-card {
            text-align: center;
            padding: 30px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .content-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="../index.php">
                <img src="../assets/images/logo-clear.png" alt="TryMeOut" width="40" height="40" class="me-2">
                TryMeOut
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../utils/start_challenge.php">
                            <i class="fas fa-code me-1"></i>Challenges
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../pages/owasp.php">
                            <i class="fas fa-shield-alt me-1"></i>OWASP
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../pages/about.php">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/signin.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Sign In
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span id="typed-text"></span>
                </h1>
                <p class="hero-subtitle">Empowering the next generation of cybersecurity professionals through comprehensive vulnerability education</p>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="content-section">
        <div class="container">
            <div class="section-title">
                <h2>Our Mission</h2>
                <p class="section-subtitle">Empowering cybersecurity education through hands-on vulnerability challenges and interactive learning experiences</p>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="content-card">
                        <p class="mb-4">TryMeOut is dedicated to providing a comprehensive, practical platform for learning cybersecurity concepts. Our goal is to bridge the gap between theoretical knowledge and real-world application by offering hands-on challenges that simulate actual security vulnerabilities.</p>
                        <p class="mb-4">Through our interactive platform, students and professionals can safely explore SQL Injection, Cross-Site Scripting (XSS), and Command Injection vulnerabilities in a controlled environment, gaining valuable experience without the risks associated with testing on live systems.</p>
                        <p class="mb-0">We believe that cybersecurity education should be accessible, engaging, and practical. By combining gamification elements with serious security concepts, we create an environment where learning is both effective and enjoyable.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="content-section" style="background: var(--secondary-gradient);">
        <div class="container">
            <div class="section-title">
                <h2>Platform Features</h2>
                <p class="section-subtitle">Comprehensive tools and resources designed to enhance your cybersecurity learning journey</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <h3 class="feature-title">Interactive Challenges</h3>
                        <p class="feature-description">Solve progressive vulnerability challenges across SQL Injection, XSS, and Command Injection with multiple difficulty levels and unique scenarios.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Progress Tracking</h3>
                        <p class="feature-description">Monitor your learning journey with detailed progress tracking, achievement badges, and completion certificates for each category.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h3 class="feature-title">Guided Learning</h3>
                        <p class="feature-description">Access comprehensive guides, hints, and OWASP-based educational content to understand vulnerabilities and prevention techniques.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">Safe Environment</h3>
                        <p class="feature-description">Practice vulnerability testing in a completely isolated, secure environment without any risk to real systems or data.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">Community Learning</h3>
                        <p class="feature-description">Join a community of learners with leaderboards, shared achievements, and collaborative learning experiences.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h3 class="feature-title">Certification</h3>
                        <p class="feature-description">Earn certificates upon completing challenge categories, validating your cybersecurity skills and knowledge.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="section-title">
                <h2>Platform Statistics</h2>
                <p class="section-subtitle">Join our growing community of cybersecurity learners</p>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">19</div>
                        <div class="stat-label">Total Challenges</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Vulnerability Categories</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">9</div>
                        <div class="stat-label">Difficulty Levels</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Safe Learning</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Target Audience Section -->
    <section class="content-section">
        <div class="container">
            <div class="section-title">
                <h2>Who Should Join?</h2>
                <p class="section-subtitle">Our platform is designed for learners at all levels of cybersecurity expertise</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="team-card">
                        <div class="team-avatar">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="team-name">UTHM BIS Students</h3>
                        <p class="team-role">Primary Target Audience</p>
                        <p class="team-description">Specifically designed for Bachelor of Information Systems students at UTHM, providing practical cybersecurity skills to complement academic learning.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="team-card">
                        <div class="team-avatar">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="team-name">Cybersecurity Students</h3>
                        <p class="team-role">Security Enthusiasts</p>
                        <p class="team-description">Students pursuing cybersecurity education who want hands-on experience with real-world vulnerability scenarios and exploitation techniques.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="team-card">
                        <div class="team-avatar">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="team-name">Developers</h3>
                        <p class="team-role">Security-Conscious Programmers</p>
                        <p class="team-description">Software developers interested in improving their security knowledge and learning how to identify and prevent common web vulnerabilities.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="content-section" style="background: var(--secondary-gradient);">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="content-card">
                        <h2 class="mb-4" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Ready to Start Your Journey?</h2>
                        <p class="mb-4 fs-5">Join TryMeOut today and begin mastering cybersecurity through hands-on vulnerability challenges. Start with our comprehensive learning platform designed specifically for UTHM BIS students and cybersecurity enthusiasts.</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="../auth/signup.php" class="btn btn-lg px-4 py-3" style="background: var(--primary-gradient); color: white; border: none; border-radius: 15px; font-weight: 600; text-decoration: none;">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                            <a href="../utils/start_challenge.php" class="btn btn-lg px-4 py-3" style="background: transparent; color: var(--accent-color); border: 2px solid var(--accent-color); border-radius: 15px; font-weight: 600; text-decoration: none;">
                                <i class="fas fa-code me-2"></i>View Challenges
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--text-primary); color: white; padding: 60px 0 30px;">
        <div class="container">
            <div class="text-center">
                <div style="font-size: 2rem; font-weight: 700; margin-bottom: 20px;">
                    <img src="../assets/images/logo-clear.png" alt="TryMeOut" width="50" height="50" class="me-3">
                    TryMeOut
                </div>
                <p style="font-size: 1.1rem; opacity: 0.8; margin-bottom: 30px; max-width: 500px; margin-left: auto; margin-right: auto;">
                    Empowering the next generation of cybersecurity professionals through hands-on vulnerability challenges and interactive learning experiences.
                </p>
<p style="font-size: 1.1rem; opacity: 0.8; margin-bottom: 30px; max-width: 500px; margin-left: auto; margin-right: auto;">
                    Support: <EMAIL>                </p>

                <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap;">
                    <a href="../index.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Home</a>
                    <a href="../utils/start_challenge.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Challenges</a>
                    <a href="../pages/owasp.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">OWASP</a>
                    <a href="../pages/about.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">About</a>
                    <a href="../auth/signin.php" style="color: white; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">Sign In</a>
                </div>
                <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 30px; opacity: 0.7;">
                    <p>&copy; 2025 TryMeOut. All Rights Reserved. | Built for UTHM BIS Students</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Typed.js animation
        var typed = new Typed('#typed-text', {
            strings: ['About TryMeOut', 'Our Mission', 'Cybersecurity Education'],
            typeSpeed: 80,
            backSpeed: 50,
            backDelay: 2000,
            loop: true,
            showCursor: true,
            cursorChar: '|'
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = '0 10px 40px rgba(0, 0, 0, 0.08)';
            }
        });
    </script>
</body>
</html>
