<?php
session_start();
require '../config/db_connect.php';
require '../config/logger.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/signin.php'); // Redirect to sign-in if user is not logged in
    exit;
}

// Log dashboard access
$logger = getLogger();
$logger->logUserActivity('DASHBOARD_ACCESS', $_SESSION['user_id'], [
    'username' => $_SESSION['username'],
    'role' => $_SESSION['user_role']
]);

// Get user data from the database
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Ensure user data exists and has required fields
if (!$user || empty($user)) {
    // Fallback if user not found
    $user = [
        'id' => $user_id,
        'username' => $_SESSION['username'] ?? 'Unknown User',
        'email' => $_SESSION['email'] ?? 'No email available',
        'profile_picture' => null
    ];
} else {
    // Ensure email field exists and has a value
    if (empty($user['email'])) {
        $user['email'] = $_SESSION['email'] ?? 'No email available';
    }
}

// Get total challenges count
$stmt = $pdo->query("SELECT COUNT(*) FROM challenges");
$total_challenges = $stmt->fetchColumn();

// Get user's completed challenges
$stmt = $pdo->prepare("SELECT COUNT(DISTINCT challenge_id) FROM user_progress WHERE user_id = ? AND status = 'completed'");
$stmt->execute([$user_id]);
$progress = $stmt->fetchColumn();

// Get category progress
$categories = [
    1 => ['name' => 'SQL Injection', 'icon' => '🛡️', 'color' => '#007bff'],
    2 => ['name' => 'XSS', 'icon' => '⚡', 'color' => '#28a745'],
    3 => ['name' => 'Command Injection', 'icon' => '💻', 'color' => '#dc3545']
];

$category_progress = [];
$badges_earned = [];

foreach ($categories as $cat_id => $cat_info) {
    // Get total challenges in category
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM challenges WHERE category_id = ?");
    $stmt->execute([$cat_id]);
    $total_in_category = $stmt->fetchColumn();

    // Get completed challenges in category
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT up.challenge_id)
        FROM user_progress up
        JOIN challenges c ON up.challenge_id = c.id
        WHERE up.user_id = ? AND c.category_id = ? AND up.status = 'completed'
    ");
    $stmt->execute([$user_id, $cat_id]);
    $completed_in_category = $stmt->fetchColumn();

    $category_progress[$cat_id] = [
        'name' => $cat_info['name'],
        'icon' => $cat_info['icon'],
        'color' => $cat_info['color'],
        'completed' => $completed_in_category,
        'total' => $total_in_category,
        'percentage' => $total_in_category > 0 ? round(($completed_in_category / $total_in_category) * 100) : 0
    ];

    // Check if badge earned (100% completion)
    if ($completed_in_category == $total_in_category && $total_in_category > 0) {
        $badges_earned[] = [
            'name' => $cat_info['name'] . ' Master',
            'icon' => $cat_info['icon'],
            'color' => $cat_info['color'],
            'description' => 'Completed all ' . $cat_info['name'] . ' challenges'
        ];
    }
}

// Get user certificates
$stmt = $pdo->prepare("
    SELECT uc.*, c.name as category_name, ct.accent_color
    FROM user_certificates uc
    JOIN categories c ON uc.category_id = c.id
    LEFT JOIN certificate_templates ct ON c.id = ct.category_id AND ct.is_active = 1
    WHERE uc.user_id = ?
    ORDER BY uc.issued_at DESC
");
$stmt->execute([$user_id]);
$user_certificates = $stmt->fetchAll();

// Check certificate eligibility for each category
$certificate_eligibility = [];
foreach ($categories as $cat_id => $cat_info) {
    $completed = $category_progress[$cat_id]['completed'];
    $total = $category_progress[$cat_id]['total'];

    // Check if certificate already exists
    $has_certificate = false;
    foreach ($user_certificates as $cert) {
        if ($cert['category_id'] == $cat_id) {
            $has_certificate = true;
            break;
        }
    }

    $certificate_eligibility[$cat_id] = [
        'eligible' => $completed >= $total && $total > 0,
        'has_certificate' => $has_certificate,
        'completed' => $completed,
        'total' => $total
    ];
}

// Calculate derived values
// Fix profile picture path - handle both relative and absolute paths
if (!empty($user['profile_picture'])) {
    // If the path already starts with assets/, add ../ prefix
    if (strpos($user['profile_picture'], 'assets/') === 0) {
        $profile_picture = '../' . $user['profile_picture'];
    }
    // If it already has ../ prefix, use as is
    elseif (strpos($user['profile_picture'], '../assets/') === 0) {
        $profile_picture = $user['profile_picture'];
    }
    // If it's a full path, use as is
    else {
        $profile_picture = $user['profile_picture'];
    }
} else {
    $profile_picture = '../assets/uploads/default.jpg';
}
$progress_percentage = $total_challenges > 0 ? round(($progress / $total_challenges) * 100) : 0;
$current_level = floor($progress / 3) + 1;
$next_milestone = (floor($progress / 3) + 1) * 3;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TryMeOut | Dashboard</title>
    <link rel="icon" type="image/png" href="../assets/images/logo-clear.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Learning Platform Colors */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --accent-color: #10b981;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;

            /* Neutral Palette */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Learning Platform Theme */
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-bg: rgba(255, 255, 255, 0.98);
            --sidebar-bg: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);

            /* Shadows & Effects */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;

            /* Transitions */
            --transition-fast: all 0.15s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--bg-gradient);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--gray-700);
            line-height: 1.6;
            font-weight: 400;
        }

        /* Modern Learning Sidebar */
        .sidebar {
            width: 300px;
            min-height: 100vh;
            background: var(--sidebar-bg);
            padding: 0;
            color: white;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-2xl);
            backdrop-filter: blur(20px);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
            padding: 32px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            padding: 8px;
        }

        .sidebar-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 12px;
        }

        .main-content {
            margin-left: 300px;
            min-height: 100vh;
            background: var(--gray-50);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(6, 182, 212, 0.03) 100%);
            pointer-events: none;
        }

        /* Modern Learning Cards */
        .stats-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 32px 28px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            height: 100%;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
            border-color: var(--primary-light);
        }

        .stats-card .display-4 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .stats-card h2 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .stats-card .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius-sm);
        }

        .category-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            padding: 28px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            border-left: 4px solid;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .category-card:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .category-card h5 {
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: var(--gray-800);
        }

        .category-card p {
            color: var(--gray-600);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .badge-item {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 25px;
            border-radius: 25px;
            margin: 10px;
            text-align: center;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .badge-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .badge-item:hover::before {
            left: 100%;
        }

        .badge-item:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        /* Consistent Profile Section */
        .profile-pic {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            border: 4px solid rgba(255,255,255,0.2);
            transition: var(--transition-normal);
        }

        .profile-pic:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(99, 102, 241, 0.4);
        }

        .profile-info h4 {
            font-size: 1.3rem;
            color: white;
            font-weight: 700;
            margin: 15px 0 5px;
        }

        .profile-info p {
            font-size: 0.9rem;
            color: #bdc3c7;
            margin: 0;
            opacity: 0.8;
        }

        /* Modern Learning Navigation */
        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 16px 24px;
            font-size: 0.95rem;
            border-radius: var(--radius-md);
            margin: 4px 16px;
            transition: var(--transition-normal);
            font-weight: 500;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(99, 102, 241, 0.2);
            color: white;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
            font-weight: 600;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: white;
            border-radius: 2px;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Sidebar Footer / Copyright */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 5px;
        }

        .copyright-info {
            text-align: center;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            gap: 10px;
        }

        .footer-logo {
            width: 32px;
            height: 32px;
            object-fit: contain;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px;
        }

        .brand-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .copyright-text p {
            margin: 0;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .copyright-text p:first-child {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .version-info {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .version-info small {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
            font-weight: 400;
        }

        /* Content section */
        .content {
            padding: 40px;
        }

        /* Enhanced Progress Bars */
        .progress {
            height: 12px;
            border-radius: 10px;
            background: rgba(0,0,0,0.1);
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-bar {
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Modern Welcome Section */
        .welcome-section {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 40px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            line-height: 1.2;
        }

        .welcome-subtitle {
            color: var(--gray-600);
            font-size: 1.15rem;
            font-weight: 500;
            line-height: 1.5;
        }

        .progress-circle {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
            font-weight: 800;
            box-shadow: var(--shadow-xl);
            position: relative;
        }

        .progress-circle::before {
            content: '';
            position: absolute;
            inset: -4px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            border-radius: 50%;
            z-index: -1;
            opacity: 0.3;
        }

        /* Content Section */
        .content {
            padding: 32px;
            position: relative;
            z-index: 1;
        }

        /* Modern Progress Bars */
        .progress {
            height: 10px;
            border-radius: var(--radius-sm);
            background: var(--gray-200);
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: var(--radius-sm);
            position: relative;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <div class="profile-info text-center">
                <img src="<?= htmlspecialchars($profile_picture); ?>?v=<?= time(); ?>" alt="Profile Picture" class="profile-pic">
                <h4><?= htmlspecialchars($user['username'] ?? 'Unknown User'); ?></h4>
                <p><?= htmlspecialchars($user['email'] ?? $_SESSION['email'] ?? 'No email'); ?></p>
            </div>
        </div>
        <div class="p-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../utils/start_challenge.php">
                        <i class="fas fa-flag"></i>Challenges
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="leaderboard.php">
                        <i class="fas fa-trophy"></i>Leaderboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#certificates" onclick="scrollToCertificates()">
                        <i class="fas fa-certificate"></i>Certificates
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                </li>
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../admin/dashboard.php">
                        <i class="fas fa-user-shield"></i>Admin Panel
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Copyright Section -->
        <div class="sidebar-footer">
            <div class="copyright-info">
                <p>&copy; <?= date('Y') ?> TryMeOut. All Rights Reserved.</p>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content">
                <!-- Enhanced Welcome Section -->
                <div class="welcome-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="welcome-title">Welcome back, <?= htmlspecialchars($user['username']); ?>! 👋</h1>
                            <p class="welcome-subtitle">Ready to tackle some security challenges and level up your skills?</p>
                        </div>
                        <div class="text-end">
                            <div class="progress-circle">
                                <?= $progress_percentage ?>%
                            </div>
                            <small class="text-muted mt-2 d-block">Overall Progress</small>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Statistics Cards -->
                <div class="row mb-5">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card text-center">
                            <div class="display-4 mb-3" style="color: var(--primary-color);">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h2 style="color: var(--primary-color); font-weight: 800;"><?= $progress ?></h2>
                            <p class="text-muted mb-2 fw-bold">Challenges Completed</p>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-arrow-up me-1"></i>Active
                                </span>
                                <small class="text-muted">learning</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card text-center">
                            <div class="display-4 mb-3" style="color: var(--success-color);">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <h2 style="color: var(--success-color); font-weight: 800;"><?= count($user_certificates) ?></h2>
                            <p class="text-muted mb-2 fw-bold">Certificates Earned</p>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-award me-1"></i>Verified
                                </span>
                                <small class="text-muted">authentic</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card text-center">
                            <div class="display-4 mb-3" style="color: var(--warning-color);">
                                <i class="fas fa-level-up-alt"></i>
                            </div>
                            <h2 style="color: var(--warning-color); font-weight: 800;"><?= $current_level ?></h2>
                            <p class="text-muted mb-2 fw-bold">Current Level</p>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge bg-warning me-2">
                                    <i class="fas fa-star me-1"></i>Rising
                                </span>
                                <small class="text-muted">expert</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card text-center">
                            <div class="display-4 mb-3" style="color: var(--info-color);">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h2 style="color: var(--info-color); font-weight: 800;"><?= $next_milestone - $progress ?></h2>
                            <p class="text-muted mb-2 fw-bold">To Next Level</p>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge bg-info me-2">
                                    <i class="fas fa-rocket me-1"></i>Goal
                                </span>
                                <small class="text-muted">ahead</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Progress -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h4 class="mb-3">Category Progress</h4>
                        <?php foreach ($category_progress as $cat_id => $cat_progress): ?>
                        <div class="category-card" style="border-left-color: <?= $cat_progress['color'] ?>;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="me-3" style="font-size: 2rem;"><?= $cat_progress['icon'] ?></div>
                                    <div>
                                        <h5 class="mb-1"><?= $cat_progress['name'] ?></h5>
                                        <p class="text-muted mb-0"><?= $cat_progress['completed'] ?>/<?= $cat_progress['total'] ?> challenges completed</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h4 style="color: <?= $cat_progress['color'] ?>;"><?= $cat_progress['percentage'] ?>%</h4>
                                    <?php if ($cat_progress['percentage'] == 100): ?>
                                        <span class="badge bg-success">Complete!</span>
                                        <?php if ($certificate_eligibility[$cat_id]['has_certificate']): ?>
                                            <br><small class="text-success mt-1"><i class="fas fa-certificate"></i> Certificate Earned</small>
                                        <?php elseif ($certificate_eligibility[$cat_id]['eligible']): ?>
                                            <br><small class="text-warning mt-1"><i class="fas fa-award"></i> Certificate Available</small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="progress mt-3" style="height: 8px;">
                                <div class="progress-bar" style="width: <?= $cat_progress['percentage'] ?>%; background-color: <?= $cat_progress['color'] ?>;"></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="col-md-4">
                        <h4 class="mb-3">Badges Earned 🏆</h4>
                        <div class="stats-card">
                            <?php if (empty($badges_earned)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-medal fa-3x mb-3" style="opacity: 0.3;"></i>
                                    <p>Complete all challenges in a category to earn your first badge!</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($badges_earned as $badge): ?>
                                <div class="badge-item">
                                    <div style="font-size: 1.5rem; margin-bottom: 5px;"><?= $badge['icon'] ?></div>
                                    <div style="font-weight: bold;"><?= $badge['name'] ?></div>
                                    <small style="opacity: 0.8;"><?= $badge['description'] ?></small>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Certificates Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="mb-3">Certificates 🎓</h4>
                        <div class="row">
                            <?php foreach ($categories as $cat_id => $cat_info): ?>
                            <div class="col-md-4 mb-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <div class="mb-3" style="font-size: 3rem; color: <?= $cat_info['color'] ?>;">
                                            <?= $cat_info['icon'] ?>
                                        </div>
                                        <h5><?= $cat_info['name'] ?> Certificate</h5>

                                        <?php if ($certificate_eligibility[$cat_id]['has_certificate']): ?>
                                            <?php
                                            $cert = null;
                                            foreach ($user_certificates as $c) {
                                                if ($c['category_id'] == $cat_id) {
                                                    $cert = $c;
                                                    break;
                                                }
                                            }
                                            ?>
                                            <div class="alert alert-success">
                                                <i class="fas fa-certificate"></i> Certificate Earned!
                                                <br><small>Issued: <?= date('M j, Y', strtotime($cert['issued_at'])) ?></small>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <a href="../utils/view_certificate.php?code=<?= $cert['certificate_code'] ?>" class="btn btn-success">
                                                    <i class="fas fa-eye"></i> View Certificate
                                                </a>
                                                <a href="../utils/view_certificate.php?code=<?= $cert['certificate_code'] ?>" class="btn btn-outline-success btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i> Download PDF
                                                </a>
                                            </div>

                                        <?php elseif ($certificate_eligibility[$cat_id]['eligible']): ?>
                                            <div class="alert alert-warning">
                                                <i class="fas fa-award"></i> Certificate Available!
                                                <br><small>You've completed all challenges</small>
                                            </div>
                                            <form method="POST" action="../utils/certificate_generator.php">
                                                <input type="hidden" name="category_id" value="<?= $cat_id ?>">
                                                <button type="submit" name="generate_certificate" class="btn btn-warning">
                                                    <i class="fas fa-certificate"></i> Generate Certificate
                                                </button>
                                            </form>

                                        <?php else: ?>
                                            <div class="alert alert-light">
                                                <i class="fas fa-lock text-muted"></i> Complete all challenges to unlock
                                                <br><small><?= $certificate_eligibility[$cat_id]['completed'] ?>/<?= $certificate_eligibility[$cat_id]['total'] ?> completed</small>
                                            </div>
                                            <div class="progress mb-3" style="height: 6px;">
                                                <div class="progress-bar" style="width: <?= $category_progress[$cat_id]['percentage'] ?>%; background-color: <?= $cat_info['color'] ?>;"></div>
                                            </div>
                                            <a href="../utils/start_challenge.php" class="btn btn-outline-primary">
                                                <i class="fas fa-play"></i> Continue Challenges
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>

    <script>
        function scrollToCertificates() {
            const certificatesSection = document.querySelector('.row.mb-4:last-of-type');
            if (certificatesSection) {
                certificatesSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Show success message if certificate was generated
        <?php if (isset($_SESSION['certificate_generated'])): ?>
            alert('🎉 Certificate generated successfully! Certificate ID: <?= $_SESSION['certificate_generated'] ?>');
            <?php unset($_SESSION['certificate_generated']); ?>
        <?php endif; ?>

        // Show error message if any
        <?php if (isset($_SESSION['error_message'])): ?>
            alert('❌ Error: <?= $_SESSION['error_message'] ?>');
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>
    </script>
</body>
</html>
